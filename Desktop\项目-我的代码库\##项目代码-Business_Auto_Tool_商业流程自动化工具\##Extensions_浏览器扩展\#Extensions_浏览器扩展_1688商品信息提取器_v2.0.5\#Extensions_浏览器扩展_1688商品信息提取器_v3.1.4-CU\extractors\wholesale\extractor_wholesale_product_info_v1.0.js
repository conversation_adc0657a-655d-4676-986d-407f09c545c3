/**
 * 批发模式商品详细信息抽取器
 * 专门处理1688批发页面的详细商品信息提取
 * 包括发货地、权益、服务、评价等信息
 * <AUTHOR>
 * @version 1.0.0
 */

class WholesaleProductInfoExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_wholesale_product_info_001',
      '批发商品详细信息',
      '提取1688批发页面的发货地、权益、服务等详细信息'
    );
    
    // 抽取器配置
    this.config = {
      timeout: 8000, // 8秒超时
      retryDelay: 1500, // 重试延迟1.5秒
      maxTextLength: 500, // 最大文本长度
      minTextLength: 1 // 最小文本长度
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 发货信息选择器
      shipping: {
        container: ['.发货', '[class*="shipping"]', '[class*="deliver"]'],
        location: ['[class*="发货"]', '[class*="shipping"]', '[class*="location"]'],
        time: ['[class*="时间"]', '[class*="time"]', '[class*="承诺"]']
      },
      
      // 权益信息选择器
      benefits: {
        container: ['.权益', '[class*="benefit"]', '[class*="privilege"]'],
        items: ['.权益 span', '.权益 div', '[class*="benefit"] span']
      },
      
      // 服务信息选择器
      services: {
        container: ['.服务', '[class*="service"]'],
        items: ['.服务 span', '.服务 div', '[class*="service"] span']
      },
      
      // 评价信息选择器
      rating: {
        score: ['.rating-score', '[class*="rating"]', '[class*="score"]'],
        count: ['.rating-count', '[class*="评价"]', '[class*="review"]'],
        stars: ['.stars', '[class*="star"]']
      },
      
      // 成交信息选择器
      sales: {
        container: ['.sales-info', '[class*="成交"]', '[class*="sales"]'],
        count: ['[class*="成交"]', '[class*="件"]']
      }
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的商品详细信息
   */
  async extract() {
    console.log('🚀 [商品详情调试] ========== 开始商品详细信息提取 ==========');
    console.log('🚀 [商品详情调试] 提取器ID:', this.moduleId);
    console.log('🚀 [商品详情调试] 当前页面URL:', window.location.href);
    
    const productInfo = {
      shipping: null,
      benefits: [],
      services: [],
      sales: null,
      attributes: null, // 商品属性信息
      colors: [], // 颜色选项
      sizes: [], // 尺寸选项
      extractedAt: new Date().toISOString()
    };
    
    try {
      // 等待页面加载
      await this.delay(2000);
      
      // 简化的信息提取 - 直接从页面文本中提取
      const pageText = document.body.textContent || '';
      console.log('🔍 [商品详情调试] 页面文本长度:', pageText.length);
      
      // 提取发货信息
      const shippingMatch = pageText.match(/发货\s*([^\s]+)/);
      if (shippingMatch) {
        productInfo.shipping = { location: shippingMatch[1] };
        console.log('✅ [商品详情调试] 找到发货地:', shippingMatch[1]);
      }
      
      // 提取成交信息
      const salesMatch = pageText.match(/一年内(\d+\+?)\s*件成交/);
      if (salesMatch) {
        productInfo.sales = { text: salesMatch[0], count: salesMatch[1] };
        console.log('✅ [商品详情调试] 找到成交信息:', salesMatch[0]);
      }
      
      // 提取商品属性信息（批发页面重点）
      const attributeMatch = pageText.match(/商品属性[：:]*\s*(\d+)\s*项/);
      if (attributeMatch) {
        productInfo.attributes = {
          count: parseInt(attributeMatch[1]),
          text: attributeMatch[0]
        };
        console.log('✅ [商品详情调试] 找到商品属性:', productInfo.attributes);
      }
      
      // 提取颜色信息
      const colorMatch = pageText.match(/颜色[：:]*\s*([^\n]{1,200})/);
      if (colorMatch) {
        const colors = colorMatch[1].split(/[,，]/).map(c => c.trim()).filter(c => c.length > 0);
        if (colors.length > 0) {
          productInfo.colors = colors;
          console.log('✅ [商品详情调试] 找到颜色信息:', colors.length, '种颜色');
        }
      }
      
      // 提取尺寸信息
      const sizeMatch = pageText.match(/尺寸[：:]*\s*([^\n]{1,100})/);
      if (sizeMatch) {
        const sizes = sizeMatch[1].split(/[,，]/).map(s => s.trim()).filter(s => s.length > 0);
        if (sizes.length > 0) {
          productInfo.sizes = sizes;
          console.log('✅ [商品详情调试] 找到尺寸信息:', sizes);
        }
      }
      
      console.log('✅ [商品详情调试] 提取完成:', productInfo);
      
      return {
        success: true,
        data: productInfo,
        confidence: this.calculateConfidence(productInfo),
        extractedAt: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ [商品详情调试] 提取失败:', error);
      // 即使失败也返回成功，避免影响其他提取器
      return {
        success: true,
        data: productInfo,
        confidence: 0,
        error: error.message
      };
    }
  }

  /**
   * 提取发货信息
   * @returns {Promise<Object|null>} 发货信息
   */
  async extractShippingInfo() {
    const shippingInfo = {
      location: null,
      time: null,
      cost: null
    };
    
    try {
      // 等待页面加载完成
      await this.delay(1000);
      
      // 查找发货地信息
      const locationPatterns = [
        /发货\s*([^\s]+)/,
        /送至\s*([^\s]+)/,
        /浙江[^\s]*|北京[^\s]*|上海[^\s]*|广东[^\s]*|江苏[^\s]*/
      ];
      
      const pageText = document.body.textContent || '';
      console.log('🔍 [商品详情调试] 页面文本长度:', pageText.length);
      
      for (const pattern of locationPatterns) {
        const match = pageText.match(pattern);
        if (match) {
          shippingInfo.location = match[1] || match[0];
          console.log('✅ [商品详情调试] 找到发货地:', shippingInfo.location);
          break;
        }
      }
      
      // 查找发货时间
      const timePatterns = [
        /承诺(\d+)小时发货/,
        /(\d+)天内发货/,
        /现货|当天发货|24小时发货/
      ];
      
      for (const pattern of timePatterns) {
        const match = pageText.match(pattern);
        if (match) {
          shippingInfo.time = match[0];
          break;
        }
      }
      
      // 查找运费信息
      const costPatterns = [
        /运费[¥￥]?(\d+(?:\.\d+)?)/,
        /邮费[¥￥]?(\d+(?:\.\d+)?)/
      ];
      
      for (const pattern of costPatterns) {
        const match = pageText.match(pattern);
        if (match) {
          shippingInfo.cost = match[1];
          break;
        }
      }
      
      return Object.values(shippingInfo).some(v => v !== null) ? shippingInfo : null;
      
    } catch (error) {
      console.error('发货信息提取失败:', error);
      return null;
    }
  }

  /**
   * 提取权益信息
   * @returns {Promise<Array>} 权益列表
   */
  async extractBenefitsInfo() {
    const benefits = [];
    
    try {
      const benefitPatterns = [
        /先采后付/,
        /0元下单/,
        /免费试用/,
        /质量保证/,
        /正品保障/
      ];
      
      const pageText = document.body.textContent || '';
      
      for (const pattern of benefitPatterns) {
        const match = pageText.match(pattern);
        if (match) {
          benefits.push({
            name: match[0],
            description: match[0]
          });
        }
      }
      
      return benefits;
      
    } catch (error) {
      console.error('权益信息提取失败:', error);
      return [];
    }
  }

  /**
   * 提取服务信息
   * @returns {Promise<Array>} 服务列表
   */
  async extractServicesInfo() {
    const services = [];
    
    try {
      const servicePatterns = [
        /退货包运费/,
        /7天无理由退货/,
        /晚发必赔/,
        /极速退款/,
        /质量保证/,
        /售后保障/
      ];
      
      const pageText = document.body.textContent || '';
      
      for (const pattern of servicePatterns) {
        const match = pageText.match(pattern);
        if (match) {
          services.push({
            name: match[0],
            description: match[0]
          });
        }
      }
      
      return services;
      
    } catch (error) {
      console.error('服务信息提取失败:', error);
      return [];
    }
  }

  /**
   * 提取评价信息
   * @returns {Promise<Object|null>} 评价信息
   */
  async extractRatingInfo() {
    const ratingInfo = {
      score: null,
      count: null,
      stars: null
    };
    
    try {
      const pageText = document.body.textContent || '';
      
      // 提取评分
      const scoreMatch = pageText.match(/(\d+\.\d+)\s*分?/);
      if (scoreMatch) {
        ratingInfo.score = parseFloat(scoreMatch[1]);
      }
      
      // 提取评价数量
      const countMatch = pageText.match(/(\d+)条?评价/);
      if (countMatch) {
        ratingInfo.count = parseInt(countMatch[1]);
      }
      
      return Object.values(ratingInfo).some(v => v !== null) ? ratingInfo : null;
      
    } catch (error) {
      console.error('评价信息提取失败:', error);
      return null;
    }
  }

  /**
   * 提取成交信息
   * @returns {Promise<Object|null>} 成交信息
   */
  async extractSalesInfo() {
    const salesInfo = {
      count: null,
      period: null,
      text: null
    };
    
    try {
      const pageText = document.body.textContent || '';
      
      // 提取成交信息
      const salesPatterns = [
        /一年内(\d+\+?)\s*件成交/,
        /(\d+\+?)\s*件成交/,
        /月销(\d+\+?)/
      ];
      
      for (const pattern of salesPatterns) {
        const match = pageText.match(pattern);
        if (match) {
          salesInfo.text = match[0];
          salesInfo.count = match[1] || match[0].match(/\d+/)?.[0];
          
          if (match[0].includes('一年内')) {
            salesInfo.period = 'yearly';
          } else if (match[0].includes('月销')) {
            salesInfo.period = 'monthly';
          } else {
            salesInfo.period = 'total';
          }
          break;
        }
      }
      
      return salesInfo.text ? salesInfo : null;
      
    } catch (error) {
      console.error('成交信息提取失败:', error);
      return null;
    }
  }

  /**
   * 计算置信度
   * @param {Object} productInfo - 商品信息
   * @returns {number} 置信度 (0-100)
   */
  calculateConfidence(productInfo) {
    let score = 0;
    let maxScore = 6;
    
    // 发货信息 (15%)
    if (productInfo.shipping && productInfo.shipping.location) {
      score += 1;
    }
    
    // 权益信息 (15%)
    if (productInfo.benefits && productInfo.benefits.length > 0) {
      score += 1;
    }
    
    // 服务信息 (15%)
    if (productInfo.services && productInfo.services.length > 0) {
      score += 1;
    }
    
    // 成交信息 (15%)
    if (productInfo.sales && (productInfo.sales.count || productInfo.sales.text)) {
      score += 1;
    }
    
    // 商品属性信息 (20%) - 批发页面重点
    if (productInfo.attributes && productInfo.attributes.count > 0) {
      score += 1;
    }
    
    // 颜色和尺寸信息 (20%) - 批发页面重点
    if ((productInfo.colors && productInfo.colors.length > 0) || 
        (productInfo.sizes && productInfo.sizes.length > 0)) {
      score += 1;
    }
    
    return Math.round((score / maxScore) * 100);
  }

  /**
   * 验证数据
   * @param {Object} data - 要验证的数据
   * @returns {boolean} 是否有效
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 商品详细信息是辅助信息，即使没有提取到也应该通过验证
    // 避免影响核心功能的正常运行
    return true;
  }

  /**
   * 格式化数据
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    return {
      ...data,
      summary: this.generateSummary(data)
    };
  }

  /**
   * 生成摘要
   * @param {Object} data - 商品信息
   * @returns {string} 摘要文本
   */
  generateSummary(data) {
    const parts = [];
    
    if (data.attributes && data.attributes.count) {
      parts.push(`商品属性: ${data.attributes.count}项`);
    }
    
    if (data.colors && data.colors.length > 0) {
      parts.push(`颜色: ${data.colors.length}种`);
    }
    
    if (data.sizes && data.sizes.length > 0) {
      parts.push(`尺寸: ${data.sizes.length}种`);
    }
    
    if (data.shipping && data.shipping.location) {
      parts.push(`发货地: ${data.shipping.location}`);
    }
    
    if (data.sales && data.sales.count) {
      parts.push(`成交: ${data.sales.count}`);
    }
    
    if (data.benefits && data.benefits.length > 0) {
      parts.push(`权益: ${data.benefits.length}项`);
    }
    
    if (data.services && data.services.length > 0) {
      parts.push(`服务: ${data.services.length}项`);
    }
    
    return parts.length > 0 ? parts.join(', ') : '商品详细信息';
  }

  /**
   * 延迟执行
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 安全的querySelector
   * @param {string} selector - CSS选择器
   * @param {Element} context - 上下文元素
   * @returns {Element|null} 找到的元素
   */
  safeQuerySelector(selector, context = document) {
    try {
      return context.querySelector(selector);
    } catch (error) {
      console.warn('querySelector失败:', selector, error);
      return null;
    }
  }

  /**
   * 安全的querySelectorAll
   * @param {string} selector - CSS选择器
   * @param {Element} context - 上下文元素
   * @returns {NodeList} 找到的元素列表
   */
  safeQuerySelectorAll(selector, context = document) {
    try {
      return context.querySelectorAll(selector) || [];
    } catch (error) {
      console.warn('querySelectorAll失败:', selector, error);
      return [];
    }
  }

  /**
   * 等待元素出现
   * @param {string} selector - CSS选择器
   * @param {number} timeout - 超时时间
   * @returns {Promise<Element|null>} 找到的元素
   */
  async waitForElement(selector, timeout = 5000) {
    return new Promise((resolve) => {
      const element = this.safeQuerySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = this.safeQuerySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        resolve(null);
      }, timeout);
    });
  }
}

// 全局注册
if (typeof window !== 'undefined') {
  window.WholesaleProductInfoExtractor = WholesaleProductInfoExtractor;
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WholesaleProductInfoExtractor;
}