/**
 * Popup JavaScript - 用户界面交互逻辑
 * 处理用户操作、状态更新和数据展示
 * <AUTHOR>
 * @version 1.0.0
 */

(function () {
  'use strict';

  /**
   * Popup应用类
   */
  class PopupApp {
    constructor() {
      this.currentTab = null;
      this.isExtracting = false;
      this.progressData = new Map();
      this.results = new Map();
      this.settings = {
        autoStart: false,
        notificationEnabled: true,
        debugMode: false
      };

      this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
      try {
        // Initializing popup

        // 获取当前标签页
        await this.getCurrentTab();

        // 初始化UI元素
        this.initializeElements();

        // 设置事件监听器
        this.setupEventListeners();

        // 加载初始数据
        await this.loadInitialData();

        // 开始状态轮询
        this.startStatusPolling();

        // Popup initialized successfully

      } catch (error) {
        console.error('Failed to initialize popup:', error);
        this.showError('初始化失败: ' + error.message);
      }
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tabs[0];

      if (!this.currentTab) {
        throw new Error('无法获取当前标签页');
      }

      // 检查是否为1688页面
      if (!this.is1688ProductPage(this.currentTab.url)) {
        this.showError('请在1688商品页面使用此插件');
        this.disableAllButtons();
        return;
      }
    }

    /**
     * 初始化UI元素
     */
    initializeElements() {
      // 获取所有需要的DOM元素
      this.elements = {
        // 状态显示
        pageType: document.getElementById('pageType'),
        extractionStatus: document.getElementById('extractionStatus'),

        // 进度相关
        progressSection: document.getElementById('progressSection'),
        progressContainer: document.getElementById('progressContainer'),
        globalProgressFill: document.getElementById('globalProgressFill'),
        globalProgressText: document.getElementById('globalProgressText'),

        // 结果相关
        resultsSection: document.getElementById('resultsSection'),
        resultsContainer: document.getElementById('resultsContainer'),
        noResults: document.getElementById('noResults'),

        // 按钮
        startBtn: document.getElementById('startBtn'),
        stopBtn: document.getElementById('stopBtn'),
        resetBtn: document.getElementById('resetBtn'),
        viewResultsBtn: document.getElementById('viewResultsBtn'),
        exportBtn: document.getElementById('exportBtn'),
        settingsBtn: document.getElementById('settingsBtn'),

        // 设置面板
        settingsPanel: document.getElementById('settingsPanel'),
        autoStartSetting: document.getElementById('autoStartSetting'),
        notificationSetting: document.getElementById('notificationSetting'),
        debugModeSetting: document.getElementById('debugModeSetting'),
        saveSettingsBtn: document.getElementById('saveSettingsBtn'),
        cancelSettingsBtn: document.getElementById('cancelSettingsBtn'),

        // 消息提示
        errorMessage: document.getElementById('errorMessage'),
        errorText: document.getElementById('errorText'),
        errorClose: document.getElementById('errorClose'),
        successMessage: document.getElementById('successMessage'),
        successText: document.getElementById('successText'),
        successClose: document.getElementById('successClose'),

        // 加载覆盖层
        loadingOverlay: document.getElementById('loadingOverlay')
      };
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
      // 按钮事件
      this.elements.startBtn.addEventListener('click', () => this.startExtraction());
      this.elements.stopBtn.addEventListener('click', () => this.stopExtraction());
      this.elements.resetBtn.addEventListener('click', () => this.resetExtraction());
      this.elements.viewResultsBtn.addEventListener('click', () => this.showResults());
      this.elements.exportBtn.addEventListener('click', () => this.exportData());
      this.elements.settingsBtn.addEventListener('click', () => this.toggleSettings());

      // 设置面板事件
      this.elements.saveSettingsBtn.addEventListener('click', () => this.saveSettings());
      this.elements.cancelSettingsBtn.addEventListener('click', () => this.cancelSettings());

      // 消息关闭事件
      this.elements.errorClose.addEventListener('click', () => this.hideError());
      this.elements.successClose.addEventListener('click', () => this.hideSuccess());

      // 键盘快捷键
      document.addEventListener('keydown', (e) => this.handleKeydown(e));

      // 消息监听
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleMessage(message, sender, sendResponse);
        return true;
      });
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
      try {
        // 获取后台状态
        const response = await chrome.runtime.sendMessage({
          type: 'GET_BACKGROUND_STATE'
        });

        if (response.success && response.data) {
          const data = response.data;

          // 更新页面信息
          if (data.tabData && data.tabData.pageInfo) {
            this.updatePageInfo(data.tabData.pageInfo);
          }

          // 更新状态
          if (data.tabData) {
            this.updateExtractionStatus(data.tabData.status || 'ready');
          }

          // 加载设置
          if (data.settings) {
            this.settings = { ...this.settings, ...data.settings };
            this.updateSettingsUI();
          }

          // 加载进度数据
          if (data.progressData && data.progressData.length > 0) {
            this.updateProgressFromHistory(data.progressData);
          }

          // 加载结果但不自动显示，避免与页面UI冲突
          if (data.tabData && data.tabData.results) {
            // 存储结果但不显示
            this.results.clear();
            for (const [key, value] of Object.entries(data.tabData.results)) {
              this.results.set(key, value);
            }
            // 启用查看结果和导出按钮
            this.elements.viewResultsBtn.disabled = false;
            this.elements.exportBtn.disabled = false;
          }
        }

      } catch (error) {
        console.warn('Failed to load initial data:', error);
        // 不显示错误，因为这可能是正常的（content script未就绪）
      }
    }

    /**
     * 开始状态轮询
     */
    startStatusPolling() {
      // 每2秒检查一次状态
      this.statusInterval = setInterval(async () => {
        try {
          await this.updateStatus();
        } catch (error) {
          console.warn('Status polling error:', error);
        }
      }, 2000);

      // 页面关闭时清理
      window.addEventListener('beforeunload', () => {
        if (this.statusInterval) {
          clearInterval(this.statusInterval);
        }
      });
    }

    /**
     * 更新状态
     */
    async updateStatus() {
      try {
        const response = await chrome.runtime.sendMessage({
          type: 'GET_BACKGROUND_STATE'
        });

        if (response.success && response.data && response.data.tabData) {
          const tabData = response.data.tabData;

          // 更新提取状态
          if (tabData.status) {
            this.updateExtractionStatus(tabData.status);
          }

          // 更新进度
          if (response.data.progressData) {
            this.updateProgressFromHistory(response.data.progressData);
          }
        }
      } catch (error) {
        // 忽略轮询错误
      }
    }

    /**
     * 处理消息
     */
    handleMessage(message, sender, sendResponse) {
      switch (message.type) {
        case 'PROGRESS_EVENT':
          this.handleProgressEvent(message.event);
          break;

        case 'EXTRACTION_COMPLETED':
          this.handleExtractionCompleted(message);
          break;

        case 'EXTRACTION_FAILED':
          this.handleExtractionFailed(message);
          break;

        case 'EXTRACTION_STOPPED':
          this.handleExtractionStopped();
          break;

        case 'EXTRACTION_RESET':
          this.handleExtractionReset();
          break;
      }

      sendResponse({ success: true });
    }

    /**
     * 开始提取
     */
    async startExtraction() {
      if (this.isExtracting) {
        this.showError('提取正在进行中');
        return;
      }

      try {
        this.showLoading(true);
        this.isExtracting = true;
        this.updateButtonStates();

        const response = await chrome.runtime.sendMessage({
          type: 'START_EXTRACTION'
        });

        if (response.success) {
          this.showSuccess('开始提取商品信息...');
          this.updateExtractionStatus('extracting');
        } else {
          throw new Error(response.error || '启动提取失败');
        }

      } catch (error) {
        console.error('Start extraction error:', error);
        this.showError('启动失败: ' + error.message);
        this.isExtracting = false;
        this.updateButtonStates();
      } finally {
        this.showLoading(false);
      }
    }

    /**
     * 停止提取
     */
    async stopExtraction() {
      if (!this.isExtracting) {
        return;
      }

      try {
        this.showLoading(true);

        await chrome.runtime.sendMessage({
          type: 'STOP_EXTRACTION'
        });

        this.showSuccess('已停止提取');

      } catch (error) {
        console.error('Stop extraction error:', error);
        this.showError('停止失败: ' + error.message);
      } finally {
        this.showLoading(false);
      }
    }

    /**
     * 重置提取
     */
    async resetExtraction() {
      try {
        this.showLoading(true);

        // 发送重置消息到content script
        await chrome.tabs.sendMessage(this.currentTab.id, {
          type: 'RESET'
        });

        // 清理本地状态
        this.progressData.clear();
        this.results.clear();
        this.isExtracting = false;

        // 更新UI
        this.updateProgressDisplay();
        this.updateResultsDisplay();
        this.updateButtonStates();
        this.updateExtractionStatus('ready');

        this.showSuccess('已重置提取器');

      } catch (error) {
        console.error('Reset extraction error:', error);
        this.showError('重置失败: ' + error.message);
      } finally {
        this.showLoading(false);
      }
    }

    /**
     * 导出数据
     */
    exportData() {
      if (this.results.size === 0) {
        this.showError('没有可导出的数据');
        return;
      }

      try {
        const data = {
          timestamp: new Date().toISOString(),
          url: this.currentTab.url,
          results: Object.fromEntries(this.results)
        };

        const jsonStr = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `1688_extraction_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);

        this.showSuccess('数据导出成功');

      } catch (error) {
        console.error('Export error:', error);
        this.showError('导出失败: ' + error.message);
      }
    }

    /**
     * 切换设置面板
     */
    toggleSettings() {
      const isVisible = this.elements.settingsPanel.style.display !== 'none';
      this.elements.settingsPanel.style.display = isVisible ? 'none' : 'block';

      if (!isVisible) {
        this.updateSettingsUI();
      }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
      try {
        this.showLoading(true);

        // 收集设置
        const newSettings = {
          autoStart: this.elements.autoStartSetting.checked,
          notificationEnabled: this.elements.notificationSetting.checked,
          debugMode: this.elements.debugModeSetting.checked
        };

        // 发送到background script
        const response = await chrome.runtime.sendMessage({
          type: 'UPDATE_SETTINGS',
          settings: newSettings
        });

        if (response.success) {
          this.settings = { ...this.settings, ...newSettings };
          this.elements.settingsPanel.style.display = 'none';
          this.showSuccess('设置已保存');
        } else {
          throw new Error(response.error || '保存设置失败');
        }

      } catch (error) {
        console.error('Save settings error:', error);
        this.showError('保存失败: ' + error.message);
      } finally {
        this.showLoading(false);
      }
    }

    /**
     * 取消设置
     */
    cancelSettings() {
      this.elements.settingsPanel.style.display = 'none';
      this.updateSettingsUI(); // 恢复原始值
    }

    /**
     * 更新设置UI
     */
    updateSettingsUI() {
      this.elements.autoStartSetting.checked = this.settings.autoStart;
      this.elements.notificationSetting.checked = this.settings.notificationEnabled;
      this.elements.debugModeSetting.checked = this.settings.debugMode;
    }

    /**
     * 处理键盘事件
     */
    handleKeydown(e) {
      // Ctrl/Cmd + Enter: 开始提取
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (!this.isExtracting) {
          this.startExtraction();
        }
      }

      // Escape: 关闭设置面板或停止提取
      if (e.key === 'Escape') {
        if (this.elements.settingsPanel.style.display !== 'none') {
          this.cancelSettings();
        } else if (this.isExtracting) {
          this.stopExtraction();
        }
      }
    }

    /**
     * 处理进度事件
     */
    handleProgressEvent(event) {
      switch (event.type) {
        case 'progressUpdate':
          this.updateTaskProgress(event.data);
          break;

        case 'globalProgress':
          this.updateGlobalProgress(event.data);
          break;

        case 'globalStart':
          this.isExtracting = true;
          this.updateExtractionStatus('extracting');
          this.updateButtonStates();
          break;

        case 'globalCompleted':
        case 'globalFailed':
          this.isExtracting = false;
          this.updateButtonStates();
          break;
      }
    }

    /**
     * 处理提取完成
     */
    handleExtractionCompleted(message) {
      this.isExtracting = false;
      this.updateExtractionStatus('completed');
      this.updateButtonStates();

      // 存储结果但不自动显示，避免与页面UI冲突
      // 用户可以通过点击弹出窗口手动查看结果
      this.results.clear();
      for (const [key, value] of Object.entries(message.results)) {
        this.results.set(key, value);
      }

      // 启用查看结果和导出按钮
      this.elements.viewResultsBtn.disabled = false;
      this.elements.exportBtn.disabled = false;

      this.showSuccess(`提取完成！成功 ${message.stats.success} 项，失败 ${message.stats.failed} 项`);
    }

    /**
     * 处理提取失败
     */
    handleExtractionFailed(message) {
      this.isExtracting = false;
      this.updateExtractionStatus('failed');
      this.updateButtonStates();
      this.showError('提取失败: ' + message.error);
    }

    /**
     * 处理提取停止
     */
    handleExtractionStopped() {
      this.isExtracting = false;
      this.updateExtractionStatus('ready');
      this.updateButtonStates();
    }

    /**
     * 处理提取重置
     */
    handleExtractionReset() {
      this.progressData.clear();
      this.results.clear();
      this.isExtracting = false;
      this.updateProgressDisplay();
      this.updateResultsDisplay();
      this.updateButtonStates();
      this.updateExtractionStatus('ready');
    }

    /**
     * 更新页面信息
     */
    updatePageInfo(pageInfo) {
      const pageTypeElement = this.elements.pageType;
      const iconElement = pageTypeElement.querySelector('.page-type-icon');
      const textElement = pageTypeElement.querySelector('.page-type-text');

      if (pageInfo.pageType === 'wholesale') {
        iconElement.textContent = '🏪';
        textElement.textContent = '批发模式';
      } else if (pageInfo.pageType === 'consign') {
        iconElement.textContent = '📦';
        textElement.textContent = '代发模式';
      } else {
        iconElement.textContent = '❓';
        textElement.textContent = '未知类型';
      }
    }

    /**
     * 更新提取状态
     */
    updateExtractionStatus(status) {
      const statusElement = this.elements.extractionStatus;
      const indicatorElement = statusElement.querySelector('.status-indicator');
      const textElement = statusElement.querySelector('.status-text');

      // 清除所有状态类
      indicatorElement.className = 'status-indicator';

      switch (status) {
        case 'ready':
          indicatorElement.classList.add('ready');
          textElement.textContent = '就绪';
          break;
        case 'extracting':
          indicatorElement.classList.add('extracting');
          textElement.textContent = '提取中';
          break;
        case 'completed':
          indicatorElement.classList.add('completed');
          textElement.textContent = '已完成';
          break;
        case 'failed':
          indicatorElement.classList.add('failed');
          textElement.textContent = '失败';
          break;
        default:
          indicatorElement.classList.add('idle');
          textElement.textContent = '空闲';
      }
    }

    /**
     * 更新任务进度
     */
    updateTaskProgress(taskData) {
      const { taskId, task } = taskData;

      // 存储进度数据
      this.progressData.set(taskId, task);

      // 更新显示
      this.updateProgressDisplay();
    }

    /**
     * 更新全局进度
     */
    updateGlobalProgress(globalData) {
      const { progress } = globalData;

      // 更新全局进度条
      this.elements.globalProgressFill.style.width = `${progress}%`;
      this.elements.globalProgressText.textContent = `${progress}%`;
    }

    /**
     * 从历史数据更新进度
     */
    updateProgressFromHistory(progressHistory) {
      // 获取最新的进度事件
      const latestEvents = new Map();

      progressHistory.forEach(event => {
        if (event.type === 'progressUpdate' && event.data.taskId) {
          latestEvents.set(event.data.taskId, event.data.task);
        } else if (event.type === 'globalProgress') {
          this.updateGlobalProgress(event.data);
        }
      });

      // 更新进度数据
      this.progressData = latestEvents;

      // 更新显示
      this.updateProgressDisplay();
    }

    /**
     * 更新进度显示
     */
    updateProgressDisplay() {
      const container = this.elements.progressContainer;
      container.innerHTML = '';

      if (this.progressData.size === 0) {
        container.innerHTML = '<div class="no-progress">暂无进度信息</div>';
        return;
      }

      // 创建进度项
      for (const [taskId, task] of this.progressData) {
        const progressItem = this.createProgressItem(taskId, task);
        container.appendChild(progressItem);
      }
    }

    /**
     * 创建进度项
     */
    createProgressItem(taskId, task) {
      const item = document.createElement('div');
      item.className = 'progress-item';
      item.innerHTML = `
        <div class="progress-info">
          <div class="progress-name">${task.name}</div>
          <div class="progress-status">${this.getStatusText(task.status)}</div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill ${task.status}" style="width: ${task.progress}%"></div>
        </div>
        <div class="progress-percent">${task.progress}%</div>
      `;

      return item;
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        pending: '等待中',
        processing: '处理中',
        completed: '已完成',
        failed: '失败',
        cancelled: '已取消'
      };

      return statusMap[status] || status;
    }

    /**
     * 更新结果
     */
    updateResults(results) {
      // 存储结果
      this.results.clear();
      for (const [key, value] of Object.entries(results)) {
        this.results.set(key, value);
      }

      // 更新显示
      this.updateResultsDisplay();

      // 启用导出按钮
      this.elements.exportBtn.disabled = false;
    }

    /**
     * 更新结果显示
     */
    updateResultsDisplay() {
      const container = this.elements.resultsContainer;
      const noResults = this.elements.noResults;

      if (this.results.size === 0) {
        noResults.style.display = 'block';
        container.innerHTML = '';
        container.appendChild(noResults);
        return;
      }

      noResults.style.display = 'none';
      container.innerHTML = '';

      // 创建结果项
      for (const [key, result] of this.results) {
        if (result.success && result.data) {
          const resultItem = this.createResultItem(key, result);
          container.appendChild(resultItem);
        }
      }
    }

    /**
     * 创建结果项
     */
    createResultItem(key, result) {
      const item = document.createElement('div');
      item.className = 'result-item';

      const confidence = result.data.confidence || 0;
      const confidenceClass = confidence >= 80 ? 'high' : confidence >= 60 ? 'medium' : 'low';

      item.innerHTML = `
        <div class="result-header">
          <div class="result-name">${result.name}</div>
          <div class="result-confidence ${confidenceClass}">${confidence}%</div>
        </div>
        <div class="result-content">
          <div class="result-value">${this.formatResultValue(result.data)}</div>
        </div>
      `;

      return item;
    }

    /**
     * 格式化结果值
     */
    formatResultValue(data) {
      if (data.name) {
        return data.name;
      }

      if (typeof data === 'string') {
        return data;
      }

      return JSON.stringify(data, null, 2);
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
      this.elements.startBtn.disabled = this.isExtracting;
      this.elements.stopBtn.disabled = !this.isExtracting;
      this.elements.resetBtn.disabled = this.isExtracting;
    }

    /**
     * 禁用所有按钮
     */
    disableAllButtons() {
      this.elements.startBtn.disabled = true;
      this.elements.stopBtn.disabled = true;
      this.elements.resetBtn.disabled = true;
      this.elements.exportBtn.disabled = true;
    }

    /**
     * 显示错误消息
     */
    showError(message) {
      this.elements.errorText.textContent = message;
      this.elements.errorMessage.style.display = 'flex';

      // 3秒后自动隐藏
      setTimeout(() => this.hideError(), 3000);
    }

    /**
     * 隐藏错误消息
     */
    hideError() {
      this.elements.errorMessage.style.display = 'none';
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
      this.elements.successText.textContent = message;
      this.elements.successMessage.style.display = 'flex';

      // 2秒后自动隐藏
      setTimeout(() => this.hideSuccess(), 2000);
    }

    /**
     * 隐藏成功消息
     */
    hideSuccess() {
      this.elements.successMessage.style.display = 'none';
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
      this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    /**
     * 检查是否为1688商品页面
     */
    is1688ProductPage(url) {
      if (!url) return false;

      try {
        const urlObj = new URL(url);
        return urlObj.hostname.includes('1688.com') &&
          urlObj.pathname.includes('/offer/') &&
          urlObj.pathname.includes('.html');
      } catch (error) {
        return false;
      }
    }
  }

  // 页面加载完成后初始化
  document.addEventListener('DOMContentLoaded', () => {
    window.popupApp = new PopupApp();
  });

})();