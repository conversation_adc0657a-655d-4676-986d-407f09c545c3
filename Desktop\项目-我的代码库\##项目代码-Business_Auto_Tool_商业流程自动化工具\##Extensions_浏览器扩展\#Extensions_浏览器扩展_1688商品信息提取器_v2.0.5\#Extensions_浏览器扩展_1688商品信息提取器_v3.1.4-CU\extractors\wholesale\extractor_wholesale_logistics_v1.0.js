/**
 * 物流信息抽取器 - 批发模式专用
 * 提取商品的物流配送信息，包括发货地、目的地、运费等
 * 基于实际DOM结构：.module-od-shipping-services
 * <AUTHOR>
 * @version 1.0.0
 */

class WholesaleLogisticsExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_wholesale_logistics_001',
      '批发物流信息',
      '提取1688批发模式页面的物流配送信息，包括发货地、目的地、运费等'
    );
    
    // 抽取器配置
    this.config = {
      timeout: 8000, // 8秒超时
      retryDelay: 1500, // 重试延迟1.5秒
      maxRetries: 3 // 最大重试次数
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置对象
   */
  getSelectors() {
    return {
      // 主要选择器 - 基于实际DOM结构
      primary: {
        // 物流信息容器 - 2024年最新结构
        logisticsContainer: [
          '.module-od-shipping-services', // 主要物流服务模块（最高优先级）
          '.cart-gap', // 购物车间隙容器
          '.shipping-services', // 配送服务
          '.logistics-module', // 物流模块
          '.delivery-info', // 配送信息
          '.shipping-info', // 运输信息
          // 备用选择器
          '[class*="shipping"]', // 包含shipping的类
          '[class*="logistics"]', // 包含logistics的类
          '[class*="delivery"]', // 包含delivery的类
          'div[class*="od-shipping"]' // 包含od-shipping的div
        ],
        
        // 发货地选择器 - 基于实际结构
        originCity: [
          '.module-od-shipping-services .location', // 精确路径：物流模块中的位置（最高优先级）
          '.cart-content .location', // 购物车内容中的位置
          'span.location', // span标签的位置
          '.origin-location', // 发货位置
          '.from-location', // 来源位置
          '.sender-location', // 发件人位置
          // 通用选择器
          'span[class*="location"]', // 包含location的span
          'div[class*="location"]', // 包含location的div
          '[class*="origin"]', // 包含origin的类
          '[class*="from"]' // 包含from的类
        ],
        
        // 目的地选择器 - 基于实际结构
        destinationCity: [
          '.module-od-shipping-services .recieve-address', // 精确路径：收货地址（最高优先级）
          '.cart-content .recieve-address', // 购物车内容中的收货地址
          'a.recieve-address', // a标签的收货地址
          '.receive-address', // 收货地址（拼写变体）
          '.delivery-address', // 配送地址
          '.destination-address', // 目的地地址
          '.to-address', // 送达地址
          // 通用选择器
          '[class*="recieve"]', // 包含recieve的类
          '[class*="receive"]', // 包含receive的类
          '[class*="destination"]', // 包含destination的类
          '[class*="address"]' // 包含address的类
        ],
        
        // 运费选择器 - 基于实际结构
        shippingFee: [
          '.module-od-shipping-services .service-item:contains("运费")', // 包含运费的服务项（最高优先级）
          '.cart-content .service-item', // 购物车内容中的服务项
          'span.service-item', // span标签的服务项
          '.shipping-fee', // 运费
          '.delivery-fee', // 配送费
          '.freight-cost', // 运费成本
          '.logistics-cost', // 物流成本
          // 通用选择器
          'span[class*="service"]', // 包含service的span
          'span[class*="fee"]', // 包含fee的span
          'span[class*="cost"]', // 包含cost的span
          '[class*="freight"]' // 包含freight的类
        ],
        
        // 配送承诺选择器 - 基于实际结构
        deliveryPromise: [
          '.module-od-shipping-services .delivery-limit', // 配送限制（最高优先级）
          '.cart-content .delivery-limit', // 购物车内容中的配送限制
          'a.delivery-limit', // a标签的配送限制
          '.delivery-promise', // 配送承诺
          '.shipping-promise', // 运输承诺
          '.logistics-promise', // 物流承诺
          // 通用选择器
          '[class*="delivery-limit"]', // 包含delivery-limit的类
          '[class*="promise"]', // 包含promise的类
          '[class*="commitment"]' // 包含commitment的类
        ]
      },
      
      // 备用选择器
      fallback: {
        // 通用物流信息
        logisticsInfo: [
          'div[class*="shipping"]', // 包含shipping的div
          'div[class*="delivery"]', // 包含delivery的div
          'div[class*="logistics"]', // 包含logistics的div
          '.od-text[i18n="logistic"]', // 物流国际化文本
          'span:contains("发货")', // 包含发货的span
          'span:contains("送至")', // 包含送至的span
          'span:contains("运费")' // 包含运费的span
        ]
      }
    };
  }

  /**
   * 执行物流信息提取
   * @returns {Promise<Object>} 提取结果
   */
  async extract() {
    try {
      console.log('🚚 [WholesaleLogisticsExtractor] 开始提取批发物流信息...');
      
      // 添加延迟等待DOM完全加载
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const selectors = this.getSelectors();
      
      // 查找物流信息容器
      const logisticsContainer = await this.findLogisticsContainer(selectors);
      
      // 提取物流信息
      const logisticsInfo = await this.extractLogisticsInfo(selectors, logisticsContainer);
      
      // 增强和格式化数据
      const enhancedInfo = this.enhanceLogisticsInfo(logisticsInfo);
      const formattedInfo = this.formatLogisticsInfo(enhancedInfo);
      
      console.log('🎯 [WholesaleLogisticsExtractor] 提取完成:', formattedInfo);
      
      return {
        success: true,
        data: formattedInfo,
        confidence: this.calculateConfidence(formattedInfo),
        extractedAt: new Date().toISOString(),
        source: 'WholesaleLogisticsExtractor'
      };
      
    } catch (error) {
      console.error('❌ [WholesaleLogisticsExtractor] 物流信息提取失败:', error);
      return this.createErrorResult(error.message);
    }
  }
  
  /**
   * 查找物流信息容器（带重试机制）
   * @param {Object} selectors 选择器配置
   * @returns {Element|null} 物流信息容器元素
   */
  async findLogisticsContainer(selectors) {
    console.log('🔍 [WholesaleLogisticsExtractor] 查找物流容器...');
    
    // 重试机制：最多尝试3次
    for (let attempt = 0; attempt < 3; attempt++) {
      // 尝试主要选择器
      for (const selector of selectors.primary.logisticsContainer) {
        try {
          const element = this.safeQuerySelector(selector);
          if (element && element.textContent.trim()) {
            console.log(`✅ [WholesaleLogisticsExtractor] 物流容器找到 (尝试${attempt + 1}):`, selector);
            console.log('📋 [WholesaleLogisticsExtractor] 容器内容预览:', element.textContent.trim().substring(0, 200));
            return element;
          }
        } catch (e) {
          console.log('⚠️ [WholesaleLogisticsExtractor] 容器选择器错误:', selector, e.message);
        }
      }
      
      // 尝试备用选择器
      for (const selector of selectors.fallback.logisticsInfo) {
        try {
          const element = this.safeQuerySelector(selector);
          if (element && element.textContent.trim()) {
            console.log(`✅ [WholesaleLogisticsExtractor] 物流容器找到 (备用, 尝试${attempt + 1}):`, selector);
            return element;
          }
        } catch (e) {
          console.log('⚠️ [WholesaleLogisticsExtractor] 备用选择器错误:', selector, e.message);
        }
      }
      
      // 如果没找到，等待500ms后重试
      if (attempt < 2) {
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log(`🔄 [WholesaleLogisticsExtractor] 物流容器未找到，等待重试 (${attempt + 1}/3)`);
      }
    }
    
    console.log('❌ [WholesaleLogisticsExtractor] 物流容器最终未找到');
    return null;
  }
  
  /**
   * 提取物流信息
   * @param {Object} selectors 选择器配置
   * @param {Element} container 物流信息容器
   * @returns {Promise<Object>} 物流信息对象
   */
  async extractLogisticsInfo(selectors, container) {
    const logisticsInfo = {
      originCity: '', // 发货地
      destinationCity: '', // 目的地
      shippingFee: '', // 运费
      deliveryPromise: '', // 配送承诺
      isFreeship: false, // 是否包邮
      rawData: {} // 原始数据
    };
    
    console.log('📊 [WholesaleLogisticsExtractor] 开始提取各项物流信息...');
    
    // 提取发货地
    logisticsInfo.originCity = this.extractOriginCity(selectors, container);
    
    // 提取目的地
    logisticsInfo.destinationCity = this.extractDestinationCity(selectors, container);
    
    // 提取运费
    const feeInfo = this.extractShippingFee(selectors, container);
    logisticsInfo.shippingFee = feeInfo.fee;
    logisticsInfo.isFreeship = feeInfo.isFreeship;
    
    // 提取配送承诺
    logisticsInfo.deliveryPromise = this.extractDeliveryPromise(selectors, container);
    
    // 保存原始数据
    logisticsInfo.rawData = {
      containerHTML: container ? container.outerHTML.substring(0, 500) : 'No container found',
      containerText: container ? container.textContent.trim().substring(0, 200) : 'No container text'
    };
    
    // 如果所有字段都为空，尝试智能文本匹配
    if (!logisticsInfo.originCity && !logisticsInfo.destinationCity && !logisticsInfo.shippingFee) {
      console.log('🤖 [WholesaleLogisticsExtractor] 尝试智能文本匹配...');
      const textMatchResult = this.performIntelligentTextMatching();
      
      if (textMatchResult.originCity) {
        logisticsInfo.originCity = textMatchResult.originCity;
        console.log('✅ [WholesaleLogisticsExtractor] 智能匹配找到发货地:', textMatchResult.originCity);
      }
      
      if (textMatchResult.destinationCity) {
        logisticsInfo.destinationCity = textMatchResult.destinationCity;
        console.log('✅ [WholesaleLogisticsExtractor] 智能匹配找到目的地:', textMatchResult.destinationCity);
      }
      
      if (textMatchResult.shippingFee) {
        logisticsInfo.shippingFee = textMatchResult.shippingFee;
        console.log('✅ [WholesaleLogisticsExtractor] 智能匹配找到运费:', textMatchResult.shippingFee);
      }
    }
    
    console.log('📋 [WholesaleLogisticsExtractor] 最终物流信息:', logisticsInfo);
    return logisticsInfo;
  }
  
  /**
   * 提取发货地
   * @param {Object} selectors 选择器配置
   * @param {Element} container 容器元素
   * @returns {string} 发货地
   */
  extractOriginCity(selectors, container) {
    console.log('📍 [WholesaleLogisticsExtractor] 提取发货地...');
    
    // 先尝试在容器内查找，再尝试全局查找
    const searchContexts = container ? [container, document] : [document];
    
    for (const context of searchContexts) {
      for (const selector of selectors.primary.originCity) {
        const element = this.safeQuerySelector(selector, context);
        if (element) {
          const text = this.getElementText(element).trim();
          if (text && text.length > 0 && text.length < 20) {
            console.log('✅ [WholesaleLogisticsExtractor] 发货地找到:', text, 'selector:', selector);
            return text;
          }
        }
      }
    }
    
    console.log('❌ [WholesaleLogisticsExtractor] 发货地未找到');
    return '';
  }
  
  /**
   * 提取目的地
   * @param {Object} selectors 选择器配置
   * @param {Element} container 容器元素
   * @returns {string} 目的地
   */
  extractDestinationCity(selectors, container) {
    console.log('🎯 [WholesaleLogisticsExtractor] 提取目的地...');
    
    // 先尝试在容器内查找，再尝试全局查找
    const searchContexts = container ? [container, document] : [document];
    
    for (const context of searchContexts) {
      for (const selector of selectors.primary.destinationCity) {
        const element = this.safeQuerySelector(selector, context);
        if (element) {
          let text = '';
          
          // 优先获取title属性
          if (element.hasAttribute('title')) {
            text = element.getAttribute('title').trim();
          }
          
          // 如果没有title，获取文本内容
          if (!text) {
            text = this.getElementText(element).trim();
          }
          
          if (text && text.length > 0 && text.length < 20) {
            console.log('✅ [WholesaleLogisticsExtractor] 目的地找到:', text, 'selector:', selector);
            return text;
          }
        }
      }
    }
    
    console.log('❌ [WholesaleLogisticsExtractor] 目的地未找到');
    return '';
  }
  
  /**
   * 提取运费信息
   * @param {Object} selectors 选择器配置
   * @param {Element} container 容器元素
   * @returns {Object} 运费信息对象
   */
  extractShippingFee(selectors, container) {
    console.log('💰 [WholesaleLogisticsExtractor] 提取运费信息...');
    
    const feeInfo = {
      fee: '',
      isFreeship: false
    };
    
    // 先尝试在容器内查找，再尝试全局查找
    const searchContexts = container ? [container, document] : [document];
    
    // 尝试提取运费
    for (const context of searchContexts) {
      for (const selector of selectors.primary.shippingFee) {
        const element = this.safeQuerySelector(selector, context);
        if (element) {
          const text = this.getElementText(element).trim();
          if (text && (text.includes('¥') || text.includes('运费') || text.includes('免费'))) {
            feeInfo.fee = text;
            
            // 检查是否包邮
            const lowerText = text.toLowerCase();
            if (lowerText.includes('免费') || lowerText.includes('包邮') || 
                lowerText.includes('free') || text.includes('¥0')) {
              feeInfo.isFreeship = true;
            }
            
            console.log('✅ [WholesaleLogisticsExtractor] 运费找到:', text, 'selector:', selector, 'isFreeship:', feeInfo.isFreeship);
            return feeInfo;
          }
        }
      }
    }
    
    console.log('❌ [WholesaleLogisticsExtractor] 运费未找到');
    return feeInfo;
  }
  
  /**
   * 提取配送承诺
   * @param {Object} selectors 选择器配置
   * @param {Element} container 容器元素
   * @returns {string} 配送承诺
   */
  extractDeliveryPromise(selectors, container) {
    console.log('⏰ [WholesaleLogisticsExtractor] 提取配送承诺...');
    
    for (const selector of selectors.primary.deliveryPromise) {
      const element = this.safeQuerySelector(selector, container) || this.safeQuerySelector(selector);
      if (element) {
        const text = this.getElementText(element).trim();
        if (text && text.length > 0 && text.length < 100) {
          console.log('✅ [WholesaleLogisticsExtractor] 配送承诺找到:', text, 'selector:', selector);
          return text;
        }
      }
    }
    
    console.log('❌ [WholesaleLogisticsExtractor] 配送承诺未找到');
    return '';
  }
  
  /**
   * 执行智能文本匹配
   * @returns {Object} 匹配结果
   */
  performIntelligentTextMatching() {
    const result = {
      originCity: '',
      destinationCity: '',
      shippingFee: ''
    };

    try {
      console.log('🤖 [WholesaleLogisticsExtractor] 执行智能文本匹配...');
      
      // 查找所有可能包含物流信息的元素
      const allElements = document.querySelectorAll('span, div, a, em');
      
      // 发货地匹配模式
      const originPatterns = [
        /^(浙江金华|广东东莞|浙江绍兴|广东|浙江|江苏|山东|河北|福建)$/,
        /^(浙江\s*金华|广东\s*东莞|浙江\s*绍兴)$/,
        /(浙江|广东|江苏|山东|河北|福建)[\s\u4e00-\u9fa5]{0,10}(市|区|县)/
      ];
      
      // 目的地匹配模式
      const destinationPatterns = [
        /^(北京朝阳|上海浦东|深圳南山|广州天河)$/,
        /(北京|上海|深圳|广州|杭州)[\s\u4e00-\u9fa5]{0,10}(区|市)/
      ];
      
      // 运费匹配模式
      const feePatterns = [
        /运费¥[\d.,]+起?/,
        /¥[\d.,]+起/,
        /[\d.,]+元起?/,
        /免费|包邮/,
        /¥[\d.,]+/
      ];

      for (const element of allElements) {
        const text = element.textContent.trim();
        const title = element.getAttribute('title') || '';
        
        // 匹配发货地
        if (!result.originCity) {
          for (const pattern of originPatterns) {
            if (pattern.test(text) || pattern.test(title)) {
              result.originCity = text || title;
              console.log('🎯 [WholesaleLogisticsExtractor] 智能匹配发货地:', result.originCity);
              break;
            }
          }
        }
        
        // 匹配目的地
        if (!result.destinationCity) {
          for (const pattern of destinationPatterns) {
            if (pattern.test(text) || pattern.test(title)) {
              result.destinationCity = text || title;
              console.log('🎯 [WholesaleLogisticsExtractor] 智能匹配目的地:', result.destinationCity);
              break;
            }
          }
        }
        
        // 匹配运费
        if (!result.shippingFee) {
          for (const pattern of feePatterns) {
            if (pattern.test(text)) {
              result.shippingFee = text;
              console.log('🎯 [WholesaleLogisticsExtractor] 智能匹配运费:', result.shippingFee);
              break;
            }
          }
        }
        
        // 如果所有信息都找到了，提前退出
        if (result.originCity && result.destinationCity && result.shippingFee) {
          break;
        }
      }
      
      console.log('📊 [WholesaleLogisticsExtractor] 智能文本匹配结果:', result);
      
    } catch (error) {
      console.error('❌ [WholesaleLogisticsExtractor] 智能文本匹配过程中出现错误:', error);
    }
    
    return result;
  }
  
  /**
   * 增强物流信息
   * @param {Object} logisticsInfo 原始物流信息
   * @returns {Object} 增强后的物流信息
   */
  enhanceLogisticsInfo(logisticsInfo) {
    const enhanced = { ...logisticsInfo };
    
    // 标准化发货地格式
    if (enhanced.originCity) {
      enhanced.originCity = this.standardizeCityName(enhanced.originCity);
    }
    
    // 标准化目的地格式
    if (enhanced.destinationCity) {
      enhanced.destinationCity = this.standardizeCityName(enhanced.destinationCity);
    }
    
    // 解析运费数值
    if (enhanced.shippingFee) {
      enhanced.shippingFeeAmount = this.parseShippingFeeAmount(enhanced.shippingFee);
    }
    
    // 添加物流路线
    if (enhanced.originCity && enhanced.destinationCity) {
      enhanced.logisticsRoute = `${enhanced.originCity} → ${enhanced.destinationCity}`;
    }
    
    return enhanced;
  }
  
  /**
   * 标准化城市名称
   * @param {string} cityName 城市名称
   * @returns {string} 标准化后的城市名称
   */
  standardizeCityName(cityName) {
    if (!cityName) return '';
    
    // 移除多余的空格和特殊字符
    let standardized = cityName.trim().replace(/\s+/g, '');
    
    // 处理常见的城市名称格式
    standardized = standardized.replace(/市$/, ''); // 移除末尾的"市"
    standardized = standardized.replace(/区$/, '区'); // 保留区
    
    return standardized;
  }
  
  /**
   * 解析运费金额
   * @param {string} feeText 运费文本
   * @returns {number|null} 运费金额
   */
  parseShippingFeeAmount(feeText) {
    if (!feeText) return null;
    
    // 提取数字
    const match = feeText.match(/¥?([\d.,]+)/);
    if (match) {
      const amount = parseFloat(match[1].replace(/,/g, ''));
      return isNaN(amount) ? null : amount;
    }
    
    return null;
  }
  
  /**
   * 格式化物流信息
   * @param {Object} logisticsInfo 物流信息
   * @returns {Object} 格式化后的物流信息
   */
  formatLogisticsInfo(logisticsInfo) {
    return {
      ...logisticsInfo,
      extractedFields: this.getExtractedFieldsCount(logisticsInfo),
      dataCompleteness: this.calculateDataCompleteness(logisticsInfo),
      extractorType: 'wholesale',
      timestamp: Date.now()
    };
  }
  
  /**
   * 获取已提取字段数量
   * @param {Object} logisticsInfo 物流信息
   * @returns {number} 已提取字段数量
   */
  getExtractedFieldsCount(logisticsInfo) {
    const fields = ['originCity', 'destinationCity', 'shippingFee', 'deliveryPromise'];
    return fields.filter(field => logisticsInfo[field] && logisticsInfo[field].trim()).length;
  }
  
  /**
   * 计算数据完整性
   * @param {Object} logisticsInfo 物流信息
   * @returns {number} 数据完整性百分比
   */
  calculateDataCompleteness(logisticsInfo) {
    const totalFields = 4; // originCity, destinationCity, shippingFee, deliveryPromise
    const extractedFields = this.getExtractedFieldsCount(logisticsInfo);
    return Math.round((extractedFields / totalFields) * 100);
  }
  
  /**
   * 计算置信度
   * @param {Object} logisticsInfo 物流信息
   * @returns {number} 置信度百分比
   */
  calculateConfidence(logisticsInfo) {
    let confidence = 0;
    
    // 基础分数
    if (logisticsInfo.originCity) confidence += 30;
    if (logisticsInfo.destinationCity) confidence += 30;
    if (logisticsInfo.shippingFee) confidence += 25;
    if (logisticsInfo.deliveryPromise) confidence += 15;
    
    // 数据质量加分
    const dataCompleteness = this.calculateDataCompleteness(logisticsInfo);
    if (dataCompleteness >= 75) confidence += 10;
    else if (dataCompleteness >= 50) confidence += 5;
    
    return Math.min(confidence, 100);
  }
  
  /**
   * 创建空结果
   * @param {string} reason 原因
   * @returns {Object} 空结果对象
   */
  createEmptyResult(reason) {
    return {
      success: false,
      data: {
        originCity: '',
        destinationCity: '',
        shippingFee: '',
        deliveryPromise: '',
        isFreeship: false,
        extractedFields: 0,
        dataCompleteness: 0,
        extractorType: 'wholesale',
        timestamp: Date.now()
      },
      confidence: 0,
      error: reason,
      extractedAt: new Date().toISOString(),
      source: 'WholesaleLogisticsExtractor'
    };
  }
  
  /**
   * 创建错误结果
   * @param {string} errorMessage 错误信息
   * @returns {Object} 错误结果对象
   */
  createErrorResult(errorMessage) {
    return {
      success: false,
      data: null,
      confidence: 0,
      error: errorMessage,
      extractedAt: new Date().toISOString(),
      source: 'WholesaleLogisticsExtractor'
    };
  }
  
  /**
   * 安全的查询选择器
   * @param {string} selector CSS选择器
   * @param {Element} context 查询上下文
   * @returns {Element|null} 查询结果
   */
  safeQuerySelector(selector, context = document) {
    try {
      return context.querySelector(selector);
    } catch (error) {
      console.warn(`[WholesaleLogisticsExtractor] 选择器错误: ${selector}`, error);
      return null;
    }
  }
  
  /**
   * 获取元素文本内容
   * @param {Element} element DOM元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    if (!element) return '';
    
    try {
      return element.textContent || element.innerText || '';
    } catch (error) {
      console.warn('[WholesaleLogisticsExtractor] 获取元素文本失败:', error);
      return '';
    }
  }
  
  /**
   * 验证提取结果
   * @param {Object} data 提取的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    console.log('🔍 [WholesaleLogisticsExtractor] 验证数据:', data);
    
    if (!data || typeof data !== 'object') {
      console.log('❌ [WholesaleLogisticsExtractor] 数据为空或类型错误');
      return false;
    }
    
    // 检查是否有success字段且为true
    if (data.success === false) {
      console.log('❌ [WholesaleLogisticsExtractor] 提取失败，success为false');
      return false;
    }
    
    // 检查data字段中是否有有效内容
    const actualData = data.data || data;
    
    // 至少要有一个有效字段
    const hasValidField = actualData.originCity || actualData.destinationCity || 
                         actualData.shippingFee || actualData.deliveryPromise;
    
    console.log('📊 [WholesaleLogisticsExtractor] 验证结果:', {
      hasValidField,
      originCity: actualData.originCity,
      destinationCity: actualData.destinationCity,
      shippingFee: actualData.shippingFee,
      deliveryPromise: actualData.deliveryPromise
    });
    
    return hasValidField;
  }
  
  /**
   * 格式化输出数据
   * @param {Object} data 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    console.log('📋 [WholesaleLogisticsExtractor] 格式化数据:', data);
    
    if (!this.validate(data)) {
      console.log('❌ [WholesaleLogisticsExtractor] 数据验证失败，返回空结果');
      return this.createEmptyResult('数据验证失败');
    }
    
    // 如果data已经是完整的结果对象，直接返回
    if (data.success !== undefined && data.data !== undefined) {
      console.log('✅ [WholesaleLogisticsExtractor] 数据已经是完整格式，直接返回');
      return data;
    }
    
    // 否则包装数据
    const formattedResult = {
      success: true,
      extractorId: this.moduleId,
      extractorName: this.name,
      data: data,
      extractedAt: new Date().toISOString(),
      version: '1.0.0'
    };
    
    console.log('📦 [WholesaleLogisticsExtractor] 格式化完成:', formattedResult);
    return formattedResult;
  }
}

// 导出类
if (typeof window !== 'undefined') {
  window.WholesaleLogisticsExtractor = WholesaleLogisticsExtractor;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = WholesaleLogisticsExtractor;
}