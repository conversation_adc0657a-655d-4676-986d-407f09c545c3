/**
 * 物流提取器数据验证修复测试脚本
 * 专门测试修复后的validate和format方法
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * 测试批发物流提取器的数据验证修复
 */
async function testWholesaleLogisticsValidationFix() {
  console.log('🧪 开始测试批发物流提取器数据验证修复...');
  
  if (!window.WholesaleLogisticsExtractor) {
    console.error('❌ WholesaleLogisticsExtractor 未找到');
    return false;
  }
  
  const extractor = new window.WholesaleLogisticsExtractor();
  
  // 测试用例1: 空数据
  console.log('\n📋 测试用例1: 空数据');
  const emptyResult = extractor.validate(null);
  console.log('空数据验证结果:', emptyResult, '(应该为false)');
  
  // 测试用例2: 无效数据结构
  console.log('\n📋 测试用例2: 无效数据结构');
  const invalidResult = extractor.validate({ success: false });
  console.log('无效数据验证结果:', invalidResult, '(应该为false)');
  
  // 测试用例3: 有效数据（直接格式）
  console.log('\n📋 测试用例3: 有效数据（直接格式）');
  const validData1 = {
    originCity: '浙江金华',
    destinationCity: '北京朝阳',
    shippingFee: '运费¥4起',
    deliveryPromise: '承诺48小时发货'
  };
  const validResult1 = extractor.validate(validData1);
  console.log('有效数据验证结果:', validResult1, '(应该为true)');
  
  // 测试用例4: 有效数据（嵌套格式）
  console.log('\n📋 测试用例4: 有效数据（嵌套格式）');
  const validData2 = {
    success: true,
    data: {
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起'
    }
  };
  const validResult2 = extractor.validate(validData2);
  console.log('嵌套数据验证结果:', validResult2, '(应该为true)');
  
  // 测试用例5: 部分有效数据
  console.log('\n📋 测试用例5: 部分有效数据');
  const partialData = {
    originCity: '浙江金华',
    destinationCity: '',
    shippingFee: '',
    deliveryPromise: ''
  };
  const partialResult = extractor.validate(partialData);
  console.log('部分数据验证结果:', partialResult, '(应该为true，因为有发货地)');
  
  // 测试format方法
  console.log('\n📋 测试format方法');
  try {
    const formatResult = extractor.format(validData1);
    console.log('格式化结果:', formatResult);
    console.log('格式化成功:', !!formatResult && formatResult.success);
  } catch (error) {
    console.error('格式化失败:', error);
  }
  
  return true;
}

/**
 * 测试代发物流提取器的数据验证修复
 */
async function testConsignLogisticsValidationFix() {
  console.log('\n🧪 开始测试代发物流提取器数据验证修复...');
  
  if (!window.ConsignLogisticsExtractor) {
    console.error('❌ ConsignLogisticsExtractor 未找到');
    return false;
  }
  
  const extractor = new window.ConsignLogisticsExtractor();
  
  // 测试用例1: 空数据
  console.log('\n📋 测试用例1: 空数据');
  const emptyResult = extractor.validate(null);
  console.log('空数据验证结果:', emptyResult, '(应该为false)');
  
  // 测试用例2: 无效数据结构
  console.log('\n📋 测试用例2: 无效数据结构');
  const invalidResult = extractor.validate({ success: false });
  console.log('无效数据验证结果:', invalidResult, '(应该为false)');
  
  // 测试用例3: 有效数据（直接格式）
  console.log('\n📋 测试用例3: 有效数据（直接格式）');
  const validData1 = {
    originCity: '浙江金华',
    destinationCity: '北京朝阳',
    shippingFee: '运费¥4起',
    deliveryTime: '承诺48小时发货'
  };
  const validResult1 = extractor.validate(validData1);
  console.log('有效数据验证结果:', validResult1, '(应该为true)');
  
  // 测试用例4: 有效数据（嵌套格式）
  console.log('\n📋 测试用例4: 有效数据（嵌套格式）');
  const validData2 = {
    success: true,
    data: {
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起'
    }
  };
  const validResult2 = extractor.validate(validData2);
  console.log('嵌套数据验证结果:', validResult2, '(应该为true)');
  
  // 测试用例5: 包含"未知"值的数据
  console.log('\n📋 测试用例5: 包含"未知"值的数据');
  const unknownData = {
    originCity: '浙江金华',
    destinationCity: '未知',
    shippingFee: '未知',
    deliveryTime: '未知'
  };
  const unknownResult = extractor.validate(unknownData);
  console.log('包含未知值验证结果:', unknownResult, '(应该为true，因为有发货地)');
  
  // 测试format方法
  console.log('\n📋 测试format方法');
  try {
    const formatResult = extractor.format(validData1);
    console.log('格式化结果:', formatResult);
    console.log('格式化成功:', !!formatResult && formatResult.success);
  } catch (error) {
    console.error('格式化失败:', error);
  }
  
  return true;
}

/**
 * 测试实际提取流程
 */
async function testActualExtractionFlow() {
  console.log('\n🧪 开始测试实际提取流程...');
  
  // 测试批发物流提取器
  if (window.WholesaleLogisticsExtractor) {
    console.log('\n📊 测试批发物流提取器实际提取...');
    try {
      const extractor = new window.WholesaleLogisticsExtractor();
      const result = await extractor.extract();
      console.log('批发提取结果:', result);
      
      if (result && result.success) {
        console.log('✅ 批发物流提取成功');
        console.log('数据:', result.data);
        console.log('置信度:', result.confidence);
      } else {
        console.log('❌ 批发物流提取失败:', result?.error);
      }
    } catch (error) {
      console.error('❌ 批发物流提取异常:', error);
    }
  }
  
  // 测试代发物流提取器
  if (window.ConsignLogisticsExtractor) {
    console.log('\n📊 测试代发物流提取器实际提取...');
    try {
      const extractor = new window.ConsignLogisticsExtractor();
      const result = await extractor.extract();
      console.log('代发提取结果:', result);
      
      if (result && result.success) {
        console.log('✅ 代发物流提取成功');
        console.log('数据:', result.data);
        console.log('置信度:', result.confidence);
      } else {
        console.log('❌ 代发物流提取失败:', result?.error);
      }
    } catch (error) {
      console.error('❌ 代发物流提取异常:', error);
    }
  }
}

/**
 * 运行完整的验证修复测试
 */
async function runValidationFixTest() {
  console.log('🚀 开始运行物流提取器数据验证修复测试...');
  console.log('=' .repeat(60));
  
  try {
    // 测试批发物流提取器
    await testWholesaleLogisticsValidationFix();
    
    // 测试代发物流提取器
    await testConsignLogisticsValidationFix();
    
    // 测试实际提取流程
    await testActualExtractionFlow();
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 物流提取器数据验证修复测试完成！');
    console.log('\n💡 如果看到上述测试都通过，说明数据验证问题已修复。');
    console.log('💡 现在可以重新运行扩展，物流信息应该能正常提取了。');
    
  } catch (error) {
    console.error('❌ 测试过程中出现异常:', error);
  }
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.LogisticsValidationFixTest = {
    testWholesaleLogisticsValidationFix,
    testConsignLogisticsValidationFix,
    testActualExtractionFlow,
    runValidationFixTest
  };
  
  // 自动运行测试（延迟2秒等待页面加载）
  setTimeout(() => {
    if (window.location.href.includes('1688.com')) {
      console.log('🔧 检测到1688页面，2秒后自动运行数据验证修复测试...');
      setTimeout(runValidationFixTest, 2000);
    }
  }, 1000);
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testWholesaleLogisticsValidationFix,
    testConsignLogisticsValidationFix,
    testActualExtractionFlow,
    runValidationFixTest
  };
}