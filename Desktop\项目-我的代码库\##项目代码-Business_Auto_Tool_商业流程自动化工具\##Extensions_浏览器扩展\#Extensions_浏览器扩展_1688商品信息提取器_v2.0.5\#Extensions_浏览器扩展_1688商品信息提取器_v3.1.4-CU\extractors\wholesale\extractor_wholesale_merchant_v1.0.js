/**
 * 批发模式商家信息抽取器
 * 专门处理1688批发页面的商家信息提取
 * 目标元素: <h1> 标签中的商家信息
 * <AUTHOR>
 * @version 1.0.0
 */

class WholesaleMerchantExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_wholesale_merchant_001',
      '批发商家信息',
      '提取1688批发页面的商家名称和相关信息'
    );

    // 抽取器配置
    this.config = {
      timeout: 10000, // 10秒超时
      retryDelay: 2000, // 重试延迟2秒
      maxTextLength: 200, // 最大文本长度
      minTextLength: 2 // 最小文本长度
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 基于Playwright DOM分析的精确商家选择器 - 按优先级排序
      primary: [
        // 最精确的商家名称选择器（基于实际DOM结构分析）
        'div#shopNavigation > div.od-shop-navigation > div.shop-container > div.winport-title.v-flex:nth-of-type(1) > a.shop-company-name.v-flex:nth-of-type(1) > h1',
        '.shop-company-name h1', // 店铺公司名称
        '.winport-title .shop-company-name h1', // 窗口标题中的店铺公司名称
        '#shopNavigation h1', // 基于ID的精确定位
        '.od-shop-navigation h1' // 店铺导航中的h1
      ],

      // 备用选择器
      fallback: [
        '.shop-company-name', // 店铺公司名称容器
        '.winport-title a', // 窗口标题链接
        'h1', // 通用h1标签
        '.company-title',
        '.merchant-title',
        '[data-company-name]',
        '[data-merchant-name]',
        'a[data-trace="SHOP_CREDIT"]' // 店铺信用链接
      ],

      // 上下文选择器 - 用于缩小搜索范围
      context: [
        '.od-shop-navigation', // 店铺导航
        '.winport-title', // 窗口标题
        '.shop-container', // 店铺容器
        '.company-info',
        '.merchant-info',
        '.supplier-info',
        '.shop-info',
        'header',
        '.page-header'
      ],

      // 排除选择器 - 避免误选
      exclude: [
        '.advertisement',
        '.ad-content',
        '.popup',
        '.modal',
        '.tooltip',
        'script',
        'style',
        'noscript'
      ]
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的商家信息
   */
  async extract() {
    // 开始商家信息提取

    const selectors = this.getSelectors();
    let merchantInfo = null;

    try {
      // 第一步：尝试主要选择器
      // 尝试主要选择器
      merchantInfo = await this.extractWithPrimarySelectors(selectors.primary);

      if (!merchantInfo) {
        // 第二步：尝试在上下文中查找
        // 尝试上下文选择器
        merchantInfo = await this.extractWithContextSelectors(selectors.context, selectors.primary);
      } else {
        // 主要选择器提取成功
      }

      if (!merchantInfo) {
        // 第三步：尝试备用选择器
        // 尝试备用选择器
        merchantInfo = await this.extractWithFallbackSelectors(selectors.fallback);
      } else if (!merchantInfo.fromPrimary) {
        // 上下文选择器提取成功
      }

      if (!merchantInfo) {
        // 所有方法都未找到商家信息元素
        throw new Error('未找到商家信息元素');
      } else if (!merchantInfo.fromPrimary && !merchantInfo.fromContext) {
        // 备用选择器提取成功
      }

      // 原始商家信息提取完成

      // 记录DOM数据到调试面板
      if (window.debugPanel && merchantInfo.element) {
        window.debugPanel.addDOMData('商家信息提取器', merchantInfo.selector || '商家选择器', merchantInfo.element);
      }

      // 数据清理和增强
      // 开始数据增强处理
      const enhancedInfo = this.enhanceMerchantInfo(merchantInfo);

      // 记录提取结果到调试面板
      if (window.debugPanel) {
        window.debugPanel.addResult('商家信息提取器', enhancedInfo);
      }

      // 商家信息提取完成

      return enhancedInfo;

    } catch (error) {
      console.error('❌ [商家调试] 批发商家信息提取失败:', error);
      // 商家信息提取异常结束
      throw error;
    }
  }

  /**
   * 使用主要选择器提取
   * @param {Array} selectors - 选择器列表
   * @returns {Promise<Object|null>} 商家信息
   */
  async extractWithPrimarySelectors(selectors) {
    for (const selector of selectors) {
      try {
        const element = await this.waitForElement(selector, 3000);
        if (element && this.isValidMerchantElement(element)) {
          return this.extractMerchantData(element, selector, 'primary');
        }
      } catch (error) {
        console.debug(`主选择器 ${selector} 未找到元素:`, error.message);
        continue;
      }
    }
    return null;
  }

  /**
   * 在上下文中查找
   * @param {Array} contextSelectors - 上下文选择器
   * @param {Array} targetSelectors - 目标选择器
   * @returns {Promise<Object|null>} 商家信息
   */
  async extractWithContextSelectors(contextSelectors, targetSelectors) {
    for (const contextSelector of contextSelectors) {
      const contextElement = this.safeQuerySelector(contextSelector);
      if (!contextElement) continue;

      for (const targetSelector of targetSelectors) {
        const element = this.safeQuerySelector(targetSelector, contextElement);
        if (element && this.isValidMerchantElement(element)) {
          return this.extractMerchantData(element, `${contextSelector} ${targetSelector}`, 'context');
        }
      }
    }
    return null;
  }

  /**
   * 使用备用选择器提取
   * @param {Array} selectors - 备用选择器列表
   * @returns {Promise<Object|null>} 商家信息
   */
  async extractWithFallbackSelectors(selectors) {
    for (const selector of selectors) {
      const elements = this.safeQuerySelectorAll(selector);

      for (const element of elements) {
        if (this.isValidMerchantElement(element)) {
          return this.extractMerchantData(element, selector, 'fallback');
        }
      }
    }
    return null;
  }

  /**
   * 验证是否为有效的商家元素
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否有效
   */
  isValidMerchantElement(element) {
    if (!element) return false;

    // 检查是否在排除列表中
    const excludeSelectors = this.getSelectors().exclude;
    for (const excludeSelector of excludeSelectors) {
      if (element.matches && element.matches(excludeSelector)) {
        return false;
      }
      if (element.closest && element.closest(excludeSelector)) {
        return false;
      }
    }

    // 获取文本内容
    const text = this.getElementText(element);
    if (!text) return false;

    // 文本长度检查
    if (text.length < this.config.minTextLength || text.length > this.config.maxTextLength) {
      return false;
    }

    // 检查是否包含商家相关关键词
    const merchantKeywords = [
      '公司', '企业', '商行', '厂', '店', '铺',
      '有限公司', '股份', '集团', '实业',
      '贸易', '制造', '生产', '经营'
    ];

    const hasKeyword = merchantKeywords.some(keyword => text.includes(keyword));

    // 检查是否为明显的非商家信息
    const nonMerchantKeywords = [
      '登录', '注册', '搜索', '购物车', '收藏',
      '价格', '数量', '规格', '型号', '品牌',
      '评价', '评论', '问答', '客服'
    ];

    const hasNonKeyword = nonMerchantKeywords.some(keyword => text.includes(keyword));

    return hasKeyword || (!hasNonKeyword && text.length >= 4);
  }

  /**
   * 提取商家数据
   * @param {Element} element - DOM元素
   * @param {string} selector - 使用的选择器
   * @param {string} method - 提取方法
   * @returns {Object} 商家信息
   */
  extractMerchantData(element, selector, method) {
    const merchantName = this.getElementText(element);
    const title = element.getAttribute('title') || merchantName;

    return {
      name: merchantName,
      title: title,
      element: {
        tagName: element.tagName.toLowerCase(),
        className: element.className,
        id: element.id,
        selector: selector
      },
      extraction: {
        method: method,
        selector: selector,
        timestamp: Date.now()
      },
      metadata: {
        textLength: merchantName.length,
        hasTitle: !!element.getAttribute('title'),
        position: this.getElementPosition(element)
      }
    };
  }

  /**
   * 获取元素文本内容
   * @param {Element} element - DOM元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    if (!element) return '';

    try {
      // 优先使用title属性
      const title = element.getAttribute('title');
      if (title && title.trim && typeof title.trim === 'function') {
        return title.trim();
      }

      // 使用textContent
      const textContent = element.textContent || element.innerText || '';
      if (textContent && typeof textContent === 'string') {
        return textContent.trim().replace(/\s+/g, ' ');
      }

      return '';
    } catch (error) {
      console.warn('🔧 [商家信息] getElementText错误:', error.message);
      return '';
    }
  }

  /**
   * 获取元素位置信息
   * @param {Element} element - DOM元素
   * @returns {Object} 位置信息
   */
  getElementPosition(element) {
    try {
      const rect = element.getBoundingClientRect();
      return {
        top: Math.round(rect.top),
        left: Math.round(rect.left),
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        visible: rect.width > 0 && rect.height > 0
      };
    } catch (error) {
      return { visible: false };
    }
  }

  /**
   * 增强商家信息
   * @param {Object} merchantInfo - 原始商家信息
   * @returns {Object} 增强后的商家信息
   */
  enhanceMerchantInfo(merchantInfo) {
    const enhanced = { ...merchantInfo };

    // 分析商家类型
    enhanced.analysis = this.analyzeMerchantType(merchantInfo.name);

    // 提取关键信息
    enhanced.keywords = this.extractKeywords(merchantInfo.name);

    // 计算可信度
    enhanced.confidence = this.calculateConfidence(merchantInfo);

    return enhanced;
  }

  /**
   * 分析商家类型
   * @param {string} merchantName - 商家名称
   * @returns {Object} 分析结果
   */
  analyzeMerchantType(merchantName) {
    const analysis = {
      type: 'unknown',
      subtype: null,
      indicators: []
    };

    // 公司类型判断
    if (merchantName.includes('有限公司') || merchantName.includes('有限责任公司')) {
      analysis.type = 'company';
      analysis.subtype = 'limited';
      analysis.indicators.push('有限公司');
    } else if (merchantName.includes('股份有限公司')) {
      analysis.type = 'company';
      analysis.subtype = 'corporation';
      analysis.indicators.push('股份公司');
    } else if (merchantName.includes('个体工商户') || merchantName.includes('个体户')) {
      analysis.type = 'individual';
      analysis.indicators.push('个体户');
    } else if (merchantName.includes('合作社')) {
      analysis.type = 'cooperative';
      analysis.indicators.push('合作社');
    }

    // 行业类型判断
    const industryKeywords = {
      manufacturing: ['制造', '生产', '厂', '工厂'],
      trading: ['贸易', '商贸', '经贸'],
      retail: ['商行', '店', '铺'],
      technology: ['科技', '技术', '电子'],
      textile: ['纺织', '服装', '布艺']
    };

    for (const [industry, keywords] of Object.entries(industryKeywords)) {
      if (keywords.some(keyword => merchantName.includes(keyword))) {
        analysis.industry = industry;
        analysis.indicators.push(...keywords.filter(k => merchantName.includes(k)));
        break;
      }
    }

    return analysis;
  }

  /**
   * 提取关键词
   * @param {string} merchantName - 商家名称
   * @returns {Array} 关键词列表
   */
  extractKeywords(merchantName) {
    const keywords = [];

    // 地区关键词
    const regions = ['北京', '上海', '广州', '深圳', '杭州', '义乌', '温州', '东莞', '佛山'];
    regions.forEach(region => {
      if (merchantName.includes(region)) {
        keywords.push({ type: 'region', value: region });
      }
    });

    // 行业关键词
    const industries = ['电子', '服装', '家具', '五金', '化工', '食品', '玩具', '箱包'];
    industries.forEach(industry => {
      if (merchantName.includes(industry)) {
        keywords.push({ type: 'industry', value: industry });
      }
    });

    return keywords;
  }

  /**
   * 计算提取可信度
   * @param {Object} merchantInfo - 商家信息
   * @returns {number} 可信度 (0-100)
   */
  calculateConfidence(merchantInfo) {
    let confidence = 0;

    // 基础分数
    confidence += 30;

    // 提取方法加分
    if (merchantInfo.extraction.method === 'primary') {
      confidence += 40;
    } else if (merchantInfo.extraction.method === 'context') {
      confidence += 25;
    } else {
      confidence += 10;
    }

    // 元素属性加分
    if (merchantInfo.metadata.hasTitle) {
      confidence += 15;
    }

    // 文本长度合理性
    const textLength = merchantInfo.metadata.textLength;
    if (textLength >= 4 && textLength <= 50) {
      confidence += 10;
    } else if (textLength > 50) {
      confidence -= 5;
    }

    // 可见性检查
    if (merchantInfo.metadata.position && merchantInfo.metadata.position.visible) {
      confidence += 5;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }

    // 检查必要字段
    if (!data.name || typeof data.name !== 'string') {
      return false;
    }

    // 检查文本长度
    if (data.name.length < this.config.minTextLength ||
      data.name.length > this.config.maxTextLength) {
      return false;
    }

    // 检查可信度
    if (data.confidence && data.confidence < 30) {
      return false;
    }

    return true;
  }

  /**
   * 数据格式化
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    if (!data) return null;

    return {
      ...data,
      name: data.name.trim().replace(/\s+/g, ' '),
      title: data.title ? data.title.trim().replace(/\s+/g, ' ') : data.name,
      formatted: true,
      formatTime: Date.now()
    };
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.WholesaleMerchantExtractor = WholesaleMerchantExtractor;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WholesaleMerchantExtractor;
}