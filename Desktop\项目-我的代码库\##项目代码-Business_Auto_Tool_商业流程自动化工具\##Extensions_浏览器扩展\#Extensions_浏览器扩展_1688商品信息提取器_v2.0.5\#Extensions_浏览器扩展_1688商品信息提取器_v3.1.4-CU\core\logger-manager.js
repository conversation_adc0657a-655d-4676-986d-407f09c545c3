/**
 * 日志管理器 - 统一日志收集和错误报告
 * 为每个模块提供独立的日志反馈和错误追踪
 * <AUTHOR>
 * @version 1.0.0
 */

class LoggerManager {
  constructor() {
    this.logs = new Map(); // 按模块存储日志
    this.errors = new Map(); // 按模块存储错误
    this.config = {
      maxLogsPerModule: 100,
      maxErrors: 50,
      enableConsoleOutput: true,
      enableErrorUI: true
    };
    
    this.init();
  }
  
  /**
   * 初始化日志管理器
   */
  init() {
    // 创建错误报告UI容器
    this.createErrorReportUI();
    
    // 监听全局错误
    this.setupGlobalErrorHandling();
    
    console.log('Logger Manager initialized');
  }
  
  /**
   * 记录日志
   * @param {string} moduleId - 模块ID
   * @param {string} level - 日志级别 (info, warn, error, debug)
   * @param {string} message - 日志消息
   * @param {Object} data - 附加数据
   */
  log(moduleId, level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data,
      id: `${moduleId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    // 存储到模块日志
    if (!this.logs.has(moduleId)) {
      this.logs.set(moduleId, []);
    }
    
    const moduleLogs = this.logs.get(moduleId);
    moduleLogs.push(logEntry);
    
    // 限制日志数量
    if (moduleLogs.length > this.config.maxLogsPerModule) {
      moduleLogs.shift();
    }
    
    // 控制台输出
    if (this.config.enableConsoleOutput) {
      this.outputToConsole(moduleId, logEntry);
    }
    
    // 如果是错误级别，特殊处理
    if (level === 'error') {
      this.handleError(moduleId, message, data);
    }
    
    return logEntry.id;
  }
  
  /**
   * 记录错误
   * @param {string} moduleId - 模块ID
   * @param {string} message - 错误消息
   * @param {Object} error - 错误对象或附加数据
   */
  error(moduleId, message, error = null) {
    return this.log(moduleId, 'error', message, error);
  }
  
  /**
   * 记录警告
   * @param {string} moduleId - 模块ID
   * @param {string} message - 警告消息
   * @param {Object} data - 附加数据
   */
  warn(moduleId, message, data = null) {
    return this.log(moduleId, 'warn', message, data);
  }
  
  /**
   * 记录信息
   * @param {string} moduleId - 模块ID
   * @param {string} message - 信息消息
   * @param {Object} data - 附加数据
   */
  info(moduleId, message, data = null) {
    return this.log(moduleId, 'info', message, data);
  }
  
  /**
   * 记录调试信息
   * @param {string} moduleId - 模块ID
   * @param {string} message - 调试消息
   * @param {Object} data - 附加数据
   */
  debug(moduleId, message, data = null) {
    return this.log(moduleId, 'debug', message, data);
  }
  
  /**
   * 处理错误
   * @param {string} moduleId - 模块ID
   * @param {string} message - 错误消息
   * @param {Object} error - 错误数据
   */
  handleError(moduleId, message, error) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      moduleId,
      message,
      error: error ? this.serializeError(error) : null,
      stack: error && error.stack ? error.stack : new Error().stack,
      id: `error_${moduleId}_${Date.now()}`
    };
    
    // 存储错误
    if (!this.errors.has(moduleId)) {
      this.errors.set(moduleId, []);
    }
    
    const moduleErrors = this.errors.get(moduleId);
    moduleErrors.push(errorEntry);
    
    // 限制错误数量
    if (moduleErrors.length > this.config.maxErrors) {
      moduleErrors.shift();
    }
    
    // 更新错误报告UI
    if (this.config.enableErrorUI) {
      this.updateErrorReportUI();
    }
  }
  
  /**
   * 序列化错误对象
   * @param {Error|Object} error - 错误对象
   * @returns {Object} 序列化后的错误信息
   */
  serializeError(error) {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }
    
    if (typeof error === 'object') {
      try {
        return JSON.parse(JSON.stringify(error));
      } catch (e) {
        return { serializedError: error.toString() };
      }
    }
    
    return { error: error.toString() };
  }
  
  /**
   * 输出到控制台
   * @param {string} moduleId - 模块ID
   * @param {Object} logEntry - 日志条目
   */
  outputToConsole(moduleId, logEntry) {
    const prefix = `[${moduleId}] ${logEntry.timestamp}`;
    const message = `${prefix} ${logEntry.message}`;
    
    switch (logEntry.level) {
      case 'error':
        console.error(message, logEntry.data);
        break;
      case 'warn':
        console.warn(message, logEntry.data);
        break;
      case 'debug':
        console.debug(message, logEntry.data);
        break;
      default:
        console.log(message, logEntry.data);
    }
  }
  
  /**
   * 创建错误报告UI
   */
  createErrorReportUI() {
    // 检查是否已存在
    if (document.querySelector('.extractor-error-report')) {
      return;
    }
    
    const errorReport = document.createElement('div');
    errorReport.className = 'extractor-error-report hidden';
    errorReport.innerHTML = `
      <div class="error-report-header">
        <div class="error-report-title">
          <span class="error-report-icon">🐛</span>
          <h3>模块错误报告</h3>
        </div>
        <div class="error-report-controls">
          <button class="error-report-minimize" title="最小化">−</button>
          <button class="error-report-close" title="关闭">✕</button>
        </div>
      </div>
      <div class="error-report-content">
        <div class="error-report-summary">
          <div class="error-count">总错误数: <span id="total-errors">0</span></div>
          <div class="module-count">受影响模块: <span id="affected-modules">0</span></div>
        </div>
        <div class="error-report-modules" id="error-modules-list">
          <!-- 模块错误列表将在这里动态生成 -->
        </div>
      </div>
      <div class="error-report-footer">
        <button class="error-report-btn clear-btn">清除所有错误</button>
        <button class="error-report-btn export-btn">导出错误报告</button>
        <button class="error-report-btn close-btn">关闭</button>
      </div>
    `;
    
    document.body.appendChild(errorReport);
    this.errorReportUI = errorReport;
    
    // 绑定事件监听器
    this.bindErrorReportEvents();
  }
  
  /**
   * 绑定错误报告UI事件
   */
  bindErrorReportEvents() {
    if (!this.errorReportUI) return;
    
    // 绑定最小化按钮
    const minimizeBtn = this.errorReportUI.querySelector('.error-report-minimize');
    if (minimizeBtn) {
      minimizeBtn.addEventListener('click', () => this.toggleMinimize());
    }
    
    // 绑定关闭按钮
    const closeBtns = this.errorReportUI.querySelectorAll('.error-report-close, .close-btn');
    closeBtns.forEach(btn => {
      btn.addEventListener('click', () => this.hideErrorReport());
    });
    
    // 绑定清除按钮
    const clearBtn = this.errorReportUI.querySelector('.clear-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => this.clearAllErrors());
    }
    
    // 绑定导出按钮
    const exportBtn = this.errorReportUI.querySelector('.export-btn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportErrorReport());
    }
  }
  
  /**
   * 更新错误报告UI
   */
  updateErrorReportUI() {
    if (!this.errorReportUI) return;
    
    const totalErrors = Array.from(this.errors.values()).reduce((sum, errors) => sum + errors.length, 0);
    const affectedModules = this.errors.size;
    
    // 更新统计信息
    const totalErrorsEl = document.getElementById('total-errors');
    const affectedModulesEl = document.getElementById('affected-modules');
    
    if (totalErrorsEl) totalErrorsEl.textContent = totalErrors;
    if (affectedModulesEl) affectedModulesEl.textContent = affectedModules;
    
    // 更新模块错误列表
    const modulesList = document.getElementById('error-modules-list');
    if (modulesList) {
      modulesList.innerHTML = '';
      
      for (const [moduleId, errors] of this.errors) {
        const moduleDiv = document.createElement('div');
        moduleDiv.className = 'error-module-item';
        
        const latestError = errors[errors.length - 1];
        const errorCount = errors.length;
        
        moduleDiv.innerHTML = `
          <div class="error-module-header">
            <div class="error-module-name">${moduleId}</div>
            <div class="error-module-count">${errorCount} 个错误</div>
          </div>
          <div class="error-module-latest">
            <div class="error-time">${new Date(latestError.timestamp).toLocaleString()}</div>
            <div class="error-message">${latestError.message}</div>
          </div>
          <div class="error-module-actions">
            <button class="error-action-btn view-details" data-module-id="${moduleId}">查看详情</button>
            <button class="error-action-btn clear-module" data-module-id="${moduleId}">清除</button>
          </div>
        `;
        
        modulesList.appendChild(moduleDiv);
        
        // 绑定动态生成的按钮事件
        const viewBtn = moduleDiv.querySelector('.view-details');
        const clearBtn = moduleDiv.querySelector('.clear-module');
        
        if (viewBtn) {
          viewBtn.addEventListener('click', () => this.showModuleErrors(moduleId));
        }
        
        if (clearBtn) {
          clearBtn.addEventListener('click', () => this.clearModuleErrors(moduleId));
        }
      }
    }
    
    // 如果有错误，显示错误报告UI
    if (totalErrors > 0) {
      this.showErrorReport();
    }
  }
  
  /**
   * 显示错误报告
   */
  showErrorReport() {
    if (this.errorReportUI) {
      this.errorReportUI.classList.remove('hidden');
    }
  }
  
  /**
   * 隐藏错误报告
   */
  hideErrorReport() {
    if (this.errorReportUI) {
      this.errorReportUI.classList.add('hidden');
      // 重置最小化状态
      this.errorReportUI.classList.remove('minimized');
    }
  }
  
  /**
   * 切换最小化状态
   */
  toggleMinimize() {
    if (this.errorReportUI) {
      const isMinimized = this.errorReportUI.classList.contains('minimized');
      if (isMinimized) {
        this.errorReportUI.classList.remove('minimized');
        const minimizeBtn = this.errorReportUI.querySelector('.error-report-minimize');
        if (minimizeBtn) {
          minimizeBtn.innerHTML = '−';
          minimizeBtn.title = '最小化';
        }
      } else {
        this.errorReportUI.classList.add('minimized');
        const minimizeBtn = this.errorReportUI.querySelector('.error-report-minimize');
        if (minimizeBtn) {
          minimizeBtn.innerHTML = '□';
          minimizeBtn.title = '还原';
        }
      }
    }
  }
  
  /**
   * 显示特定模块的错误详情
   * @param {string} moduleId - 模块ID
   */
  showModuleErrors(moduleId) {
    const errors = this.errors.get(moduleId) || [];
    
    // 创建模块错误详情弹窗
    const modal = document.createElement('div');
    modal.className = 'extractor-error-modal';
    modal.innerHTML = `
      <div class="error-modal-content">
        <div class="error-modal-header">
          <h3>${moduleId} 错误详情</h3>
          <button class="error-modal-close">✕</button>
        </div>
        <div class="error-modal-body">
          ${errors.map(error => `
            <div class="error-detail-item">
              <div class="error-detail-time">${new Date(error.timestamp).toLocaleString()}</div>
              <div class="error-detail-message">${error.message}</div>
              ${error.error ? `<div class="error-detail-data">${JSON.stringify(error.error, null, 2)}</div>` : ''}
              ${error.stack ? `<div class="error-detail-stack">${error.stack}</div>` : ''}
            </div>
          `).join('')}
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定关闭按钮事件
    const closeBtn = modal.querySelector('.error-modal-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => modal.remove());
    }
    
    // 点击背景关闭弹窗
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }
  
  /**
   * 清除所有错误
   */
  clearAllErrors() {
    this.errors.clear();
    this.updateErrorReportUI();
    this.hideErrorReport();
  }
  
  /**
   * 清除特定模块的错误
   * @param {string} moduleId - 模块ID
   */
  clearModuleErrors(moduleId) {
    this.errors.delete(moduleId);
    this.updateErrorReportUI();
  }
  
  /**
   * 导出错误报告
   */
  exportErrorReport() {
    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      errors: Object.fromEntries(this.errors),
      logs: Object.fromEntries(this.logs)
    };
    
    const jsonStr = JSON.stringify(report, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `extractor_error_report_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
  }
  
  /**
   * 获取模块日志
   * @param {string} moduleId - 模块ID
   * @returns {Array} 模块日志列表
   */
  getModuleLogs(moduleId) {
    return this.logs.get(moduleId) || [];
  }
  
  /**
   * 获取模块错误
   * @param {string} moduleId - 模块ID
   * @returns {Array} 模块错误列表
   */
  getModuleErrors(moduleId) {
    return this.errors.get(moduleId) || [];
  }
  
  /**
   * 获取所有日志统计
   * @returns {Object} 日志统计信息
   */
  getLogStats() {
    const stats = {
      totalLogs: 0,
      totalErrors: 0,
      moduleStats: {}
    };
    
    for (const [moduleId, logs] of this.logs) {
      const errorCount = logs.filter(log => log.level === 'error').length;
      stats.totalLogs += logs.length;
      stats.totalErrors += errorCount;
      stats.moduleStats[moduleId] = {
        totalLogs: logs.length,
        errors: errorCount,
        warnings: logs.filter(log => log.level === 'warn').length,
        info: logs.filter(log => log.level === 'info').length,
        debug: logs.filter(log => log.level === 'debug').length
      };
    }
    
    return stats;
  }
  
  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandling() {
    // 捕获未处理的错误
    window.addEventListener('error', (event) => {
      this.error('global', `Uncaught error: ${event.message}`, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
    });
    
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.error('global', `Unhandled promise rejection: ${event.reason}`, {
        reason: event.reason,
        promise: event.promise
      });
    });
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.LoggerManager = LoggerManager;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LoggerManager;
}