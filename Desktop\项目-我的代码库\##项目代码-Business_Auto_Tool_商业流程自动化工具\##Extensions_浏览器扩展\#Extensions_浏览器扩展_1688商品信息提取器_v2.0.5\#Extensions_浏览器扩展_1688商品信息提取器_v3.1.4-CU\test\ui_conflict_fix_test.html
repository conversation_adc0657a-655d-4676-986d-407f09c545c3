<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI冲突修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #005a87;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #F44336; }
        
        .mock-data {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 UI冲突修复测试</h1>
            <p>测试1688商品信息提取器的多重UI实现冲突修复效果</p>
        </div>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>此测试用于验证以下问题的修复：</p>
            <ul>
                <li>多重UI实现导致的显示冲突</li>
                <li>物流信息重复处理逻辑</li>
                <li>结果格式化器与UI管理器的协调</li>
                <li>结果公告板的单一显示</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🎯 测试操作</h3>
            <button class="test-button" onclick="loadTestScripts()">加载测试脚本</button>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="testLogisticsDisplay()">测试物流显示</button>
            <button class="test-button" onclick="clearResults()">清除结果</button>
        </div>
        
        <div class="test-section">
            <h3>📊 模拟数据</h3>
            <div class="mock-data">
                <strong>批发物流信息:</strong><br>
                浙江金华 → 北京朝阳, 运费¥4起, 承诺48小时发货<br><br>
                <strong>代发物流信息:</strong><br>
                广东东莞 → 上海浦东, ¥15.00, 24小时内发货
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试结果</h3>
            <div id="testResults" class="test-results">等待测试开始...</div>
        </div>
        
        <div class="test-section">
            <h3>🔍 状态检查</h3>
            <div id="statusCheck">
                <p><span class="status-indicator status-warning"></span>等待检查组件状态...</p>
            </div>
        </div>
    </div>

    <script>
        let testOutput = '';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            testOutput += `[${timestamp}] ${prefix} ${message}\n`;
            updateTestResults();
        }
        
        function updateTestResults() {
            document.getElementById('testResults').textContent = testOutput;
            document.getElementById('testResults').scrollTop = document.getElementById('testResults').scrollHeight;
        }
        
        function clearResults() {
            testOutput = '';
            updateTestResults();
            log('测试结果已清除');
        }
        
        function checkComponentStatus() {
            const statusDiv = document.getElementById('statusCheck');
            let statusHtml = '';
            
            // 检查各个组件的状态
            const components = [
                { name: 'ResultFormatter', obj: window.resultFormatter },
                { name: 'UIManager', obj: window.uiManager },
                { name: 'LoggerManager', obj: window.loggerManager },
                { name: 'ExtractionManager', obj: window.extractionManager }
            ];
            
            components.forEach(comp => {
                const status = comp.obj ? 'success' : 'error';
                const statusText = comp.obj ? '已加载' : '未加载';
                statusHtml += `<p><span class="status-indicator status-${status}"></span>${comp.name}: ${statusText}</p>`;
            });
            
            statusDiv.innerHTML = statusHtml;
        }
        
        function loadTestScripts() {
            log('开始加载测试脚本...');
            
            // 模拟加载核心组件
            if (!window.resultFormatter) {
                log('ResultFormatter 未找到，尝试加载...', 'warning');
            }
            
            if (!window.uiManager) {
                log('UIManager 未找到，尝试加载...', 'warning');
            }
            
            // 检查组件状态
            checkComponentStatus();
            
            log('测试脚本加载完成', 'success');
        }
        
        function runAllTests() {
            log('开始运行所有测试...');
            
            if (window.uiConflictFixTest) {
                try {
                    window.uiConflictFixTest.runAllTests();
                    log('测试执行完成，请查看控制台获取详细结果', 'success');
                } catch (error) {
                    log(`测试执行失败: ${error.message}`, 'error');
                }
            } else {
                log('测试脚本未加载，请先加载测试脚本', 'warning');
            }
        }
        
        function testLogisticsDisplay() {
            log('开始测试物流信息显示...');
            
            // 模拟物流数据
            const mockData = {
                wholesaleLogistics: {
                    success: true,
                    name: '批发物流信息',
                    data: {
                        confidence: 90,
                        originCity: '浙江金华',
                        destinationCity: '北京朝阳',
                        shippingFee: '运费¥4起',
                        deliveryTime: '承诺48小时发货'
                    }
                },
                dropshipLogistics: {
                    success: true,
                    name: '代发物流信息',
                    data: {
                        confidence: 85,
                        originCity: '广东东莞',
                        destinationCity: '上海浦东',
                        shippingFee: '¥15.00',
                        deliveryTime: '24小时内发货'
                    }
                }
            };
            
            if (window.uiManager && window.uiManager.showResultsBoard) {
                try {
                    const testResults = new Map();
                    testResults.set('wholesale-logistics', mockData.wholesaleLogistics);
                    testResults.set('dropship-logistics', mockData.dropshipLogistics);
                    
                    window.uiManager.showResultsBoard(testResults);
                    log('物流信息显示测试完成', 'success');
                    
                    // 检查是否有重复的结果公告板
                    setTimeout(() => {
                        const boards = document.querySelectorAll('.extractor-results-board');
                        if (boards.length === 1) {
                            log('✅ 只有一个结果公告板，无重复显示', 'success');
                        } else {
                            log(`⚠️ 发现 ${boards.length} 个结果公告板，可能存在重复显示`, 'warning');
                        }
                    }, 500);
                    
                } catch (error) {
                    log(`物流信息显示测试失败: ${error.message}`, 'error');
                }
            } else {
                log('UIManager 未找到或方法不可用', 'error');
            }
        }
        
        // 页面加载完成后检查组件状态
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            checkComponentStatus();
        });
        
        // 定期检查组件状态
        setInterval(checkComponentStatus, 5000);
    </script>
</body>
</html>
