/**
 * 代发价格抽取器 - 专门处理代发模式的简化价格结构
 * 支持代发特有的简单价格显示和起订量信息
 * 目标元素: .od-fx-price-pc-price-wrapper 等代发价格相关元素
 * <AUTHOR>
 * @version 1.0.0
 */

class ConsignPriceExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_consign_price_001',
      '代发价格信息',
      '提取1688代发模式页面的价格信息，包括代发价格和最小起订量'
    );
    
    // 抽取器配置
    this.config = {
      timeout: 8000, // 8秒超时
      retryDelay: 1500, // 重试延迟1.5秒
      minPriceValue: 0.01, // 最小价格值
      maxPriceValue: 999999 // 最大价格值
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 主要选择器 - 代发模式特有
      primary: {
        // 代发价格容器
        priceWrapper: [
          '.od-fx-price-pc-price-wrapper', // 代发价格PC包装器
          '.od-fx-price-pc', // 代发价格PC端
          '[class*="od-fx-price"]' // 包含代发价格类名的元素
        ],
        
        // 价格列容器
        priceColumn: [
          '.od-fx-price-pc-price-column', // 代发价格列
          '.price-column', // 价格列
          '.consign-price-column' // 代发价格列
        ],
        
        // 价格盒子
        priceBox: [
          '.od-fx-price-pc-price-box', // 代发价格盒子
          '.price-box', // 价格盒子
          '.consign-price-box' // 代发价格盒子
        ],
        
        // 价格单位
        priceUnit: [
          '.price-unit', // 价格单位
          '.od-fx-price-pc-price-box .price-unit', // 代发价格盒子中的单位
          'span.price-unit' // span价格单位
        ],
        
        // 价格文本
        priceText: [
          '.price-text', // 价格文本
          '.od-fx-price-pc-price-box .price-text', // 代发价格盒子中的文本
          '.price-text strong' // 价格文本中的强调部分
        ],
        
        // 起始文本（可选，某些代发页面可能没有）
        startText: [
          '.start-text', // 起始文本
          '.od-fx-price-pc-price-box .start-text', // 代发价格盒子中的起始文本
          '.price-suffix' // 价格后缀
        ],
        
        // 描述文本（起订量）
        descText: [
          '.desc-text', // 描述文本
          '.od-fx-price-pc-price-column .desc-text' // 价格列中的描述文本
        ]
      },
      
      // 备用选择器
      fallback: {
        // 通用价格选择器
        anyPrice: [
          '[class*="price"] strong', // 包含price类名的强调文本
          '[class*="price"] span', // 包含price类名的span
          '.price' // 通用价格类
        ],
        
        // 通用货币符号
        currency: [
          '.currency', // 货币类
          '.yuan' // 元类
        ],
        
        // 通用起订量
        minOrder: [
          '.min-order' // 最小订单类
        ]
      },
      
      // 上下文选择器
      context: [
        '.od-fx-price-pc-price-wrapper', // 代发价格包装器
        '.od-fx-price-pc', // 代发价格PC端
        '[class*="od-fx-price"]', // 包含代发价格的类
        '.consign-price' // 代发价格
      ],
      
      // 排除选择器
      exclude: [
        '.advertisement',
        '.popup',
        '.modal',
        '.tooltip',
        '.original-price', // 原价
        '.old-price', // 旧价格
        'script',
        'style',
        'noscript'
      ]
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的代发价格信息
   */
  async extract() {
    const selectors = this.getSelectors();
    let priceInfo = null;
    
    try {
      // 第一步：检查是否为代发模式页面
      if (!this.isConsignPage()) {
        throw new Error('当前页面不是代发模式');
      }
      
      // 第二步：提取代发价格容器
      const priceWrapper = await this.findPriceWrapper(selectors.primary.priceWrapper);
      if (!priceWrapper) {
        throw new Error('未找到代发价格容器');
      }
      
      // 记录DOM数据到调试面板
      if (window.debugPanel && typeof window.debugPanel.addDOMData === 'function') {
        window.debugPanel.addDOMData('代发价格', {
          selector: '.od-fx-price-pc-price-wrapper等',
          element: {
            tagName: priceWrapper.tagName,
            className: priceWrapper.className,
            textContent: priceWrapper.textContent ? priceWrapper.textContent.substring(0, 100) + '...' : '',
            innerHTML: priceWrapper.innerHTML ? priceWrapper.innerHTML.substring(0, 200) + '...' : ''
          }
        });
      }
      
      // 第三步：提取代发价格信息
      priceInfo = await this.extractConsignPriceInfo(priceWrapper, selectors);
      
      if (!priceInfo) {
        throw new Error('未能提取到代发价格信息');
      }
      
      // 数据清理和增强
      const enhancedInfo = this.enhanceConsignPriceInfo(priceInfo);
      
      // 记录提取结果到调试面板
      if (window.debugPanel && typeof window.debugPanel.addResult === 'function') {
        window.debugPanel.addResult('代发价格', {
          success: true,
          data: enhancedInfo,
          extractorType: 'consign_price',
          timestamp: Date.now(),
          confidence: enhancedInfo.confidence || 0
        });
      }
      
      console.log('✅ [代发价格调试] 代发价格提取完成，最终数据:', JSON.stringify(enhancedInfo, null, 2));
      
      return enhancedInfo;
      
    } catch (error) {
      console.error('代发价格提取失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否为代发模式页面
   * @returns {boolean} 是否为代发模式
   */
  isConsignPage() {
    const url = window.location.href;
    
    // 首先检查URL中是否包含代发标识
    if (url.includes('sk=consign')) {
      return true;
    }
    
    // 检查是否存在批发特有元素（如果存在则不是代发模式）
    const wholesaleIndicators = [
      '#mainPrice[data-module="od_main_price"]',
      '.module-od-main-price',
      '.price-component.range-price'
    ];
    
    const hasWholesaleElements = wholesaleIndicators.some(selector => {
      return this.safeQuerySelector(selector) !== null;
    });
    
    if (hasWholesaleElements) {
      return false;
    }
    
    // 检查页面中是否存在代发特有元素
    const consignIndicators = [
      '.od-fx-price-pc-price-wrapper',
      '.od-fx-price-pc',
      '[class*="od-fx-price"]'
    ];
    
    return consignIndicators.some(selector => {
      return this.safeQuerySelector(selector) !== null;
    });
  }

  /**
   * 查找价格包装器
   * @param {Array} selectors - 价格包装器选择器列表
   * @returns {Promise<Element|null>} 价格包装器元素
   */
  async findPriceWrapper(selectors) {
    for (const selector of selectors) {
      const element = this.safeQuerySelector(selector);
      if (element && this.isValidPriceWrapper(element)) {
        return element;
      }
    }
    return null;
  }

  /**
   * 验证价格包装器是否有效
   * @param {Element} wrapper - 包装器元素
   * @returns {boolean} 是否有效
   */
  isValidPriceWrapper(wrapper) {
    if (!wrapper) return false;
    
    // 检查包装器是否包含价格相关内容
    const priceIndicators = [
      '.price-unit',
      '.price-text',
      '.price-box',
      'span:contains("¥")',
      'strong'
    ];
    
    return priceIndicators.some(indicator => {
      return this.safeQuerySelector(indicator, wrapper) !== null;
    });
  }

  /**
   * 提取代发价格信息
   * @param {Element} wrapper - 价格包装器
   * @param {Object} selectors - 选择器配置
   * @returns {Promise<Object>} 代发价格信息
   */
  async extractConsignPriceInfo(wrapper, selectors) {
    const priceInfo = {
      price: null, // 代发价格
      startText: null, // 起始文本
      minOrder: null, // 最小起订量
      currency: '¥', // 货币符号
      extractionMethod: 'consign_primary'
    };
    
    try {
      // 提取价格信息
      priceInfo.price = await this.extractPrice(wrapper, selectors.primary);
      
      // 提取起始文本
      priceInfo.startText = await this.extractStartText(wrapper, selectors.primary.startText);
      
      // 提取最小起订量
      priceInfo.minOrder = await this.extractMinOrder(wrapper, selectors.primary.descText);
      
      // 提取货币符号
      priceInfo.currency = await this.extractCurrency(wrapper, selectors.primary.priceUnit) || '¥';
      
      return priceInfo;
      
    } catch (error) {
      console.error('提取代发价格信息时出错:', error);
      return null;
    }
  }

  /**
   * 提取价格信息
   * @param {Element} wrapper - 价格包装器
   * @param {Object} selectors - 主要选择器
   * @returns {Promise<Object|null>} 价格信息
   */
  async extractPrice(wrapper, selectors) {
    // 查找价格盒子
    const priceBox = this.safeQuerySelector('.od-fx-price-pc-price-box', wrapper);
    if (!priceBox) {
      return null;
    }
    
    // 提取价格单位
    const unitElement = this.safeQuerySelector('.price-unit', priceBox);
    const currency = unitElement ? this.getElementText(unitElement) : '¥';
    
    // 提取价格文本
    const priceTextElement = this.safeQuerySelector('.price-text', priceBox);
    if (!priceTextElement) {
      return null;
    }
    
    // 🔥 增强容错处理：支持多种价格格式（v3.1.2）
    let priceValue, integerPart, decimalPart, fullPriceText;
    
    // 方法1：标准格式 - 两个strong元素
    const strongElements = priceTextElement.querySelectorAll('strong');
    if (strongElements.length >= 2) {
      integerPart = this.getElementText(strongElements[0]);
      decimalPart = this.getElementText(strongElements[1]);
      fullPriceText = integerPart + decimalPart;
      priceValue = parseFloat(fullPriceText.replace(/[^\d.]/g, ''));
    }
    // 方法2：单个strong元素
    else if (strongElements.length === 1) {
      fullPriceText = this.getElementText(strongElements[0]);
      priceValue = parseFloat(fullPriceText.replace(/[^\d.]/g, ''));
      const parts = fullPriceText.split('.');
      integerPart = parts[0] || fullPriceText;
      decimalPart = parts[1] ? '.' + parts[1] : '';
    }
    // 方法3：直接从价格文本提取
    else {
      fullPriceText = this.getElementText(priceTextElement);
      // 支持多种价格格式：25.00, 25, ¥25.00, $25, 25元等
      const priceMatch = fullPriceText.match(/([\d,]+(?:\.\d{1,2})?)/g);
      if (priceMatch && priceMatch.length > 0) {
        const cleanPrice = priceMatch[0].replace(/,/g, '');
        priceValue = parseFloat(cleanPrice);
        const parts = cleanPrice.split('.');
        integerPart = parts[0];
        decimalPart = parts[1] ? '.' + parts[1] : '';
      } else {
        return null;
      }
    }
    
    // 验证价格值
    if (isNaN(priceValue) || priceValue < this.config.minPriceValue || priceValue > this.config.maxPriceValue) {
      return null;
    }
    
    return {
      currency: currency,
      value: priceValue,
      integerPart: integerPart || '',
      decimalPart: decimalPart || '',
      displayText: currency + (fullPriceText || priceValue.toString()),
      element: priceTextElement,
      extractMethod: strongElements.length >= 2 ? 'standard' : strongElements.length === 1 ? 'single' : 'text'
    };
  }

  /**
   * 提取起始文本
   * @param {Element} wrapper - 价格包装器
   * @param {Array} selectors - 起始文本选择器
   * @returns {Promise<string|null>} 起始文本
   */
  async extractStartText(wrapper, selectors) {
    for (const selector of selectors) {
      const element = this.safeQuerySelector(selector, wrapper);
      if (element) {
        const text = this.getElementText(element);
        if (text) {
          return text.trim();
        }
      }
    }
    return null;
  }

  /**
   * 提取最小起订量
   * @param {Element} wrapper - 价格包装器
   * @param {Array} selectors - 起订量选择器
   * @returns {Promise<Object|null>} 最小起订量信息
   */
  async extractMinOrder(wrapper, selectors) {
    for (const selector of selectors) {
      const element = this.safeQuerySelector(selector, wrapper);
      if (element) {
        const text = this.getElementText(element);
        if (text && (text.includes('≥') || text.includes('件') || text.includes('个'))) {
          const orderInfo = this.parseMinOrderText(text);
          if (orderInfo) {
            return {
              ...orderInfo,
              originalText: text,
              element: element
            };
          }
        }
      }
    }
    return null;
  }

  /**
   * 提取货币符号
   * @param {Element} wrapper - 价格包装器
   * @param {Array} selectors - 货币符号选择器
   * @returns {Promise<string>} 货币符号
   */
  async extractCurrency(wrapper, selectors) {
    for (const selector of selectors) {
      const element = this.safeQuerySelector(selector, wrapper);
      if (element) {
        const text = this.getElementText(element);
        if (text && /[¥$€£]/.test(text)) {
          return text.trim();
        }
      }
    }
    
    return '¥'; // 默认人民币
  }

  /**
   * 解析最小起订量文本
   * @param {string} text - 起订量文本
   * @returns {Object|null} 起订量信息
   */
  parseMinOrderText(text) {
    if (!text) return null;
    
    // 匹配 ≥1件 格式
    let match = text.match(/≥(\d+)(件|个|批)?/);
    if (match) {
      return {
        quantity: parseInt(match[1]),
        unit: match[2] || '件',
        operator: '≥',
        description: `≥${match[1]}${match[2] || '件'}`
      };
    }
    
    // 匹配其他格式
    match = text.match(/(\d+)(件|个|批)起/);
    if (match) {
      return {
        quantity: parseInt(match[1]),
        unit: match[2],
        operator: '≥',
        description: `${match[1]}${match[2]}起`
      };
    }
    
    return null;
  }

  /**
   * 增强代发价格信息
   * @param {Object} priceInfo - 原始价格信息
   * @returns {Object} 增强后的价格信息
   */
  enhanceConsignPriceInfo(priceInfo) {
    const enhanced = { ...priceInfo };
    
    // 计算价格统计
    enhanced.statistics = this.calculatePriceStatistics(priceInfo);
    
    // 分析价格特征
    enhanced.analysis = this.analyzePriceFeatures(priceInfo);
    
    // 计算可信度
    enhanced.confidence = this.calculateConfidence(priceInfo);
    
    // 格式化显示
    enhanced.display = this.formatPriceDisplay(priceInfo);
    
    // 添加元数据
    enhanced.metadata = {
      extractorType: 'consign_price',
      pageType: 'consign',
      extractionTime: Date.now(),
      isSimplePricing: true
    };
    
    return enhanced;
  }

  /**
   * 计算价格统计
   * @param {Object} priceInfo - 价格信息
   * @returns {Object} 价格统计
   */
  calculatePriceStatistics(priceInfo) {
    return {
      hasPrice: !!priceInfo.price,
      hasStartText: !!priceInfo.startText,
      hasMinOrder: !!priceInfo.minOrder,
      priceValue: priceInfo.price ? priceInfo.price.value : null,
      currency: priceInfo.currency
    };
  }

  /**
   * 分析价格特征
   * @param {Object} priceInfo - 价格信息
   * @returns {Object} 价格特征分析
   */
  analyzePriceFeatures(priceInfo) {
    return {
      pricingModel: 'consign',
      isFixedPrice: true,
      hasVolumeDiscount: false,
      requiresMinimumOrder: !!priceInfo.minOrder,
      complexityLevel: 'low'
    };
  }

  /**
   * 格式化价格显示
   * @param {Object} priceInfo - 价格信息
   * @returns {Object} 格式化显示
   */
  formatPriceDisplay(priceInfo) {
    const display = {
      main: '',
      price: '',
      order: ''
    };
    
    // 主价格显示
    if (priceInfo.price) {
      display.price = priceInfo.price.displayText;
      display.main = display.price;
      
      // 添加起始文本
      if (priceInfo.startText) {
        display.main += ` ${priceInfo.startText}`;
      }
    }
    
    // 起订量显示
    if (priceInfo.minOrder) {
      display.order = priceInfo.minOrder.description;
    }
    
    return display;
  }

  /**
   * 计算可信度
   * @param {Object} priceInfo - 价格信息
   * @returns {number} 可信度 (0-100)
   */
  calculateConfidence(priceInfo) {
    let confidence = 0;
    
    // 基础分数
    confidence += 30;
    
    // 有价格信息加分
    if (priceInfo.price) confidence += 40;
    
    // 有起始文本加分
    if (priceInfo.startText) confidence += 10;
    
    // 有起订量加分
    if (priceInfo.minOrder) confidence += 15;
    
    // 货币符号正确加分
    if (priceInfo.currency === '¥') confidence += 5;
    
    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * 获取元素文本内容
   * @param {Element} element - DOM元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    if (!element) return '';
    return (element.textContent || element.innerText || '').trim();
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 检查是否有有效的价格信息
    if (!data.price || !data.price.value) {
      return false;
    }
    
    // 检查价格值是否在合理范围内
    if (data.price.value < this.config.minPriceValue || data.price.value > this.config.maxPriceValue) {
      return false;
    }
    
    // 检查可信度
    if (data.confidence && data.confidence < 40) {
      return false;
    }
    
    return true;
  }

  /**
   * 数据格式化
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    if (!data) return null;
    
    return {
      ...data,
      currency: data.currency || '¥',
      startText: data.startText ? data.startText.trim() : '',
      formatted: true,
      formatTime: Date.now()
    };
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.ConsignPriceExtractor = ConsignPriceExtractor;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ConsignPriceExtractor;
}