/**
 * URL检测器 - 智能识别1688页面类型
 * 根据URL参数判断批发模式或代发模式
 * <AUTHOR>
 * @version 1.0.0
 */

class URLDetector {
  /**
   * 构造函数
   */
  constructor() {
    this.supportedDomains = [
      'detail.1688.com',
      'www.1688.com'
    ];
    
    this.pageTypes = {
      WHOLESALE: 'wholesale',  // 批发模式
      CONSIGN: 'consign',      // 代发模式
      UNKNOWN: 'unknown'       // 未知类型
    };
    
    this.detectionRules = {
      // 代发模式检测规则
      consign: {
        urlParams: ['sk=consign'],
        pathPatterns: ['/offer/'],
        requiredParams: ['sk']
      },
      
      // 批发模式检测规则
      wholesale: {
        pathPatterns: ['/offer/'],
        excludeParams: ['sk=consign']
      }
    };
  }

  /**
   * 检测当前页面类型
   * @param {string} url - 要检测的URL，默认为当前页面URL
   * @returns {Object} 检测结果
   */
  detectPageType(url = window.location.href) {
    try {
      const urlObj = new URL(url);
      const result = {
        url: url,
        domain: urlObj.hostname,
        pageType: this.pageTypes.UNKNOWN,
        confidence: 0,
        details: {},
        timestamp: Date.now()
      };

      // 检查是否为支持的域名
      if (!this.isSupportedDomain(urlObj.hostname)) {
        result.details.error = 'Unsupported domain';
        return result;
      }

      // 首先检测代发模式（URL参数 + DOM内容检测）
      const consignResult = this.detectConsignMode(urlObj);
      if (consignResult.isMatch && consignResult.confidence >= 70) {
        result.pageType = this.pageTypes.CONSIGN;
        result.confidence = consignResult.confidence;
        result.details = consignResult.details;
        return result;
      }
      
      // 如果URL参数检测失败，尝试DOM内容检测
      const domConsignResult = this.detectConsignModeByDOM();
      if (domConsignResult.isMatch && domConsignResult.confidence >= 60) {
        result.pageType = this.pageTypes.CONSIGN;
        result.confidence = domConsignResult.confidence;
        result.details = { ...consignResult.details, ...domConsignResult.details, detectionMethod: 'DOM_CONTENT' };
        return result;
      }

      // 如果不是明确的代发模式，检测批发模式
      const wholesaleResult = this.detectWholesaleMode(urlObj);
      if (wholesaleResult.isMatch) {
        result.pageType = this.pageTypes.WHOLESALE;
        result.confidence = wholesaleResult.confidence;
        result.details = wholesaleResult.details;
        return result;
      }

      // 如果都不匹配，但是是1688的offer页面，默认为批发模式
      if (urlObj.pathname.includes('/offer/')) {
        result.pageType = this.pageTypes.WHOLESALE;
        result.confidence = 60; // 中等置信度
        result.details = {
          reason: 'Default to wholesale for 1688 offer pages',
          pathMatch: true
        };
        return result;
      }

      // 未匹配到任何模式
      result.details.error = 'No matching page type found';
      return result;

    } catch (error) {
      return {
        url: url,
        pageType: this.pageTypes.UNKNOWN,
        confidence: 0,
        details: { error: error.message },
        timestamp: Date.now()
      };
    }
  }

  /**
   * 检测代发模式
   * @param {URL} urlObj - URL对象
   * @returns {Object} 检测结果
   */
  detectConsignMode(urlObj) {
    const result = {
      isMatch: false,
      confidence: 0,
      details: {
        checkedParams: [],
        matchedParams: [],
        pathMatch: false
      }
    };

    const rules = this.detectionRules.consign;
    let score = 0;
    const maxScore = 100;

    // 检查URL参数
    const params = urlObj.searchParams;
    
    // 检查sk=consign参数（主要标识）
    if (params.has('sk') && params.get('sk') === 'consign') {
      result.details.matchedParams.push('sk=consign');
      score += 70; // 主要标识权重最高
    }
    
    result.details.checkedParams.push('sk');

    // 检查路径模式
    for (const pattern of rules.pathPatterns) {
      if (urlObj.pathname.includes(pattern)) {
        result.details.pathMatch = true;
        score += 20;
        break;
      }
    }

    // 检查其他相关参数
    const additionalParams = ['traceId', 'spm-url', 'spm-auction'];
    let additionalParamCount = 0;
    
    additionalParams.forEach(param => {
      result.details.checkedParams.push(param);
      if (params.has(param)) {
        result.details.matchedParams.push(param);
        additionalParamCount++;
      }
    });
    
    // 额外参数加分
    if (additionalParamCount > 0) {
      score += Math.min(additionalParamCount * 3, 10);
    }

    result.confidence = Math.min(score, maxScore);
    result.isMatch = result.confidence >= 70; // 置信度阈值

    return result;
  }

  /**
   * 基于DOM内容检测代发模式
   * @returns {Object} 检测结果
   */
  detectConsignModeByDOM() {
    const result = {
      isMatch: false,
      confidence: 0,
      details: {
        domIndicators: [],
        logisticsElements: [],
        consignKeywords: []
      }
    };

    let score = 0;
    const maxScore = 100;

    try {
      // 检测物流相关元素（代发页面特有的物流信息）
      const logisticsSelectors = [
        '.od-pc-logistics-contain',
        '.logistics-wrapper',
        '.logistics-content',
        '.logistics-express',
        '.logistics-city',
        '.logistics-express-price'
      ];

      let logisticsElementCount = 0;
      logisticsSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          logisticsElementCount++;
          result.details.logisticsElements.push({
            selector: selector,
            count: elements.length,
            hasContent: Array.from(elements).some(el => el.textContent.trim().length > 0)
          });
        }
      });

      // 物流元素存在加分
      if (logisticsElementCount >= 3) {
        score += 40; // 多个物流元素存在
        result.details.domIndicators.push('Multiple logistics elements found');
      } else if (logisticsElementCount >= 1) {
        score += 20; // 至少有物流元素
        result.details.domIndicators.push('Some logistics elements found');
      }

      // 检测代发相关关键词
      const consignKeywords = ['代发', '一件代发', '代销', '发货地', '物流费用', '运费'];
      const pageText = document.body.textContent || '';
      
      let keywordMatches = 0;
      consignKeywords.forEach(keyword => {
        if (pageText.includes(keyword)) {
          keywordMatches++;
          result.details.consignKeywords.push(keyword);
        }
      });

      // 关键词匹配加分
      if (keywordMatches >= 3) {
        score += 30; // 多个关键词匹配
        result.details.domIndicators.push('Multiple consign keywords found');
      } else if (keywordMatches >= 1) {
        score += 15; // 至少有关键词匹配
        result.details.domIndicators.push('Some consign keywords found');
      }

      // 检测特定的代发页面结构
      const consignStructureSelectors = [
        '.next-select-values em[title]', // 地址选择器
        '.address-cascader',
        'em[title*="区"]', // 包含区的title
        'span.logistics-city'
      ];

      let structureMatches = 0;
      consignStructureSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            structureMatches++;
          }
        } catch (e) {
          // 忽略选择器错误
        }
      });

      // 结构匹配加分
      if (structureMatches >= 2) {
        score += 20;
        result.details.domIndicators.push('Consign page structure detected');
      }

      // 检测是否有运费相关元素
      const shippingSelectors = ['.logistics-express-price', 'span[class*="price"]'];
      let hasShippingInfo = false;
      
      shippingSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          const hasShippingContent = Array.from(elements).some(el => {
            const text = el.textContent.trim();
            return text.includes('¥') || text.includes('运费') || text.includes('免费');
          });
          if (hasShippingContent) {
            hasShippingInfo = true;
          }
        }
      });

      if (hasShippingInfo) {
        score += 10;
        result.details.domIndicators.push('Shipping fee information found');
      }

    } catch (error) {
      console.warn('DOM检测过程中出现错误:', error);
      result.details.error = error.message;
    }

    result.confidence = Math.min(score, maxScore);
    result.isMatch = result.confidence >= 60; // DOM检测的置信度阈值稍低

    console.log('🔍 [URLDetector] DOM内容检测结果:', {
      confidence: result.confidence,
      isMatch: result.isMatch,
      indicators: result.details.domIndicators,
      logisticsElements: result.details.logisticsElements.length,
      keywords: result.details.consignKeywords
    });

    return result;
  }

  /**
   * 检测批发模式
   * @param {URL} urlObj - URL对象
   * @returns {Object} 检测结果
   */
  detectWholesaleMode(urlObj) {
    const result = {
      isMatch: false,
      confidence: 0,
      details: {
        checkedParams: [],
        excludedParams: [],
        pathMatch: false
      }
    };

    const rules = this.detectionRules.wholesale;
    let score = 0;
    const maxScore = 100;

    // 检查路径模式
    for (const pattern of rules.pathPatterns) {
      if (urlObj.pathname.includes(pattern)) {
        result.details.pathMatch = true;
        score += 50; // 路径匹配基础分
        break;
      }
    }

    // 检查排除参数（不应该包含代发标识）
    const params = urlObj.searchParams;
    
    if (params.has('sk') && params.get('sk') === 'consign') {
      result.details.excludedParams.push('sk=consign');
      score -= 80; // 如果包含代发标识，大幅减分
    }
    
    result.details.checkedParams.push('sk');

    // 检查批发模式常见参数
    const wholesaleParams = ['offerId', 'spm'];
    let wholesaleParamCount = 0;
    
    wholesaleParams.forEach(param => {
      result.details.checkedParams.push(param);
      if (params.has(param)) {
        wholesaleParamCount++;
      }
    });
    
    // 批发参数加分
    if (wholesaleParamCount > 0) {
      score += wholesaleParamCount * 15;
    }

    // 检查URL结构的完整性
    if (urlObj.pathname.match(/\/offer\/\d+\.html/)) {
      score += 20; // URL结构符合1688商品页面格式
    }

    result.confidence = Math.max(0, Math.min(score, maxScore));
    result.isMatch = result.confidence >= 50 && score > 0; // 置信度阈值

    return result;
  }

  /**
   * 检查是否为支持的域名
   * @param {string} hostname - 主机名
   * @returns {boolean} 是否支持
   */
  isSupportedDomain(hostname) {
    return this.supportedDomains.some(domain => 
      hostname === domain || hostname.endsWith('.' + domain)
    );
  }

  /**
   * 获取页面类型的显示名称
   * @param {string} pageType - 页面类型
   * @returns {string} 显示名称
   */
  getPageTypeDisplayName(pageType) {
    const displayNames = {
      [this.pageTypes.WHOLESALE]: '批发模式',
      [this.pageTypes.CONSIGN]: '代发模式',
      [this.pageTypes.UNKNOWN]: '未知类型'
    };
    
    return displayNames[pageType] || '未知类型';
  }

  /**
   * 获取页面类型的图标
   * @param {string} pageType - 页面类型
   * @returns {string} 图标字符
   */
  getPageTypeIcon(pageType) {
    const icons = {
      [this.pageTypes.WHOLESALE]: '🏪',
      [this.pageTypes.CONSIGN]: '📦',
      [this.pageTypes.UNKNOWN]: '❓'
    };
    
    return icons[pageType] || '❓';
  }

  /**
   * 验证检测结果
   * @param {Object} result - 检测结果
   * @returns {boolean} 是否有效
   */
  validateResult(result) {
    return result && 
           typeof result.pageType === 'string' &&
           typeof result.confidence === 'number' &&
           result.confidence >= 0 && result.confidence <= 100;
  }

  /**
   * 获取推荐的抽取器模块
   * @param {string} pageType - 页面类型
   * @returns {Array} 推荐的模块ID列表
   */
  getRecommendedExtractors(pageType) {
    const extractorMap = {
      [this.pageTypes.WHOLESALE]: [
        '1688_wholesale_merchant_001',
        '1688_wholesale_product_001',
        '1688_wholesale_price_001'
      ],
      [this.pageTypes.CONSIGN]: [
        '1688_consign_merchant_001',
        '1688_consign_product_001',
        '1688_consign_price_001'
      ]
    };
    
    return extractorMap[pageType] || [];
  }

  /**
   * 生成检测报告
   * @param {Object} result - 检测结果
   * @returns {string} 格式化的报告
   */
  generateReport(result) {
    if (!this.validateResult(result)) {
      return 'Invalid detection result';
    }

    const lines = [
      `页面类型: ${this.getPageTypeDisplayName(result.pageType)} ${this.getPageTypeIcon(result.pageType)}`,
      `置信度: ${result.confidence}%`,
      `检测时间: ${new Date(result.timestamp).toLocaleString()}`
    ];

    if (result.details) {
      if (result.details.matchedParams && result.details.matchedParams.length > 0) {
        lines.push(`匹配参数: ${result.details.matchedParams.join(', ')}`);
      }
      
      if (result.details.pathMatch) {
        lines.push('路径匹配: ✓');
      }
      
      if (result.details.error) {
        lines.push(`错误: ${result.details.error}`);
      }
    }

    return lines.join('\n');
  }
}

// 创建全局实例
if (typeof window !== 'undefined') {
  window.URLDetector = URLDetector;
  window.urlDetector = new URLDetector();
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = URLDetector;
}