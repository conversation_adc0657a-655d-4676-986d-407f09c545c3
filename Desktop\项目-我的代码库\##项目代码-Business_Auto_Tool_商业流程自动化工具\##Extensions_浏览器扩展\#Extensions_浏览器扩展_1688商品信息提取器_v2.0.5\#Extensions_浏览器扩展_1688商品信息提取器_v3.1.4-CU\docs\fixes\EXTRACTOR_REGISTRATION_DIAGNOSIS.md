# 抽取器注册问题诊断与修复

## 🎯 问题描述

用户报告扩展程序运行时出现以下问题：
- ✅ **4个模块正常工作**：图片、规格、价格、评价
- ❌ **3个模块失败**：标题、商家、商品信息
- 错误信息：`Extractor 1688_wholesale_product_info_001 failed after 3 attempts`

## 🔍 问题分析

### 通过Playwright模拟测试发现：

1. **页面元素完全正常**：
   - 商品标题：`"日系环保托特包学生包万针刺绣购物袋小众新款百搭单肩包女"`
   - 商家名称：`"义乌市品悟电子商务商行"`
   - 所有CSS选择器都能正确匹配元素

2. **抽取逻辑正确**：
   - 模拟的抽取器逻辑完全正常
   - 数据应该能正常显示

3. **真正的问题**：
   - 问题不在页面结构或选择器
   - 问题在于**扩展程序的模块注册或数据流传递**环节

## 🔧 诊断方案

### 创建了抽取器注册诊断脚本

文件：`temp/extractor_registration_diagnosis.js`

**功能**：
- 检查全局抽取器类是否正确注册
- 验证ExtractionManager状态
- 列出已注册的抽取器
- 检查缺失的关键抽取器
- 提供修复建议

**检查的抽取器类**：
- `WholesaleProductTitleExtractor`
- `WholesaleMerchantExtractor`
- `WholesaleProductInfoExtractor`
- `WholesaleProductImagesExtractor`
- `WholesaleProductSpecsExtractor`
- `WholesaleProductRatingExtractor`
- `WholesalePriceExtractor`

## 🚀 使用方法

1. **重新加载扩展程序**
2. **访问1688批发页面**
3. **打开开发者工具控制台**
4. **查看诊断输出**：
   ```
   🔍 [注册诊断] ========== 开始抽取器注册诊断 ==========
   ✅ [注册诊断] WholesaleProductTitleExtractor: 已注册
   ❌ [注册诊断] WholesaleMerchantExtractor: 未注册
   ...
   ```

5. **检查诊断结果**：
   ```javascript
   console.log(window.extractorDiagnosis);
   ```

## 🔍 预期发现

根据症状分析，可能的问题：

1. **脚本加载顺序问题**：
   - 某些抽取器文件可能没有正确加载
   - manifest.json中的脚本顺序可能有问题

2. **全局注册失败**：
   - 抽取器类没有正确注册到window对象
   - 类定义可能有语法错误

3. **ExtractionManager注册逻辑问题**：
   - 页面类型检测可能有问题
   - 条件判断可能导致某些抽取器未注册

## 🔧 可能的修复方案

### 方案1：检查脚本加载
```javascript
// 在控制台中检查
console.log('WholesaleProductTitleExtractor:', typeof window.WholesaleProductTitleExtractor);
console.log('WholesaleMerchantExtractor:', typeof window.WholesaleMerchantExtractor);
```

### 方案2：手动注册缺失的抽取器
```javascript
// 如果类存在但未注册到ExtractionManager
if (window.extractionManager && window.WholesaleProductTitleExtractor) {
  const extractor = new window.WholesaleProductTitleExtractor();
  window.extractionManager.extractors.set(extractor.moduleId, extractor);
}
```

### 方案3：重新初始化ExtractionManager
```javascript
// 强制重新注册所有抽取器
if (window.extractionManager) {
  window.extractionManager.extractors.clear();
  window.extractionManager.registerExtractors();
}
```

## 📊 版本信息

- **扩展版本**: 3.1.1
- **诊断脚本**: v1.0.0
- **修复日期**: 2025-09-10
- **测试页面**: https://detail.1688.com/offer/772460660597.html

## 🎯 下一步

1. **运行诊断脚本**确认问题根源
2. **根据诊断结果**实施相应修复
3. **验证修复效果**
4. **更新文档**记录最终解决方案

---

**注意**: 这个诊断脚本已添加到manifest.json中，会在扩展程序加载时自动运行。
