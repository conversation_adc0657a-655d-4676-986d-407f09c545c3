# 1688商品信息提取器 - API文档

**版本**: v3.1.0
**API版本**: 1.0
**更新时间**: 2025年1月

---

## 📋 目录

- [概述](#概述)
- [核心API](#核心api)
  - [BaseExtractor](#baseextractor)
  - [ResultFormatter](#resultformatter)
  - [UIManager](#uimanager)
  - [ExtractionManager](#extractionmanager)
  - [LoggerManager](#loggermanager)
- [数据结构](#数据结构)
- [事件系统](#事件系统)
- [错误处理](#错误处理)
- [扩展接口](#扩展接口)

---

## 🎯 概述

### API设计原则
- **一致性**: 所有API遵循统一的命名和调用约定
- **可扩展性**: 支持插件化扩展和自定义实现
- **类型安全**: 提供完整的类型定义和验证
- **向后兼容**: 保持API的向后兼容性

### 版本兼容性
| API版本 | 扩展版本 | 兼容性 | 说明 |
|---------|----------|--------|------|
| 1.0 | v3.1.0+ | ✅ 当前 | 简化架构后的统一API |
| 0.9 | v3.0.x | ⚠️ 废弃 | 委托系统API，已移除 |
| 0.8 | v2.x | ❌ 不兼容 | 旧版架构API |

---

## 🔧 核心API

### BaseExtractor

所有数据提取器的基类，提供统一的接口和通用功能。

#### 构造函数

```javascript
constructor(moduleId, name, description)
```

**参数**:
- `moduleId` (string): 唯一标识符，格式: `1688_{mode}_{type}_{version}`
- `name` (string): 显示名称
- `description` (string): 功能描述

**示例**:
```javascript
class MyExtractor extends BaseExtractor {
  constructor() {
    super(
      '1688_wholesale_custom_001',
      '自定义提取器',
      '提取自定义数据'
    );
  }
}
```

#### 核心方法

##### extract()

主要的数据提取方法，必须由子类实现。

```javascript
async extract(): Promise<ExtractionResult>
```

**返回值**: `ExtractionResult`
```javascript
{
  success: boolean,        // 提取是否成功
  data: any,              // 提取的数据
  confidence: number,     // 置信度 (0-100)
  timestamp: number,      // 提取时间戳
  metadata?: any          // 可选的元数据
}
```

**示例**:
```javascript
async extract() {
  try {
    const selectors = this.getSelectors();
    const rawData = await this.extractRawData(selectors);
    const processedData = this.processData(rawData);
    
    return {
      success: true,
      data: processedData,
      confidence: this.calculateConfidence(processedData),
      timestamp: Date.now()
    };
  } catch (error) {
    this.handleError(error);
    throw error;
  }
}
```

##### getSelectors()

获取CSS选择器配置。

```javascript
getSelectors(): SelectorConfig
```

**返回值**: `SelectorConfig`
```javascript
{
  primary: {
    [key: string]: string[]    // 主要选择器
  },
  fallback?: {
    [key: string]: string[]    // 备用选择器
  },
  context?: {
    [key: string]: string[]    // 上下文选择器
  }
}
```

**示例**:
```javascript
getSelectors() {
  return {
    primary: {
      container: ['.main-container', '#content'],
      title: ['h1.title', '.product-title'],
      price: ['.price-current', '.price-value']
    },
    fallback: {
      container: ['.fallback-container'],
      title: ['h2', '.backup-title']
    }
  };
}
```

##### validate(data)

验证提取的数据是否有效。

```javascript
validate(data: any): ValidationResult
```

**参数**:
- `data` (any): 待验证的数据

**返回值**: `ValidationResult`
```javascript
{
  isValid: boolean,        // 是否有效
  errors: string[],        // 错误列表
  warnings: string[]       // 警告列表
}
```

##### format(data)

格式化输出数据。

```javascript
format(data: any): FormattedData
```

#### 工具方法

##### safeQuerySelector(selector, context?)

安全的DOM查询方法。

```javascript
safeQuerySelector(selector: string, context?: Element): Element | null
```

##### safeQuerySelectorAll(selector, context?)

安全的DOM批量查询方法。

```javascript
safeQuerySelectorAll(selector: string, context?: Element): NodeList
```

##### getElementText(element)

获取元素的文本内容。

```javascript
getElementText(element: Element): string
```

---

### ResultFormatter

v3.1.0新增的统一结果格式化器。

#### 构造函数

```javascript
constructor()
```

自动初始化格式化器配置。

#### 主要方法

##### formatResultItem(extractorId, result, pageType)

格式化单个结果项。

```javascript
formatResultItem(extractorId: string, result: ExtractionResult, pageType?: string): string
```

**参数**:
- `extractorId` (string): 提取器ID
- `result` (ExtractionResult): 提取结果
- `pageType` (string): 页面类型 ('wholesale' | 'consign' | 'default')

**返回值**: 格式化的HTML字符串

**示例**:
```javascript
const formatter = new ResultFormatter();
const html = formatter.formatResultItem(
  'price_extractor',
  {
    success: true,
    data: { price: { value: 99.99 }, currency: 'CNY' },
    confidence: 85
  },
  'wholesale'
);
```

##### formatTitleResult(data, config)

格式化标题数据。

```javascript
formatTitleResult(data: TitleData, config: FormatterConfig): string
```

##### formatPriceResult(data, config, pageType)

格式化价格数据。

```javascript
formatPriceResult(data: PriceData, config: FormatterConfig, pageType: string): string
```

##### formatSpecsResult(data, config)

格式化规格数据。

```javascript
formatSpecsResult(data: SpecsData, config: FormatterConfig): string
```

#### 配置接口

```javascript
interface FormatterConfig {
  priceFormat: string;        // 价格格式
  showBatchInfo: boolean;     // 显示批量信息
  keywordLimit: number;       // 关键词限制
  priceLabel: string;         // 价格标签
  specialFields: string[];    // 特殊字段
}
```

---

### UIManager

用户界面管理器，负责所有UI组件的创建和管理。

#### 构造函数

```javascript
constructor(loggerManager: LoggerManager)
```

#### 主要方法

##### showProgressToast(message, icon?)

显示进度提示。

```javascript
showProgressToast(message: string, icon?: string): void
```

**参数**:
- `message` (string): 提示消息
- `icon` (string): 图标，默认为 '⚡'

##### showResultsBoard(results)

显示结果面板。

```javascript
showResultsBoard(results: Map<string, ExtractionResult>): void
```

##### showErrorToast(message, title?)

显示错误提示。

```javascript
showErrorToast(message: string, title?: string): void
```

##### showSuccessToast(message, title?)

显示成功提示。

```javascript
showSuccessToast(message: string, title?: string): void
```

##### updateStatusIndicator(status, message)

更新状态指示器。

```javascript
updateStatusIndicator(status: StatusType, message: string): void
```

**StatusType**:
```javascript
type StatusType = 'idle' | 'extracting' | 'success' | 'error' | 'warning'
```

##### hideAllUI()

隐藏所有UI元素。

```javascript
hideAllUI(): void
```

##### cleanup()

清理UI资源。

```javascript
cleanup(): void
```

---

### ExtractionManager

提取管理器，协调所有数据提取器的运行。

#### 构造函数

```javascript
constructor(loggerManager: LoggerManager, uiManager: UIManager)
```

#### 主要方法

##### startExtraction()

开始数据提取。

```javascript
async startExtraction(): Promise<Map<string, ExtractionResult>>
```

##### stopExtraction()

停止数据提取。

```javascript
async stopExtraction(): Promise<void>
```

##### resetExtraction()

重置提取状态。

```javascript
async resetExtraction(): Promise<void>
```

##### registerExtractor(extractor)

注册新的提取器。

```javascript
registerExtractor(extractor: BaseExtractor): void
```

##### getExtractor(moduleId)

获取指定的提取器。

```javascript
getExtractor(moduleId: string): BaseExtractor | null
```

##### getStats()

获取提取统计信息。

```javascript
getStats(): ExtractionStats
```

**ExtractionStats**:
```javascript
{
  totalExtractors: number,     // 总提取器数量
  successCount: number,        // 成功数量
  failureCount: number,        // 失败数量
  averageConfidence: number,   // 平均置信度
  totalTime: number           // 总耗时
}
```

---

### LoggerManager

日志管理器，提供分级日志记录功能。

#### 构造函数

```javascript
constructor()
```

#### 主要方法

##### debug(module, message, data?)

记录调试日志。

```javascript
debug(module: string, message: string, data?: any): void
```

##### info(module, message, data?)

记录信息日志。

```javascript
info(module: string, message: string, data?: any): void
```

##### warn(module, message, data?)

记录警告日志。

```javascript
warn(module: string, message: string, data?: any): void
```

##### error(module, message, error?)

记录错误日志。

```javascript
error(module: string, message: string, error?: Error): void
```

##### setLevel(level)

设置日志级别。

```javascript
setLevel(level: LogLevel): void
```

**LogLevel**:
```javascript
type LogLevel = 'debug' | 'info' | 'warn' | 'error'
```

---

## 📊 数据结构

### ExtractionResult

提取结果的标准数据结构。

```javascript
interface ExtractionResult {
  success: boolean;           // 是否成功
  data: any;                 // 提取的数据
  confidence: number;        // 置信度 (0-100)
  timestamp: number;         // 时间戳
  metadata?: {
    selector?: string;       // 使用的选择器
    method?: string;         // 提取方法
    retryCount?: number;     // 重试次数
    [key: string]: any;      // 其他元数据
  };
}
```

### TitleData

标题数据结构。

```javascript
interface TitleData {
  title: string;             // 主标题
  subtitle?: string;         // 副标题
  keywords?: Array<{
    value: string;           // 关键词值
    weight?: number;         // 权重
  }>;
  salesInfo?: {
    salesText: string;       // 销售信息文本
    salesCount?: number;     // 销售数量
  };
}
```

### PriceData

价格数据结构。

```javascript
interface PriceData {
  price?: {
    value: number | string;  // 价格值
    currency?: string;       // 货币类型
  };
  couponPrice?: {
    value: number | string;
    originalPrice?: number | string;
  };
  priceRange?: Array<{
    min: number;
    max: number;
    quantity?: number;       // 对应数量
  }>;
  minBatch?: {
    quantity: number;        // 起批量
    unit: string;           // 单位
  };
  retailPrice?: number;     // 建议零售价
  profit?: number;          // 预估利润
}
```

### SpecsData

规格数据结构。

```javascript
interface SpecsData {
  attributes?: Record<string, string>;     // 基本属性
  specs?: Record<string, string>;          // 规格参数
  specifications?: Record<string, string>; // 详细规格
  variants?: Array<{
    name: string;                          // 变体名称
    options: string[];                     // 选项列表
  }>;
  materials?: string[];                    // 材质信息
  colors?: Array<{
    name: string;
    value: string;                         // 颜色值
  }>;
  sizes?: string[];                        // 尺寸信息
}
```

### RatingData

评价数据结构。

```javascript
interface RatingData {
  rating?: number;           // 评分
  score?: number;            // 得分
  reviewCount?: number;      // 评价数量
  salesCount?: number;       // 销售数量
  positiveRate?: number;     // 好评率
  reviews?: Array<{
    rating: number;
    comment: string;
    date?: string;
    user?: string;
  }>;
}
```

---

## 🔔 事件系统

### 事件类型

```javascript
type EventType = 
  | 'extraction:start'       // 开始提取
  | 'extraction:progress'    // 提取进度
  | 'extraction:complete'    // 提取完成
  | 'extraction:error'       // 提取错误
  | 'ui:show'               // UI显示
  | 'ui:hide'               // UI隐藏
  | 'data:export'           // 数据导出
  | 'logger:message';       // 日志消息
```

### 事件监听

```javascript
// 监听提取开始事件
window.addEventListener('extraction:start', (event) => {
  console.log('Extraction started:', event.detail);
});

// 监听提取完成事件
window.addEventListener('extraction:complete', (event) => {
  const { results, stats } = event.detail;
  console.log('Extraction completed:', results, stats);
});
```

### 事件触发

```javascript
// 触发自定义事件
function dispatchExtractionEvent(type, detail) {
  const event = new CustomEvent(type, { detail });
  window.dispatchEvent(event);
}

// 示例
dispatchExtractionEvent('extraction:start', {
  extractorCount: 6,
  pageType: 'wholesale'
});
```

---

## ❌ 错误处理

### 错误类型

```javascript
class ExtractionError extends Error {
  constructor(message, code, details) {
    super(message);
    this.name = 'ExtractionError';
    this.code = code;
    this.details = details;
  }
}

class ValidationError extends Error {
  constructor(message, field, value) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
    this.value = value;
  }
}

class UIError extends Error {
  constructor(message, component) {
    super(message);
    this.name = 'UIError';
    this.component = component;
  }
}
```

### 错误码

```javascript
const ERROR_CODES = {
  // 提取错误 (1000-1999)
  SELECTOR_NOT_FOUND: 1001,
  DATA_EXTRACTION_FAILED: 1002,
  TIMEOUT_ERROR: 1003,
  NETWORK_ERROR: 1004,
  
  // 验证错误 (2000-2999)
  INVALID_DATA_FORMAT: 2001,
  MISSING_REQUIRED_FIELD: 2002,
  DATA_TYPE_MISMATCH: 2003,
  
  // UI错误 (3000-3999)
  UI_COMPONENT_NOT_FOUND: 3001,
  RENDER_ERROR: 3002,
  EVENT_HANDLER_ERROR: 3003,
  
  // 系统错误 (4000-4999)
  MODULE_INITIALIZATION_FAILED: 4001,
  PERMISSION_DENIED: 4002,
  RESOURCE_NOT_AVAILABLE: 4003
};
```

### 错误处理示例

```javascript
try {
  const result = await extractor.extract();
  return result;
} catch (error) {
  if (error instanceof ExtractionError) {
    switch (error.code) {
      case ERROR_CODES.SELECTOR_NOT_FOUND:
        // 尝试备用选择器
        return await this.tryFallbackSelectors();
      
      case ERROR_CODES.TIMEOUT_ERROR:
        // 重试提取
        return await this.retryExtraction();
      
      default:
        // 记录错误并返回默认值
        this.logger.error('Extractor', error.message, error);
        return this.getDefaultResult();
    }
  }
  
  // 未知错误，重新抛出
  throw error;
}
```

---

## 🔌 扩展接口

### 插件接口

```javascript
interface Plugin {
  name: string;
  version: string;
  description: string;
  
  // 生命周期方法
  onInstall?(): void;
  onEnable?(): void;
  onDisable?(): void;
  onUninstall?(): void;
  
  // 扩展点
  extendExtractor?(extractor: BaseExtractor): void;
  extendFormatter?(formatter: ResultFormatter): void;
  extendUI?(uiManager: UIManager): void;
}
```

### 中间件接口

```javascript
interface Middleware {
  name: string;
  
  // 处理方法
  process(data: any, context: any): Promise<any>;
  
  // 可选的生命周期方法
  beforeProcess?(data: any, context: any): void;
  afterProcess?(result: any, context: any): void;
  onError?(error: Error, context: any): void;
}
```

### 自定义格式化器

```javascript
interface CustomFormatter {
  name: string;
  supportedTypes: string[];
  
  format(data: any, config: any): string;
  validate?(data: any): boolean;
}

// 注册自定义格式化器
ResultFormatter.registerCustomFormatter({
  name: 'customPrice',
  supportedTypes: ['price', 'cost'],
  
  format(data, config) {
    return `<div class="custom-price">¥${data.value}</div>`;
  },
  
  validate(data) {
    return data && typeof data.value === 'number';
  }
});
```

### 钩子系统

```javascript
// 注册钩子
HookManager.register('before:extraction', (context) => {
  console.log('Before extraction:', context);
});

HookManager.register('after:extraction', (result, context) => {
  console.log('After extraction:', result);
});

// 触发钩子
HookManager.trigger('before:extraction', { pageType: 'wholesale' });
```

---

## 📝 使用示例

### 创建自定义提取器

```javascript
class CustomProductExtractor extends BaseExtractor {
  constructor() {
    super(
      '1688_wholesale_custom_product_001',
      '自定义商品提取器',
      '提取自定义商品信息'
    );
  }
  
  getSelectors() {
    return {
      primary: {
        container: ['.product-info', '.item-details'],
        name: ['.product-name', 'h1.title'],
        description: ['.product-desc', '.description']
      },
      fallback: {
        container: ['.main-content'],
        name: ['h2', '.backup-title']
      }
    };
  }
  
  async extract() {
    const selectors = this.getSelectors();
    
    try {
      // 查找容器
      const container = this.safeQuerySelector(
        selectors.primary.container.join(',')
      );
      
      if (!container) {
        throw new ExtractionError(
          'Product container not found',
          ERROR_CODES.SELECTOR_NOT_FOUND
        );
      }
      
      // 提取数据
      const name = this.extractProductName(container, selectors);
      const description = this.extractDescription(container, selectors);
      
      const data = {
        name,
        description,
        extractedAt: new Date().toISOString()
      };
      
      // 验证数据
      const validation = this.validate(data);
      if (!validation.isValid) {
        throw new ValidationError(
          `Validation failed: ${validation.errors.join(', ')}`,
          'data',
          data
        );
      }
      
      return {
        success: true,
        data: this.format(data),
        confidence: this.calculateConfidence(data),
        timestamp: Date.now()
      };
      
    } catch (error) {
      this.logger.error('CustomProductExtractor', 'Extraction failed', error);
      throw error;
    }
  }
  
  extractProductName(container, selectors) {
    const nameElement = container.querySelector(
      selectors.primary.name.join(',')
    );
    
    return nameElement ? this.getElementText(nameElement).trim() : null;
  }
  
  extractDescription(container, selectors) {
    const descElement = container.querySelector(
      selectors.primary.description.join(',')
    );
    
    return descElement ? this.getElementText(descElement).trim() : null;
  }
  
  validate(data) {
    const errors = [];
    const warnings = [];
    
    if (!data.name) {
      errors.push('Product name is required');
    }
    
    if (!data.description) {
      warnings.push('Product description is missing');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
  
  format(data) {
    return {
      productName: data.name,
      productDescription: data.description || 'No description available',
      metadata: {
        extractedAt: data.extractedAt,
        extractor: this.moduleId
      }
    };
  }
  
  calculateConfidence(data) {
    let confidence = 0;
    
    if (data.name) confidence += 60;
    if (data.description) confidence += 40;
    
    return Math.min(confidence, 100);
  }
}

// 注册提取器
if (typeof window !== 'undefined') {
  window.CustomProductExtractor = CustomProductExtractor;
}
```

### 使用ResultFormatter

```javascript
// 创建格式化器实例
const formatter = new ResultFormatter();

// 格式化提取结果
const result = {
  success: true,
  data: {
    title: 'iPhone 14 Pro Max',
    price: { value: 8999, currency: 'CNY' },
    specs: {
      '屏幕尺寸': '6.7英寸',
      '存储容量': '256GB',
      '颜色': '深空黑色'
    }
  },
  confidence: 95
};

const html = formatter.formatResultItem(
  'product_extractor',
  result,
  'wholesale'
);

console.log(html);
```

---

*API文档版本: v3.1.0*
*更新时间: 2025年1月*
*维护团队: 1688商品信息提取器开发团队*