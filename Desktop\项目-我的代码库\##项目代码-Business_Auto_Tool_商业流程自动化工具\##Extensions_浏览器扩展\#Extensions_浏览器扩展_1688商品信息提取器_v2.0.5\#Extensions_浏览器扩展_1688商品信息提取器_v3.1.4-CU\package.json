{"name": "1688-product-extractor", "version": "3.1.1", "description": "智能提取1688商品信息的模块化Chrome插件，支持批发和代发模式自动识别", "main": "manifest.json", "scripts": {"test": "echo \"Running extension tests...\" && node temp/test_consign_fix_validation.js", "lint": "echo \"Running linter...\"", "build": "echo \"Building extension...\"", "package": "zip -r extension.zip . -x temp/\\* docs/\\* node_modules/\\* .git/\\* .gitignore package.json", "dev": "echo \"Development mode - Load unpacked extension in Chrome\"", "clean": "rm -rf temp/*.log && echo \"Cleaned temporary files\""}, "keywords": ["chrome-extension", "1688", "product-extractor", "web-scraping", "data-extraction"], "author": "Extension Developer", "license": "MIT", "repository": {"type": "git", "url": "local"}, "devDependencies": {"web-ext": "^7.0.0"}, "engines": {"node": ">=16.0.0"}}