/**
 * 商品标题抽取器 - 通用模块
 * 支持批发和代发两种模式的商品标题信息提取
 * 目标元素: .title-text 等标题相关元素
 * <AUTHOR>
 * @version 1.0.0
 */

class ConsignProductTitleExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_consign_product_title_001',
      '代发商品标题',
      '提取1688代发模式页面的完整标题信息'
    );
    
    // 抽取器配置
    this.config = {
      timeout: 8000, // 8秒超时
      retryDelay: 1500, // 重试延迟1.5秒
      maxTextLength: 500, // 最大文本长度
      minTextLength: 5, // 最小文本长度
      maxTitleParts: 5 // 最大标题片段数
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 主要选择器 - 优先级从高到低，更精确的标题选择器
      primary: [
        '.od-pc-offer-title-contain .title-text:first-child', // PC端标题的第一个子元素
        '.title-content .title-text:not([class*="evaluate"]):not([class*="rating"]):not([class*="sales"])', // 排除评价相关的标题
        '.title-first-column .title-text:first-of-type', // 第一列标题的第一个
        '.title-second-column .title-text:first-of-type', // 第二列标题的第一个
        '.product-title .title-text:only-child', // 产品标题的唯一子元素
        '.offer-title .title-text:only-child', // 商品标题的唯一子元素
        'h1.title-text', // H1标题
        'h2.title-text' // H2标题
      ],
      
      // 备用选择器
      fallback: [
        '.title-content div', // 标题内容区域
        '.od-pc-offer-title-contain div', // PC端标题容器
        '.product-name', // 产品名称
        '.offer-name', // 商品名称
        '.item-title', // 项目标题
        'h2.title', // H2标题
        'h3.title' // H3标题
      ],
      
      // 上下文选择器 - 用于缩小搜索范围
      context: [
        '.od-pc-offer-title-contain', // PC端标题容器
        '.title-content', // 标题内容
        '.product-info', // 产品信息
        '.offer-info', // 商品信息
        '.item-info', // 项目信息
        '.main-content', // 主要内容
        '.product-header' // 产品头部
      ],
      
      // 排除选择器 - 避免误选
      exclude: [
        '.advertisement',
        '.ad-content',
        '.popup',
        '.modal',
        '.tooltip',
        '.breadcrumb', // 面包屑导航
        '.navigation', // 导航
        '.menu', // 菜单
        '.sidebar', // 侧边栏
        '.footer', // 页脚
        'script',
        'style',
        'noscript'
      ]
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的标题信息
   */
  async extract() {
    const selectors = this.getSelectors();
    let titleInfo = null;
    
    try {
      // 第一步：尝试主要选择器
      titleInfo = await this.extractWithPrimarySelectors(selectors.primary);
      
      if (!titleInfo) {
        // 第二步：尝试在上下文中查找
        titleInfo = await this.extractWithContextSelectors(selectors.context, selectors.primary);
      }
      
      if (!titleInfo) {
        // 第三步：尝试备用选择器
        titleInfo = await this.extractWithFallbackSelectors(selectors.fallback);
      }
      
      if (!titleInfo) {
        // 第四步：智能搜索标题元素
        titleInfo = await this.intelligentTitleSearch();
      }
      
      if (!titleInfo) {
        throw new Error('未找到商品标题元素');
      }
      
      // 记录DOM数据到调试面板
      if (window.debugPanel && typeof window.debugPanel.addDOMData === 'function') {
        const titleElements = document.querySelectorAll('.title-text, .od-pc-offer-title-contain, .title-content');
        if (titleElements.length > 0) {
          window.debugPanel.addDOMData('商品标题', {
            selector: '.title-text等',
            count: titleElements.length,
            elements: Array.from(titleElements).slice(0, 3).map(el => ({
              tagName: el.tagName,
              className: el.className,
              textContent: el.textContent ? el.textContent.substring(0, 100) + '...' : ''
            }))
          });
        }
      }
      
      // 数据清理和增强
      const enhancedInfo = this.enhanceTitleInfo(titleInfo);
      
      // 记录提取结果到调试面板
      if (window.debugPanel && typeof window.debugPanel.addResult === 'function') {
        window.debugPanel.addResult('商品标题', {
          success: true,
          data: enhancedInfo,
          extractorType: 'consign_product_title',
          timestamp: Date.now(),
          confidence: enhancedInfo.confidence || 0
        });
      }
      
      console.log('✅ [商品标题调试] 商品标题提取完成，最终数据:', JSON.stringify(enhancedInfo, null, 2));
      
      return enhancedInfo;
      
    } catch (error) {
      console.error('商品标题提取失败:', error);
      throw error;
    }
  }

  /**
   * 使用主要选择器提取
   * @param {Array} selectors - 选择器列表
   * @returns {Promise<Object|null>} 标题信息
   */
  async extractWithPrimarySelectors(selectors) {
    for (const selector of selectors) {
      try {
        const elements = this.safeQuerySelectorAll(selector);
        if (elements.length > 0) {
          const titleData = this.extractTitleFromElements(elements, selector, 'primary');
          if (titleData) {
            return titleData;
          }
        }
      } catch (error) {
        console.debug(`主选择器 ${selector} 查询失败:`, error.message);
        continue;
      }
    }
    return null;
  }

  /**
   * 在上下文中查找
   * @param {Array} contextSelectors - 上下文选择器
   * @param {Array} targetSelectors - 目标选择器
   * @returns {Promise<Object|null>} 标题信息
   */
  async extractWithContextSelectors(contextSelectors, targetSelectors) {
    for (const contextSelector of contextSelectors) {
      const contextElement = this.safeQuerySelector(contextSelector);
      if (!contextElement) continue;
      
      for (const targetSelector of targetSelectors) {
        const elements = this.safeQuerySelectorAll(targetSelector, contextElement);
        if (elements.length > 0) {
          const titleData = this.extractTitleFromElements(elements, `${contextSelector} ${targetSelector}`, 'context');
          if (titleData) {
            return titleData;
          }
        }
      }
    }
    return null;
  }

  /**
   * 使用备用选择器提取
   * @param {Array} selectors - 备用选择器列表
   * @returns {Promise<Object|null>} 标题信息
   */
  async extractWithFallbackSelectors(selectors) {
    for (const selector of selectors) {
      const elements = this.safeQuerySelectorAll(selector);
      
      if (elements.length > 0) {
        const titleData = this.extractTitleFromElements(elements, selector, 'fallback');
        if (titleData) {
          return titleData;
        }
      }
    }
    return null;
  }

  /**
   * 智能搜索标题元素
   * @returns {Promise<Object|null>} 标题信息
   */
  async intelligentTitleSearch() {
    // 搜索可能包含标题的元素
    const possibleSelectors = [
      'div[class*="title"]',
      'span[class*="title"]',
      'p[class*="title"]',
      'div[class*="name"]',
      'span[class*="name"]',
      'h1', 'h2', 'h3', 'h4'
    ];
    
    const candidates = [];
    
    for (const selector of possibleSelectors) {
      const elements = this.safeQuerySelectorAll(selector);
      
      for (const element of elements) {
        if (this.isValidTitleElement(element)) {
          const score = this.calculateTitleScore(element);
          candidates.push({ element, score, selector });
        }
      }
    }
    
    // 按分数排序
    candidates.sort((a, b) => b.score - a.score);
    
    // 返回最高分的元素组合
    if (candidates.length > 0) {
      const topCandidates = candidates.slice(0, Math.min(3, candidates.length));
      return this.extractTitleFromCandidates(topCandidates, 'intelligent');
    }
    
    return null;
  }

  /**
   * 从元素列表中提取标题
   * @param {NodeList|Array} elements - 元素列表
   * @param {string} selector - 使用的选择器
   * @param {string} method - 提取方法
   * @returns {Object|null} 标题信息
   */
  extractTitleFromElements(elements, selector, method) {
    const titleParts = [];
    const validElements = [];
    
    for (const element of elements) {
      if (this.isValidTitleElement(element)) {
        const text = this.getElementText(element);
        if (text && text.length >= this.config.minTextLength) {
          titleParts.push(text);
          validElements.push(element);
          
          // 限制标题片段数量
          if (titleParts.length >= this.config.maxTitleParts) {
            break;
          }
        }
      }
    }
    
    if (titleParts.length === 0) {
      return null;
    }
    
    return this.createTitleData(titleParts, validElements, selector, method);
  }

  /**
   * 从候选元素中提取标题
   * @param {Array} candidates - 候选元素列表
   * @param {string} method - 提取方法
   * @returns {Object|null} 标题信息
   */
  extractTitleFromCandidates(candidates, method) {
    const titleParts = [];
    const validElements = [];
    const selectors = [];
    
    for (const candidate of candidates) {
      const text = this.getElementText(candidate.element);
      if (text && text.length >= this.config.minTextLength) {
        titleParts.push(text);
        validElements.push(candidate.element);
        selectors.push(candidate.selector);
        
        if (titleParts.length >= this.config.maxTitleParts) {
          break;
        }
      }
    }
    
    if (titleParts.length === 0) {
      return null;
    }
    
    return this.createTitleData(titleParts, validElements, selectors.join(', '), method);
  }

  /**
   * 创建标题数据对象
   * @param {Array} titleParts - 标题片段
   * @param {Array} elements - 元素列表
   * @param {string} selector - 选择器
   * @param {string} method - 提取方法
   * @returns {Object} 标题数据
   */
  createTitleData(titleParts, elements, selector, method) {
    const fullTitle = titleParts.join(' ').trim();
    
    return {
      title: fullTitle,
      titleParts: titleParts,
      partCount: titleParts.length,
      elements: elements.map(el => ({
        tagName: el.tagName.toLowerCase(),
        className: el.className,
        id: el.id,
        text: this.getElementText(el)
      })),
      extraction: {
        method: method,
        selector: selector,
        timestamp: Date.now()
      },
      metadata: {
        totalLength: fullTitle.length,
        wordCount: fullTitle.split(/\s+/).length,
        hasMultipleParts: titleParts.length > 1,
        averagePartLength: Math.round(titleParts.reduce((sum, part) => sum + part.length, 0) / titleParts.length)
      }
    };
  }

  /**
   * 验证是否为有效的标题元素
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否有效
   */
  isValidTitleElement(element) {
    if (!element) return false;
    
    // 检查是否在排除列表中
    const excludeSelectors = this.getSelectors().exclude;
    for (const excludeSelector of excludeSelectors) {
      if (element.matches && element.matches(excludeSelector)) {
        return false;
      }
      if (element.closest && element.closest(excludeSelector)) {
        return false;
      }
    }
    
    // 获取文本内容
    const text = this.getElementText(element);
    if (!text) return false;
    
    // 文本长度检查
    if (text.length < this.config.minTextLength || text.length > this.config.maxTextLength) {
      return false;
    }
    
    // 检查是否包含明显的非标题内容
    const nonTitleKeywords = [
      '登录', '注册', '搜索', '购物车', '收藏',
      '客服', '联系', '电话', '地址', '邮箱',
      '版权', '备案', '隐私', '条款', '协议',
      '首页', '导航', '菜单', '分类', '筛选',
      '排序', '页码', '上一页', '下一页'
    ];
    
    const hasNonKeyword = nonTitleKeywords.some(keyword => text.includes(keyword));
    if (hasNonKeyword) {
      return false;
    }
    
    // 检查元素可见性
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      return false;
    }
    
    return true;
  }

  /**
   * 计算标题元素分数
   * @param {Element} element - DOM元素
   * @returns {number} 分数
   */
  calculateTitleScore(element) {
    let score = 0;
    const text = this.getElementText(element);
    
    // 基础分数
    score += 10;
    
    // 标签名加分
    const tagName = element.tagName.toLowerCase();
    if (tagName === 'h1') score += 30;
    else if (tagName === 'h2') score += 25;
    else if (tagName === 'h3') score += 20;
    else if (tagName === 'div') score += 15;
    else if (tagName === 'span') score += 10;
    
    // 类名加分
    const className = element.className.toLowerCase();
    if (className.includes('title')) score += 25;
    if (className.includes('name')) score += 20;
    if (className.includes('product')) score += 15;
    if (className.includes('offer')) score += 15;
    if (className.includes('item')) score += 10;
    
    // 文本长度合理性
    if (text.length >= 10 && text.length <= 100) {
      score += 20;
    } else if (text.length > 100 && text.length <= 200) {
      score += 10;
    } else if (text.length > 200) {
      score -= 10;
    }
    
    // 位置加分（页面上方的元素更可能是标题）
    const rect = element.getBoundingClientRect();
    if (rect.top < window.innerHeight * 0.4) {
      score += 15;
    }
    
    // 可见性检查
    if (rect.width > 0 && rect.height > 0) {
      score += 5;
    }
    
    // 字体大小加分（较大的字体更可能是标题）
    const computedStyle = window.getComputedStyle(element);
    const fontSize = parseFloat(computedStyle.fontSize);
    if (fontSize >= 18) score += 15;
    else if (fontSize >= 16) score += 10;
    else if (fontSize >= 14) score += 5;
    
    // 字体粗细加分
    const fontWeight = computedStyle.fontWeight;
    if (fontWeight === 'bold' || parseInt(fontWeight) >= 600) {
      score += 10;
    }
    
    return score;
  }

  /**
   * 获取元素文本内容
   * @param {Element} element - DOM元素
   * @returns {string} 清理后的文本
   */
  getElementText(element) {
    if (!element) return '';
    
    // 获取原始文本
    let textContent = element.textContent || element.innerText || '';
    textContent = textContent.trim().replace(/\s+/g, ' ');
    
    // 过滤掉不相关的信息
    const unwantedPatterns = [
      /\d+(?:\.\d+)?条?评价/g, // 评价数量
      /全网销量\d+\+?/g, // 全网销量
      /\d+个?成交/g, // 成交数量
      /一年内\d+\+?\s*件成交/g, // 一年内成交信息
      /\d+\+?\s*件成交/g, // 件成交信息
      /月销\d+\+?/g, // 月销量
      /举报/g, // 举报
      /\d+\.\d+分?/g, // 评分
      /⭐+/g, // 星级符号
      /★+/g, // 星级符号
      /收藏\d+/g, // 收藏数
      /浏览\d+/g, // 浏览数
      /关注\d+/g, // 关注数
      /¥\d+(?:\.\d+)?/g, // 价格信息
      /批发|代发|起批/g, // 模式信息
      /包邮|运费/g // 运费信息
    ];
    
    // 移除不相关信息
    unwantedPatterns.forEach(pattern => {
      textContent = textContent.replace(pattern, '').trim();
    });
    
    // 处理重复标题问题 - 如果文本中包含重复的部分，只保留第一部分
    const words = textContent.split(' ');
    if (words.length > 10) {
      // 检查是否有重复的标题部分
      const firstHalf = words.slice(0, Math.floor(words.length / 2)).join(' ');
      const secondHalf = words.slice(Math.floor(words.length / 2)).join(' ');
      
      // 如果前半部分和后半部分相似度很高，只保留前半部分
      if (this.calculateSimilarity(firstHalf, secondHalf) > 0.8) {
        textContent = firstHalf;
      }
    }
    
    // 清理多余的空格
    textContent = textContent.replace(/\s+/g, ' ').trim();
    
    // 如果清理后文本太短，可能过度清理了，返回原始文本的前50个字符
    if (textContent.length < 10 && (element.textContent || element.innerText || '').length > 20) {
      const originalText = (element.textContent || element.innerText || '').trim();
      return originalText.substring(0, 50).replace(/\s+/g, ' ');
    }
    
    return textContent;
  }
  
  /**
   * 计算两个字符串的相似度
   * @param {string} str1 - 第一个字符串
   * @param {string} str2 - 第二个字符串
   * @returns {number} 相似度 (0-1)
   */
  calculateSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;
    
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }
  
  /**
   * 计算编辑距离
   * @param {string} str1 - 第一个字符串
   * @param {string} str2 - 第二个字符串
   * @returns {number} 编辑距离
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  /**
   * 原始的获取元素文本方法（保留作为备用）
   * @param {Element} element - DOM元素
   * @returns {string} 清理后的文本
   */
  getElementTextOriginal(element) {
    if (!element) return '';
    
    // 获取原始文本
    let textContent = element.textContent || element.innerText || '';
    textContent = textContent.trim().replace(/\s+/g, ' ');
    
    // 过滤掉不相关的信息
    const unwantedPatterns = [
      /\d+(?:\.\d+)?条?评价/g, // 评价数量
      /全网销量\d+\+?/g, // 全网销量
      /\d+个?成交/g, // 成交数量
      /举报/g, // 举报
      /\d+\.\d+分?/g, // 评分
      /⭐+/g, // 星级符号
      /★+/g, // 星级符号
      /收藏\d+/g, // 收藏数
      /浏览\d+/g, // 浏览数
      /关注\d+/g, // 关注数
      /¥\d+(?:\.\d+)?/g, // 价格信息
      /批发|代发|起批/g, // 模式信息
      /包邮|运费/g // 运费信息
    ];
    
    // 移除不相关信息
    unwantedPatterns.forEach(pattern => {
      textContent = textContent.replace(pattern, '').trim();
    });
    
    // 清理多余的空格
    textContent = textContent.replace(/\s+/g, ' ').trim();
    
    // 如果清理后文本太短，可能过度清理了，返回原始文本的前50个字符
    if (textContent.length < 10 && (element.textContent || element.innerText || '').length > 20) {
      const originalText = (element.textContent || element.innerText || '').trim();
      return originalText.substring(0, 50).replace(/\s+/g, ' ');
    }
    
    return textContent;
  }

  /**
   * 增强标题信息
   * @param {Object} titleInfo - 原始标题信息
   * @returns {Object} 增强后的标题信息
   */
  enhanceTitleInfo(titleInfo) {
    const enhanced = { ...titleInfo };
    
    // 分析标题特征
    enhanced.analysis = this.analyzeTitleFeatures(titleInfo.title);
    
    // 提取关键词
    enhanced.keywords = this.extractTitleKeywords(titleInfo.title);
    
    // 计算可信度
    enhanced.confidence = this.calculateTitleConfidence(titleInfo);
    
    // 标题分类
    enhanced.category = this.categorizeTitleType(titleInfo.title);
    
    return enhanced;
  }

  /**
   * 分析标题特征
   * @param {string} title - 标题文本
   * @returns {Object} 分析结果
   */
  analyzeTitleFeatures(title) {
    const analysis = {
      hasNumbers: /\d/.test(title),
      hasEnglish: /[a-zA-Z]/.test(title),
      hasChinese: /[\u4e00-\u9fa5]/.test(title),
      hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(title),
      hasBrandName: false,
      hasModelNumber: /\b\d{4,}\b/.test(title),
      hasYear: /20\d{2}/.test(title)
    };
    
    // 检测可能的品牌名
    const brandPatterns = [
      /^[A-Z][a-z]+/,  // 首字母大写的英文单词
      /[A-Z]{2,}/      // 全大写字母组合
    ];
    
    analysis.hasBrandName = brandPatterns.some(pattern => pattern.test(title));
    
    return analysis;
  }

  /**
   * 提取标题关键词
   * @param {string} title - 标题文本
   * @returns {Array} 关键词列表
   */
  extractTitleKeywords(title) {
    const keywords = [];
    
    // 产品类型关键词
    const productTypes = [
      '包', '袋', '箱', '盒', '瓶', '杯', '衣', '裤', '鞋',
      '手机', '电脑', '相机', '耳机', '音响', '电视',
      '家具', '灯具', '装饰', '工具', '设备'
    ];
    
    productTypes.forEach(type => {
      if (title.includes(type)) {
        keywords.push({ type: 'product', value: type });
      }
    });
    
    // 材质关键词
    const materials = [
      '棉', '丝', '毛', '麻', '尼龙', '皮革', '塑料', '金属',
      '木', '竹', '玻璃', '陶瓷', '不锈钢', '铝合金'
    ];
    
    materials.forEach(material => {
      if (title.includes(material)) {
        keywords.push({ type: 'material', value: material });
      }
    });
    
    // 颜色关键词
    const colors = [
      '红', '橙', '黄', '绿', '蓝', '紫', '黑', '白', '灰',
      '粉', '棕', '银', '金', '透明'
    ];
    
    colors.forEach(color => {
      if (title.includes(color)) {
        keywords.push({ type: 'color', value: color });
      }
    });
    
    // 尺寸关键词
    const sizePattern = /\d+(\.\d+)?(cm|mm|m|寸|英寸)/g;
    const sizeMatches = title.match(sizePattern);
    if (sizeMatches) {
      sizeMatches.forEach(size => {
        keywords.push({ type: 'size', value: size });
      });
    }
    
    return keywords;
  }

  /**
   * 标题分类
   * @param {string} title - 标题文本
   * @returns {string} 分类结果
   */
  categorizeTitleType(title) {
    // 根据标题内容判断商品类别
    const categories = {
      'fashion': ['服装', '鞋', '包', '配饰', '首饰'],
      'electronics': ['电子', '数码', '手机', '电脑', '相机'],
      'home': ['家居', '家具', '装饰', '厨具', '床品'],
      'beauty': ['美妆', '护肤', '化妆品', '香水'],
      'sports': ['运动', '健身', '户外', '体育'],
      'toys': ['玩具', '游戏', '模型', '益智'],
      'food': ['食品', '零食', '饮料', '茶', '咖啡'],
      'books': ['图书', '教材', '文具', '办公']
    };
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => title.includes(keyword))) {
        return category;
      }
    }
    
    return 'general';
  }

  /**
   * 计算标题可信度
   * @param {Object} titleInfo - 标题信息
   * @returns {number} 可信度 (0-100)
   */
  calculateTitleConfidence(titleInfo) {
    let confidence = 0;
    
    // 基础分数
    confidence += 20;
    
    // 提取方法加分
    if (titleInfo.extraction.method === 'primary') {
      confidence += 40;
    } else if (titleInfo.extraction.method === 'context') {
      confidence += 30;
    } else if (titleInfo.extraction.method === 'intelligent') {
      confidence += 25;
    } else {
      confidence += 15;
    }
    
    // 标题长度合理性
    const titleLength = titleInfo.title.length;
    if (titleLength >= 10 && titleLength <= 100) {
      confidence += 20;
    } else if (titleLength > 100 && titleLength <= 200) {
      confidence += 10;
    } else if (titleLength < 10) {
      confidence -= 15;
    }
    
    // 多部分标题加分
    if (titleInfo.hasMultipleParts) {
      confidence += 10;
    }
    
    // 元素数量合理性
    if (titleInfo.partCount >= 1 && titleInfo.partCount <= 3) {
      confidence += 10;
    } else if (titleInfo.partCount > 3) {
      confidence -= 5;
    }
    
    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 检查必要字段
    if (!data.title || typeof data.title !== 'string') {
      return false;
    }
    
    // 检查标题长度
    if (data.title.length < this.config.minTextLength || 
        data.title.length > this.config.maxTextLength) {
      return false;
    }
    
    // 检查可信度
    if (data.confidence && data.confidence < 30) {
      return false;
    }
    
    return true;
  }

  /**
   * 数据格式化
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    if (!data) return null;
    
    return {
      ...data,
      title: data.title.trim().replace(/\s+/g, ' '),
      titleParts: data.titleParts ? data.titleParts.map(part => part.trim()) : [],
      formatted: true,
      formatTime: Date.now()
    };
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.ConsignProductTitleExtractor = ConsignProductTitleExtractor;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = ConsignProductTitleExtractor;
}