# 1688商品信息提取器 - 模块化Chrome插件设计方案

## 项目概述

### 项目目标
创建一个模块化的Chrome浏览器插件，能够智能识别1688网站的批发和代发模式链接，并通过独立的抓取模块提取商品信息，在统一的UI界面中展示给用户。

### 核心特性
- **智能链接识别**: 自动区分批发链接和代发链接
- **模块化架构**: 每个抓取功能独立成模块，便于维护和扩展
- **统一UI展示**: 类似公告牌的界面，动态显示抓取结果
- **进度反馈**: 实时显示抓取进度和状态
- **可扩展性**: 支持后续添加更多抓取元素和网站

## 需求分析

### 链接类型识别

#### 批发链接特征
```
https://detail.1688.com/offer/711914850251.html?offerId=711914850251&spm=a260k.home2025.recommendpart.3
```
- 关键特征: 不包含 `sk=consign` 参数
- 目标元素: `<h1>` 标签中的商家信息

#### 代发链接特征
```
https://detail.1688.com/offer/711914850251.html?spm=a261y.7663282.sceneKey.1.48db5b6fAv3N97&sk=consign&traceId=215041fa17572797111802727eb127&spm-url=a260k.home2025.recommendpart.3&spm-auction=711914850251&spm-pre=a260k.home2025.recommendpart.3
```
- 关键特征: 包含 `sk=consign` 参数
- 目标元素: `<span>` 标签中的商家信息

### 功能需求

1. **URL解析模块**: 识别当前页面类型
2. **批发模式抓取模块**: 处理批发页面的数据提取
3. **代发模式抓取模块**: 处理代发页面的数据提取
4. **UI展示模块**: 统一的结果展示界面
5. **进度管理模块**: 抓取进度跟踪和反馈
6. **配置管理模块**: 模块配置和管理

## 系统架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────┐
│                    Chrome Extension                     │
├─────────────────────────────────────────────────────────┤
│  Content Script (注入到1688页面)                        │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │   URL识别器     │  │   进度管理器    │              │
│  └─────────────────┘  └─────────────────┘              │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │  批发抓取模块   │  │  代发抓取模块   │              │
│  └─────────────────┘  └─────────────────┘              │
├─────────────────────────────────────────────────────────┤
│  Background Script (后台处理)                           │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │   数据处理器    │  │   配置管理器    │              │
│  └─────────────────┘  └─────────────────┘              │
├─────────────────────────────────────────────────────────┤
│  Popup UI (用户界面)                                    │
│  ┌─────────────────────────────────────────────────────┐│
│  │              信息展示面板                          ││
│  │  ┌─────────────┐  ┌─────────────┐                  ││
│  │  │  商家信息   │  │  进度条     │                  ││
│  │  └─────────────┘  └─────────────┘                  ││
│  │  ┌─────────────┐  ┌─────────────┐                  ││
│  │  │  商品信息   │  │  操作按钮   │                  ││
│  │  └─────────────┘  └─────────────┘                  ││
│  └─────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────┘
```

### 模块化设计原则

#### 1. 单一职责原则
每个模块只负责一个特定的功能，便于维护和测试。

#### 2. 开放封闭原则
模块对扩展开放，对修改封闭。新增抓取元素时只需添加新模块。

#### 3. 依赖倒置原则
高层模块不依赖低层模块，都依赖于抽象接口。

## 模块命名规范

### 文件命名规范

```
模块类型_功能描述_版本号.js
```

#### 示例:
- `extractor_wholesale_merchant_v1.0.js` - 批发模式商家信息抓取器
- `extractor_consign_merchant_v1.0.js` - 代发模式商家信息抓取器
- `detector_url_pattern_v1.0.js` - URL模式识别器
- `ui_progress_bar_v1.0.js` - 进度条UI组件
- `ui_info_panel_v1.0.js` - 信息展示面板

### 模块ID命名规范

```
网站简称_页面类型_元素类型_序号
```

#### 示例:
- `1688_wholesale_merchant_001` - 1688批发页面商家信息(第1个)
- `1688_consign_merchant_001` - 1688代发页面商家信息(第1个)
- `1688_wholesale_product_001` - 1688批发页面商品信息(第1个)

## 技术实现方案

### 核心技术栈
- **Manifest V3**: Chrome扩展最新标准
- **JavaScript ES6+**: 现代JavaScript特性
- **CSS3**: 现代样式设计
- **HTML5**: 结构化标记

### 关键技术点

#### 1. URL模式识别
```javascript
class URLDetector {
  static detectPageType(url) {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;
    
    if (params.has('sk') && params.get('sk') === 'consign') {
      return 'consign'; // 代发模式
    }
    return 'wholesale'; // 批发模式
  }
}
```

#### 2. 模块化抓取器接口
```javascript
class BaseExtractor {
  constructor(moduleId, name) {
    this.moduleId = moduleId;
    this.name = name;
    this.status = 'pending';
  }
  
  async extract() {
    throw new Error('extract method must be implemented');
  }
  
  getSelectors() {
    throw new Error('getSelectors method must be implemented');
  }
}
```

#### 3. 进度管理系统
```javascript
class ProgressManager {
  constructor() {
    this.tasks = new Map();
    this.listeners = [];
  }
  
  addTask(taskId, taskName) {
    this.tasks.set(taskId, {
      id: taskId,
      name: taskName,
      status: 'pending',
      progress: 0
    });
  }
  
  updateProgress(taskId, progress, status) {
    const task = this.tasks.get(taskId);
    if (task) {
      task.progress = progress;
      task.status = status;
      this.notifyListeners();
    }
  }
}
```

## 数据流设计

### 数据流向图

```
页面加载 → URL识别 → 模块选择 → 数据抓取 → 数据处理 → UI展示
    ↓         ↓         ↓         ↓         ↓         ↓
  检测URL   判断类型   加载模块   执行抓取   格式化     更新界面
    ↓         ↓         ↓         ↓         ↓         ↓
  初始化     选择器     DOM查询   数据提取   验证数据   进度反馈
```

### 消息传递机制

```javascript
// Content Script → Background
chrome.runtime.sendMessage({
  type: 'EXTRACTION_START',
  pageType: 'wholesale',
  url: window.location.href
});

// Background → Popup
chrome.runtime.sendMessage({
  type: 'PROGRESS_UPDATE',
  taskId: 'merchant_info',
  progress: 50,
  status: 'processing'
});
```

## UI设计方案

### 界面布局

```
┌─────────────────────────────────────────┐
│  1688商品信息提取器                     │
├─────────────────────────────────────────┤
│  页面类型: [批发模式] 🔄                │
├─────────────────────────────────────────┤
│  抓取进度:                              │
│  ┌─────────────────────────────────────┐ │
│  │ 商家信息 ████████░░ 80%             │ │
│  │ 商品标题 ██████░░░░ 60%             │ │
│  │ 价格信息 ████░░░░░░ 40%             │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│  抓取结果:                              │
│  ┌─────────────────────────────────────┐ │
│  │ 商家: 义乌市靖发箱包有限公司        │ │
│  │ 标题: [商品标题]                    │ │
│  │ 价格: ¥[价格区间]                   │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│  [开始抓取] [停止] [导出数据] [设置]    │
└─────────────────────────────────────────┘
```

### 样式设计原则
- **简洁明了**: 信息层次清晰，避免视觉混乱
- **实时反馈**: 进度条和状态指示器提供即时反馈
- **响应式设计**: 适配不同屏幕尺寸
- **主题一致**: 与Chrome浏览器风格保持一致

## 扩展性设计

### 新增抓取元素流程

1. **创建新的抓取器模块**
   ```javascript
   // extractor_wholesale_price_v1.0.js
   class WholesalePriceExtractor extends BaseExtractor {
     constructor() {
       super('1688_wholesale_price_001', '批发价格');
     }
     
     getSelectors() {
       return {
         primary: '.price-range .price',
         fallback: '.offer-price .price-num'
       };
     }
     
     async extract() {
       // 实现价格抓取逻辑
     }
   }
   ```

2. **注册到模块管理器**
   ```javascript
   ModuleManager.register('wholesale', WholesalePriceExtractor);
   ```

3. **更新UI配置**
   ```javascript
   UIConfig.addDisplayItem('price', '价格信息', 'currency');
   ```

### 新增网站支持流程

1. **创建网站检测器**
2. **实现网站特定的抓取器**
3. **配置URL匹配规则**
4. **更新manifest.json权限**

## 开发计划

### 第一阶段: 核心框架 (1-2周)
- [ ] 项目初始化和基础结构
- [ ] URL识别模块开发
- [ ] 基础抓取器框架
- [ ] 进度管理系统
- [ ] 基础UI框架

### 第二阶段: 功能实现 (2-3周)
- [ ] 批发模式商家信息抓取器
- [ ] 代发模式商家信息抓取器
- [ ] UI展示面板完善
- [ ] 数据处理和验证
- [ ] 错误处理机制

### 第三阶段: 优化完善 (1-2周)
- [ ] 性能优化
- [ ] 用户体验改进
- [ ] 测试和调试
- [ ] 文档完善
- [ ] 发布准备

## 风险评估与应对

### 技术风险
1. **DOM结构变化**: 1688网站更新导致选择器失效
   - 应对: 多重选择器策略，定期监控和更新

2. **反爬虫机制**: 网站可能检测和阻止自动化抓取
   - 应对: 模拟人工操作，添加随机延迟

3. **性能问题**: 大量DOM查询可能影响页面性能
   - 应对: 异步处理，批量操作，缓存机制

### 业务风险

1. **用户隐私**: 用户数据保护
   - 应对: 本地存储，不上传敏感信息

## 总结

本设计方案采用模块化架构，实现了高内聚、低耦合的系统设计。通过智能URL识别、独立抓取模块、统一UI展示和实时进度反馈，为用户提供了一个功能强大、易于维护和扩展的1688商品信息提取工具。

该方案的核心优势:
1. **模块化设计**: 便于维护和扩展
2. **智能识别**: 自动适配不同页面类型
3. **用户友好**: 直观的界面和实时反馈
4. **可扩展性**: 支持快速添加新功能
5. **稳定性**: 多重容错和异常处理机制

通过这个设计方案，我们可以构建一个专业、稳定、易用的Chrome插件，为用户提供高效的1688商品信息提取服务。