/**
 * Background Script - 后台服务脚本
 * 处理插件的后台逻辑、消息传递和数据管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 全局状态管理
const ExtractorBackground = {
  // 状态数据
  state: {
    isActive: false,
    currentTab: null,
    extractionResults: new Map(),
    progressData: new Map(),
    settings: {
      autoStart: false,
      debugMode: true,
      notificationEnabled: true
    }
  },
  
  // 初始化
  init() {
    // Background Script initialized
    
    // 设置事件监听器
    this.setupEventListeners();
    
    // 加载设置
    this.loadSettings();
    
    // 设置图标状态
    this.updateIcon('inactive');
  },
  
  // 设置事件监听器
  setupEventListeners() {
    // 消息监听
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });
    
    // 标签页更新监听
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });
    
    // 标签页激活监听
    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.handleTabActivated(activeInfo);
    });
    
    // 插件图标点击监听
    chrome.action.onClicked.addListener((tab) => {
      this.handleActionClick(tab);
    });
    
    // 安装/更新监听
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstalled(details);
    });
  },
  
  // 处理消息
  async handleMessage(message, sender, sendResponse) {
    try {
      // Background received message
      
      switch (message.type) {
        case 'CONTENT_SCRIPT_READY':
          await this.handleContentScriptReady(message, sender);
          sendResponse({ success: true });
          break;
          
        case 'PROGRESS_EVENT':
          this.handleProgressEvent(message.event, sender.tab.id);
          sendResponse({ success: true });
          break;
          
        case 'EXTRACTION_COMPLETED':
          await this.handleExtractionCompleted(message, sender.tab.id);
          sendResponse({ success: true });
          break;
          
        case 'EXTRACTION_FAILED':
          await this.handleExtractionFailed(message, sender.tab.id);
          sendResponse({ success: true });
          break;
          
        case 'EXTRACTION_STOPPED':
          this.handleExtractionStopped(sender.tab.id);
          sendResponse({ success: true });
          break;
          
        case 'EXTRACTION_RESET':
          this.handleExtractionReset(sender.tab.id);
          sendResponse({ success: true });
          break;
          
        case 'GET_BACKGROUND_STATE':
          sendResponse({ 
            success: true, 
            data: this.getBackgroundState(sender.tab.id) 
          });
          break;
          
        case 'UPDATE_SETTINGS':
          await this.updateSettings(message.settings);
          sendResponse({ success: true });
          break;
          
        case 'START_EXTRACTION':
          const result = await this.startExtraction(sender.tab.id);
          sendResponse(result);
          break;
          
        case 'STOP_EXTRACTION':
          await this.stopExtraction(sender.tab.id);
          sendResponse({ success: true });
          break;
          
        default:
          console.warn('Unknown message type:', message.type);
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  },
  
  // 处理Content Script就绪
  async handleContentScriptReady(message, sender) {
    const tabId = sender.tab.id;
    
    // Content script ready
    
    // 更新状态
    this.state.currentTab = tabId;
    this.state.isActive = true;
    
    // 存储页面信息
    if (!this.state.extractionResults.has(tabId)) {
      this.state.extractionResults.set(tabId, {
        pageInfo: message.pageInfo,
        extractors: message.extractors,
        results: null,
        status: 'ready',
        timestamp: Date.now()
      });
    }
    
    // 更新图标
    this.updateIcon('ready', tabId);
    
    // 发送通知
    if (this.state.settings.notificationEnabled) {
      this.showNotification(
        '1688商品信息提取器',
        `检测到${message.pageInfo.pageType === 'wholesale' ? '批发' : '代发'}页面，准备就绪`,
        'ready'
      );
    }
  },
  
  // 处理进度事件
  handleProgressEvent(event, tabId) {
    if (!this.state.progressData.has(tabId)) {
      this.state.progressData.set(tabId, []);
    }
    
    const progressHistory = this.state.progressData.get(tabId);
    progressHistory.push({
      ...event,
      tabId: tabId,
      timestamp: Date.now()
    });
    
    // 限制历史记录长度
    if (progressHistory.length > 100) {
      progressHistory.splice(0, progressHistory.length - 100);
    }
    
    // 更新图标状态
    if (event.type === 'globalProgress') {
      if (event.data.status === 'running') {
        this.updateIcon('extracting', tabId);
      }
    }
  },
  
  // 处理提取完成
  async handleExtractionCompleted(message, tabId) {
    // Extraction completed
    
    // 更新结果
    const tabData = this.state.extractionResults.get(tabId);
    if (tabData) {
      tabData.results = message.results;
      tabData.status = 'completed';
      tabData.stats = message.stats;
      tabData.completedAt = Date.now();
    }
    
    // 更新图标
    this.updateIcon('completed', tabId);
    
    // 发送通知
    if (this.state.settings.notificationEnabled) {
      this.showNotification(
        '提取完成',
        `成功提取 ${message.stats.success} 项信息`,
        'completed'
      );
    }
    
    // 保存结果到存储
    await this.saveResults(tabId, message.results);
  },
  
  // 处理提取失败
  async handleExtractionFailed(message, tabId) {
    console.error(`Extraction failed for tab ${tabId}:`, message.error);
    
    // 更新状态
    const tabData = this.state.extractionResults.get(tabId);
    if (tabData) {
      tabData.status = 'failed';
      tabData.error = message.error;
      tabData.failedAt = Date.now();
    }
    
    // 更新图标
    this.updateIcon('failed', tabId);
    
    // 发送通知
    if (this.state.settings.notificationEnabled) {
      this.showNotification(
        '提取失败',
        message.error,
        'failed'
      );
    }
  },
  
  // 处理提取停止
  handleExtractionStopped(tabId) {
    // Extraction stopped
    
    const tabData = this.state.extractionResults.get(tabId);
    if (tabData) {
      tabData.status = 'stopped';
      tabData.stoppedAt = Date.now();
    }
    
    this.updateIcon('ready', tabId);
  },
  
  // 处理提取重置
  handleExtractionReset(tabId) {
    // Extraction reset
    
    // 清理数据
    this.state.extractionResults.delete(tabId);
    this.state.progressData.delete(tabId);
    
    this.updateIcon('inactive', tabId);
  },
  
  // 处理标签页更新
  handleTabUpdate(tabId, changeInfo, tab) {
    // 检查是否为1688页面
    if (changeInfo.status === 'complete' && tab.url) {
      if (this.is1688ProductPage(tab.url)) {
        // 1688 product page loaded
        // 页面加载完成，content script会自动初始化
      } else {
        // 不是1688页面，清理数据
        this.cleanupTabData(tabId);
      }
    }
  },
  
  // 处理标签页激活
  handleTabActivated(activeInfo) {
    this.state.currentTab = activeInfo.tabId;
    
    // 检查当前标签页是否有提取数据
    const hasData = this.state.extractionResults.has(activeInfo.tabId);
    this.updateIcon(hasData ? 'ready' : 'inactive', activeInfo.tabId);
  },
  
  // 处理插件图标点击
  handleActionClick(tab) {
    // 如果是1688页面，打开popup
    if (this.is1688ProductPage(tab.url)) {
      // Popup会自动打开，这里可以做一些预处理
      // Action clicked on 1688 page
    } else {
      // 不是1688页面，显示提示
      this.showNotification(
        '1688商品信息提取器',
        '请在1688商品页面使用此插件',
        'info'
      );
    }
  },
  
  // 处理安装/更新
  handleInstalled(details) {
    if (details.reason === 'install') {
      // Extension installed
      // 首次安装，可以显示欢迎页面或设置默认配置
    } else if (details.reason === 'update') {
      // Extension updated
    }
  },
  
  // 开始提取
  async startExtraction(tabId) {
    try {
      const response = await chrome.tabs.sendMessage(tabId, {
        type: 'START_EXTRACTION'
      });
      
      return response;
    } catch (error) {
      console.error('Failed to start extraction:', error);
      return { success: false, error: error.message };
    }
  },
  
  // 停止提取
  async stopExtraction(tabId) {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: 'STOP_EXTRACTION'
      });
    } catch (error) {
      console.error('Failed to stop extraction:', error);
    }
  },
  
  // 更新图标
  updateIcon(status, tabId = null) {
    const iconPaths = {
      inactive: {
        '16': 'icons/icon16_inactive.png',
        '32': 'icons/icon32_inactive.png',
        '48': 'icons/icon48_inactive.png',
        '128': 'icons/icon128_inactive.png'
      },
      ready: {
        '16': 'icons/icon16.png',
        '32': 'icons/icon32.png',
        '48': 'icons/icon48.png',
        '128': 'icons/icon128.png'
      },
      extracting: {
        '16': 'icons/icon16_active.png',
        '32': 'icons/icon32_active.png',
        '48': 'icons/icon48_active.png',
        '128': 'icons/icon128_active.png'
      },
      completed: {
        '16': 'icons/icon16_success.png',
        '32': 'icons/icon32_success.png',
        '48': 'icons/icon48_success.png',
        '128': 'icons/icon128_success.png'
      },
      failed: {
        '16': 'icons/icon16_error.png',
        '32': 'icons/icon32_error.png',
        '48': 'icons/icon48_error.png',
        '128': 'icons/icon128_error.png'
      }
    };
    
    const icons = iconPaths[status] || iconPaths.inactive;
    
    const updateOptions = { path: icons };
    if (tabId) {
      updateOptions.tabId = tabId;
    }
    
    chrome.action.setIcon(updateOptions).catch(error => {
      console.warn('Failed to update icon:', error);
    });
  },
  
  // 显示通知
  showNotification(title, message, type = 'info') {
    const iconMap = {
      info: 'icons/icon48.png',
      ready: 'icons/icon48.png',
      completed: 'icons/icon48_success.png',
      failed: 'icons/icon48_error.png'
    };
    
    chrome.notifications.create({
      type: 'basic',
      iconUrl: iconMap[type] || iconMap.info,
      title: title,
      message: message
    }).catch(error => {
      console.warn('Failed to show notification:', error);
    });
  },
  
  // 检查是否为1688商品页面
  is1688ProductPage(url) {
    if (!url) return false;
    
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.includes('1688.com') && 
             urlObj.pathname.includes('/offer/') && 
             urlObj.pathname.includes('.html');
    } catch (error) {
      return false;
    }
  },
  
  // 清理标签页数据
  cleanupTabData(tabId) {
    this.state.extractionResults.delete(tabId);
    this.state.progressData.delete(tabId);
    
    if (this.state.currentTab === tabId) {
      this.state.isActive = false;
      this.updateIcon('inactive');
    }
  },
  
  // 获取后台状态
  getBackgroundState(tabId) {
    return {
      isActive: this.state.isActive,
      currentTab: this.state.currentTab,
      tabData: this.state.extractionResults.get(tabId) || null,
      progressData: this.state.progressData.get(tabId) || [],
      settings: this.state.settings
    };
  },
  
  // 加载设置
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get('settings');
      if (result.settings) {
        this.state.settings = { ...this.state.settings, ...result.settings };
      }
    } catch (error) {
      console.warn('Failed to load settings:', error);
    }
  },
  
  // 更新设置
  async updateSettings(newSettings) {
    try {
      this.state.settings = { ...this.state.settings, ...newSettings };
      await chrome.storage.sync.set({ settings: this.state.settings });
      // Settings updated
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  },
  
  // 保存结果
  async saveResults(tabId, results) {
    try {
      const key = `results_${tabId}_${Date.now()}`;
      await chrome.storage.local.set({ [key]: results });
      
      // 清理旧结果（保留最近10个）
      const allKeys = await chrome.storage.local.get();
      const resultKeys = Object.keys(allKeys)
        .filter(key => key.startsWith('results_'))
        .sort()
        .reverse();
      
      if (resultKeys.length > 10) {
        const keysToRemove = resultKeys.slice(10);
        await chrome.storage.local.remove(keysToRemove);
      }
    } catch (error) {
      console.warn('Failed to save results:', error);
    }
  }
};

// 初始化
ExtractorBackground.init();

// 导出到全局作用域（用于调试）
if (typeof globalThis !== 'undefined') {
  globalThis.ExtractorBackground = ExtractorBackground;
}