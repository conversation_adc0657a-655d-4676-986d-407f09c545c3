/**
 * 物流信息提取功能测试脚本
 * 用于验证修复后的物流信息提取功能
 * <AUTHOR>
 * @version 1.0.0
 */

// 测试用的模拟DOM结构（基于实际观察到的结构）
const mockLogisticsHTML = `
  <div class="module-od-shipping-services cart-gap">
    <span class="cart-label"><od-text i18n="logistic">发货</od-text></span>
    <div class="cart-content">
      <span class="location">浙江金华</span>
      <span class="middle-text">送至</span>
      <span class="service-item">
        <a class="recieve-address v-flex">北京朝阳<em class="v-arrow-down"></em></a>
      </span>
      <span class="service-item">运费¥4起</span>
      <a class="v-flex delivery-limit">
        <span class="wbr">承诺48小时发货</span>
        <span class="v-arrow-down"></span>
      </a>
    </div>
  </div>
`;

// 旧版本的模拟DOM结构（用于对比测试）
const mockLogisticsHTMLOld = `
  <div class="logistics-wrapper">
    <span class="logistics-city">广东东莞</span>
    <div class="logistics-express">
      <span class="logistics-express-price">运费: ¥15.00</span>
    </div>
  </div>
  <div class="next-select-values">
    <em title="北京朝阳区">请选择收货地址</em>
  </div>
`;

/**
 * 创建测试环境
 */
function createTestEnvironment() {
  // 创建测试容器
  const testContainer = document.createElement('div');
  testContainer.id = 'logistics-test-container';
  testContainer.innerHTML = mockLogisticsHTML;
  testContainer.style.cssText = 'display: none;';
  document.body.appendChild(testContainer);
  
  console.log('✅ 测试环境创建完成');
  return testContainer;
}

/**
 * 清理测试环境
 */
function cleanupTestEnvironment() {
  const testContainer = document.getElementById('logistics-test-container');
  if (testContainer) {
    testContainer.remove();
    console.log('✅ 测试环境清理完成');
  }
}

/**
 * 测试代发物流提取器功能
 */
async function testConsignLogisticsExtractor() {
  console.log('🧪 开始测试代发物流信息提取功能...');
  
  try {
    // 创建测试环境
    const testContainer = createTestEnvironment();
    
    // 检查物流提取器是否可用
    if (!window.ConsignLogisticsExtractor) {
      throw new Error('ConsignLogisticsExtractor 未找到');
    }
    
    // 创建物流提取器实例
    const extractor = new window.ConsignLogisticsExtractor();
    console.log('✅ 代发物流提取器实例创建成功');
    
    // 执行提取
    console.log('🔍 开始执行代发物流信息提取...');
    const result = await extractor.extract();
    
    // 验证结果
    console.log('📊 代发提取结果:', result);
    
    const testResults = {
      extractorType: 'consign',
      success: result.success,
      hasOriginCity: !!(result.data && result.data.originCity),
      hasDestinationCity: !!(result.data && result.data.destinationCity),
      hasShippingFee: !!(result.data && result.data.shippingFee),
      confidence: result.confidence || 0,
      extractedData: result.data
    };
    
    console.log('🎯 代发测试结果分析:', testResults);
    
    // 清理测试环境
    cleanupTestEnvironment();
    
    return testResults;
    
  } catch (error) {
    console.error('❌ 代发测试过程中出现错误:', error);
    cleanupTestEnvironment();
    return {
      extractorType: 'consign',
      success: false,
      error: error.message,
      hasOriginCity: false,
      hasDestinationCity: false,
      hasShippingFee: false,
      confidence: 0
    };
  }
}

/**
 * 测试批发物流提取器功能
 */
async function testWholesaleLogisticsExtractor() {
  console.log('🧪 开始测试批发物流信息提取功能...');
  
  try {
    // 创建测试环境
    const testContainer = createTestEnvironment();
    
    // 检查物流提取器是否可用
    if (!window.WholesaleLogisticsExtractor) {
      throw new Error('WholesaleLogisticsExtractor 未找到');
    }
    
    // 创建物流提取器实例
    const extractor = new window.WholesaleLogisticsExtractor();
    console.log('✅ 批发物流提取器实例创建成功');
    
    // 执行提取
    console.log('🔍 开始执行批发物流信息提取...');
    const result = await extractor.extract();
    
    // 验证结果
    console.log('📊 批发提取结果:', result);
    
    const testResults = {
      extractorType: 'wholesale',
      success: result.success,
      hasOriginCity: !!(result.data && result.data.originCity),
      hasDestinationCity: !!(result.data && result.data.destinationCity),
      hasShippingFee: !!(result.data && result.data.shippingFee),
      hasDeliveryPromise: !!(result.data && result.data.deliveryPromise),
      confidence: result.confidence || 0,
      extractedData: result.data
    };
    
    console.log('🎯 批发测试结果分析:', testResults);
    
    // 清理测试环境
    cleanupTestEnvironment();
    
    return testResults;
    
  } catch (error) {
    console.error('❌ 批发测试过程中出现错误:', error);
    cleanupTestEnvironment();
    return {
      extractorType: 'wholesale',
      success: false,
      error: error.message,
      hasOriginCity: false,
      hasDestinationCity: false,
      hasShippingFee: false,
      hasDeliveryPromise: false,
      confidence: 0
    };
  }
}

/**
 * 测试物流提取器功能（兼容性方法）
 */
async function testLogisticsExtractor() {
  console.log('🧪 开始测试物流信息提取功能（双提取器模式）...');
  
  // 同时测试两个提取器
  const consignResults = await testConsignLogisticsExtractor();
  const wholesaleResults = await testWholesaleLogisticsExtractor();
  
  // 选择更好的结果
  const bestResults = consignResults.confidence >= wholesaleResults.confidence ? consignResults : wholesaleResults;
  
  console.log('🏆 最佳提取结果:', bestResults);
  
  // 显示详细的测试报告
  displayTestReport(bestResults, { consignResults, wholesaleResults });
  
  return bestResults;
}

/**
 * 显示测试报告
 */
function displayTestReport(testResults, comparativeResults = null) {
  const extractorType = testResults.extractorType || 'unknown';
  const extractorName = extractorType === 'wholesale' ? '批发' : extractorType === 'consign' ? '代发' : '未知';
  
  let report = `
🧪 ===== 物流信息提取测试报告 (${extractorName}模式) =====

✅ 基础功能测试:
   - 提取器加载: ${testResults.success ? '✅ 成功' : '❌ 失败'}
   - 发货地提取: ${testResults.hasOriginCity ? '✅ 成功' : '❌ 失败'}
   - 目的地提取: ${testResults.hasDestinationCity ? '✅ 成功' : '❌ 失败'}
   - 运费信息提取: ${testResults.hasShippingFee ? '✅ 成功' : '❌ 失败'}`;
  
  if (testResults.hasDeliveryPromise !== undefined) {
    report += `
   - 配送承诺提取: ${testResults.hasDeliveryPromise ? '✅ 成功' : '❌ 失败'}`;
  }
  
  const totalFields = testResults.hasDeliveryPromise !== undefined ? 4 : 3;
  const successFields = (testResults.hasOriginCity ? 1 : 0) + 
                       (testResults.hasDestinationCity ? 1 : 0) + 
                       (testResults.hasShippingFee ? 1 : 0) + 
                       (testResults.hasDeliveryPromise ? 1 : 0);
  
  report += `

📊 提取质量:
   - 置信度: ${testResults.confidence}%
   - 数据完整性: ${Math.round(successFields / totalFields * 100)}%
   - 提取器类型: ${extractorName}模式

📋 提取的数据:
   - 发货地: ${testResults.extractedData?.originCity || '未提取到'}
   - 目的地: ${testResults.extractedData?.destinationCity || '未提取到'}
   - 运费: ${testResults.extractedData?.shippingFee || '未提取到'}`;
  
  if (testResults.extractedData?.deliveryPromise) {
    report += `
   - 配送承诺: ${testResults.extractedData.deliveryPromise}`;
  }
  
  if (testResults.extractedData?.logisticsMethod) {
    report += `
   - 物流方式: ${testResults.extractedData.logisticsMethod}`;
  }
  
  report += `
   - 是否包邮: ${testResults.extractedData?.isFreeship ? '是' : '否'}`;
  
  if (testResults.error) {
    report += `

❌ 错误信息: ${testResults.error}`;
  }
  
  // 如果有对比结果，显示对比信息
  if (comparativeResults) {
    report += `

🔄 ===== 双提取器对比结果 =====
📊 代发提取器:
   - 成功率: ${comparativeResults.consignResults.success ? '✅' : '❌'}
   - 置信度: ${comparativeResults.consignResults.confidence}%
   - 数据完整性: ${Math.round(((comparativeResults.consignResults.hasOriginCity ? 1 : 0) + (comparativeResults.consignResults.hasDestinationCity ? 1 : 0) + (comparativeResults.consignResults.hasShippingFee ? 1 : 0)) / 3 * 100)}%

📊 批发提取器:
   - 成功率: ${comparativeResults.wholesaleResults.success ? '✅' : '❌'}
   - 置信度: ${comparativeResults.wholesaleResults.confidence}%
   - 数据完整性: ${Math.round(((comparativeResults.wholesaleResults.hasOriginCity ? 1 : 0) + (comparativeResults.wholesaleResults.hasDestinationCity ? 1 : 0) + (comparativeResults.wholesaleResults.hasShippingFee ? 1 : 0) + (comparativeResults.wholesaleResults.hasDeliveryPromise ? 1 : 0)) / 4 * 100)}%

🏆 推荐使用: ${testResults.extractorType === 'wholesale' ? '批发提取器' : '代发提取器'} (置信度更高)`;
  }
  
  report += `

=======================================`;
  
  console.log(report);
  
  // 如果有调试面板，也显示到调试面板
  if (window.debugPanel && window.debugPanel.addLogisticsInfo) {
    window.debugPanel.addLogisticsInfo({
      ...testResults.extractedData,
      confidence: testResults.confidence,
      containerFound: testResults.success,
      testMode: true,
      extractorType: testResults.extractorType,
      testResults: testResults,
      comparativeResults: comparativeResults
    });
  }
}

/**
 * 测试页面类型检测功能
 */
function testPageTypeDetection() {
  console.log('🔍 测试页面类型检测功能...');
  
  if (!window.urlDetector) {
    console.error('❌ URL检测器未找到');
    return;
  }
  
  const detection = window.urlDetector.detectPageType();
  console.log('📊 页面类型检测结果:', detection);
  
  // 测试DOM内容检测
  if (window.urlDetector.detectConsignModeByDOM) {
    const domDetection = window.urlDetector.detectConsignModeByDOM();
    console.log('🔍 DOM内容检测结果:', domDetection);
  }
  
  // 如果有调试面板，显示系统状态
  if (window.debugPanel && window.debugPanel.addSystemInfo) {
    window.debugPanel.addSystemInfo({
      pageType: detection.pageType,
      confidence: detection.confidence,
      detectionMethod: detection.details?.detectionMethod || 'URL',
      extractorCount: window.extractionManager ? window.extractionManager.extractors.size : 0,
      logisticsExtractorLoaded: !!(window.ConsignLogisticsExtractor),
      details: detection.details
    });
  }
  
  return detection;
}

/**
 * 运行完整测试套件
 */
async function runFullTestSuite() {
  console.log('🚀 开始运行完整测试套件...');
  
  // 测试1: 页面类型检测
  const pageDetection = testPageTypeDetection();
  
  // 测试2: 物流信息提取
  const logisticsTest = await testLogisticsExtractor();
  
  // 综合测试报告
  const overallSuccess = pageDetection.pageType !== 'unknown' && logisticsTest.success;
  
  console.log(`
🎯 ===== 综合测试结果 =====
页面检测: ${pageDetection.pageType !== 'unknown' ? '✅' : '❌'}
物流提取: ${logisticsTest.success ? '✅' : '❌'}
整体状态: ${overallSuccess ? '✅ 通过' : '❌ 失败'}
============================`);
  
  return {
    pageDetection,
    logisticsTest,
    overallSuccess
  };
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.LogisticsExtractionTest = {
    testLogisticsExtractor,
    testConsignLogisticsExtractor,
    testWholesaleLogisticsExtractor,
    testPageTypeDetection,
    runFullTestSuite,
    createTestEnvironment,
    cleanupTestEnvironment
  };
  
  // 自动运行测试（延迟3秒等待页面加载完成）
  setTimeout(() => {
    if (window.location.href.includes('1688.com')) {
      console.log('🔧 检测到1688页面，3秒后自动运行物流提取测试（双提取器模式）...');
      setTimeout(runFullTestSuite, 3000);
    }
  }, 1000);
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testLogisticsExtractor,
    testConsignLogisticsExtractor,
    testWholesaleLogisticsExtractor,
    testPageTypeDetection,
    runFullTestSuite
  };
}