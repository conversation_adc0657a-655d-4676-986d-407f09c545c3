/**
 * 批发模式商品标题抽取器
 * 专门处理1688批发页面的商品标题信息提取
 * 目标元素: .title-text 等标题相关元素
 * <AUTHOR>
 * @version 1.0.0
 */

class WholesaleProductTitleExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_wholesale_product_title_001',
      '批发商品标题',
      '提取1688批发页面的完整标题信息'
    );

    // 抽取器配置
    this.config = {
      timeout: 8000, // 8秒超时
      retryDelay: 1500, // 重试延迟1.5秒
      maxTextLength: 500, // 最大文本长度
      minTextLength: 5, // 最小文本长度
      maxTitleParts: 5 // 最大标题片段数
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 基于Playwright DOM分析的精确选择器 - 按优先级排序
      primary: [
        // 最精确的商品标题选择器（基于实际DOM结构分析）
        'div#productTitle > div.module-od-title > div.title-content:nth-of-type(1) > h1',
        '.module-od-title .title-content h1', // 新版1688页面结构（最高优先级）
        '#productTitle h1', // 基于ID的精确定位
        '.title-content h1:not(.shop-company-name h1):not(.winport-title h1)', // 排除商家名称的H1
        '.od-pc-offer-title-contain h1'
      ],

      // 备用选择器
      fallback: [
        '.title-content div', // 标题内容区域
        '.od-pc-offer-title-contain div', // PC端标题容器
        '.product-name', // 产品名称
        '.offer-name', // 商品名称
        '.item-title', // 项目标题
        'h2.title', // H2标题
        'h3.title' // H3标题
      ],

      // 上下文选择器 - 用于缩小搜索范围
      context: [
        '.module-od-title', // 新版1688标题模块
        '.od-pc-offer-title-contain', // PC端标题容器
        '.title-content', // 标题内容
        '.product-info', // 产品信息
        '.offer-info', // 商品信息
        '.item-info', // 项目信息
        '.main-content', // 主要内容
        '.product-header' // 产品头部
      ],

      // 排除选择器 - 避免误选
      exclude: [
        '.advertisement',
        '.ad-content',
        '.popup',
        '.modal',
        '.tooltip',
        '.breadcrumb', // 面包屑导航
        '.navigation', // 导航
        '.menu', // 菜单
        '.sidebar', // 侧边栏
        '.footer', // 页脚
        'script',
        'style',
        'noscript'
      ]
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的标题信息
   */
  async extract() {
    console.log('🚀 [标题调试] ========== 开始商品标题提取 ==========');
    console.log('🚀 [标题调试] 提取器ID:', this.moduleId);
    console.log('🚀 [标题调试] 当前页面URL:', window.location.href);

    const selectors = this.getSelectors();
    let titleInfo = null;

    try {
      // 第一步：尝试主要选择器
      console.log('🔍 [标题调试] 步骤1: 尝试主要选择器...');
      console.log('🔍 [标题调试] 主要选择器:', JSON.stringify(selectors.primary));
      titleInfo = await this.extractWithPrimarySelectors(selectors.primary);

      if (!titleInfo) {
        // 第二步：尝试在上下文中查找
        console.log('🔍 [标题调试] 步骤2: 尝试上下文选择器...');
        console.log('🔍 [标题调试] 上下文选择器:', JSON.stringify(selectors.context));
        titleInfo = await this.extractWithContextSelectors(selectors.context, selectors.primary);
      } else {
        console.log('✅ [标题调试] 主要选择器提取成功');
      }

      if (!titleInfo) {
        // 第三步：尝试备用选择器
        console.log('🔍 [标题调试] 步骤3: 尝试备用选择器...');
        console.log('🔍 [标题调试] 备用选择器:', JSON.stringify(selectors.fallback));
        titleInfo = await this.extractWithFallbackSelectors(selectors.fallback);
      } else if (!titleInfo.fromPrimary) {
        console.log('✅ [标题调试] 上下文选择器提取成功');
      }

      if (!titleInfo) {
        // 第四步：智能搜索标题元素
        console.log('🔍 [标题调试] 步骤4: 智能搜索标题元素...');
        titleInfo = await this.intelligentTitleSearch();
      } else if (!titleInfo.fromPrimary && !titleInfo.fromContext) {
        console.log('✅ [标题调试] 备用选择器提取成功');
      }

      if (!titleInfo) {
        console.log('❌ [标题调试] 所有方法都未找到商品标题元素');
        throw new Error('未找到商品标题元素');
      } else if (!titleInfo.fromPrimary && !titleInfo.fromContext && !titleInfo.fromFallback) {
        console.log('✅ [标题调试] 智能搜索提取成功');
      }

      console.log('🔄 [标题调试] 原始标题信息:', JSON.stringify(titleInfo, null, 2));

      // 记录DOM数据到调试面板
      if (window.debugPanel && titleInfo.elements && titleInfo.elements.length > 0) {
        window.debugPanel.addDOMData('商品标题提取器', titleInfo.selector || '标题选择器', titleInfo.elements[0]);
      }

      // 数据清理和增强
      console.log('🔄 [标题调试] 开始数据增强处理...');
      const enhancedInfo = this.enhanceTitleInfo(titleInfo);

      console.log('✅ [标题调试] 最终标题数据:', JSON.stringify(enhancedInfo, null, 2));

      // 记录提取结果到调试面板
      if (window.debugPanel) {
        window.debugPanel.addResult('商品标题提取器', enhancedInfo);
      }

      console.log('🎉 [标题调试] ========== 商品标题提取完成 ==========');

      return enhancedInfo;

    } catch (error) {
      console.error('❌ [标题调试] 商品标题提取失败:', error);
      console.log('🎉 [标题调试] ========== 商品标题提取完成 ==========');
      throw error;
    }
  }

  /**
   * 使用主要选择器提取
   * @param {Array} selectors - 选择器列表
   * @returns {Promise<Object|null>} 标题信息
   */
  async extractWithPrimarySelectors(selectors) {
    for (const selector of selectors) {
      try {
        const elements = this.safeQuerySelectorAll(selector);
        if (elements.length > 0) {
          const titleData = this.extractTitleFromElements(elements, selector, 'primary');
          if (titleData) {
            return titleData;
          }
        }
      } catch (error) {
        console.debug(`主选择器 ${selector} 查询失败:`, error.message);
        continue;
      }
    }
    return null;
  }

  /**
   * 在上下文中查找
   * @param {Array} contextSelectors - 上下文选择器
   * @param {Array} targetSelectors - 目标选择器
   * @returns {Promise<Object|null>} 标题信息
   */
  async extractWithContextSelectors(contextSelectors, targetSelectors) {
    for (const contextSelector of contextSelectors) {
      const contextElement = this.safeQuerySelector(contextSelector);
      if (!contextElement) continue;

      for (const targetSelector of targetSelectors) {
        const elements = this.safeQuerySelectorAll(targetSelector, contextElement);
        if (elements.length > 0) {
          const titleData = this.extractTitleFromElements(elements, `${contextSelector} ${targetSelector}`, 'context');
          if (titleData) {
            return titleData;
          }
        }
      }
    }
    return null;
  }

  /**
   * 使用备用选择器提取
   * @param {Array} selectors - 备用选择器列表
   * @returns {Promise<Object|null>} 标题信息
   */
  async extractWithFallbackSelectors(selectors) {
    for (const selector of selectors) {
      const elements = this.safeQuerySelectorAll(selector);

      if (elements.length > 0) {
        const titleData = this.extractTitleFromElements(elements, selector, 'fallback');
        if (titleData) {
          return titleData;
        }
      }
    }
    return null;
  }

  /**
   * 智能搜索标题元素
   * @returns {Promise<Object|null>} 标题信息
   */
  async intelligentTitleSearch() {
    // 搜索可能包含标题的元素
    const possibleSelectors = [
      'div[class*="title"]',
      'span[class*="title"]',
      'p[class*="title"]',
      'div[class*="name"]',
      'span[class*="name"]',
      'h1', 'h2', 'h3', 'h4'
    ];

    const candidates = [];

    for (const selector of possibleSelectors) {
      const elements = this.safeQuerySelectorAll(selector);

      for (const element of elements) {
        if (this.isValidTitleElement(element)) {
          const score = this.calculateTitleScore(element);
          candidates.push({ element, score, selector });
        }
      }
    }

    // 按分数排序
    candidates.sort((a, b) => b.score - a.score);

    // 返回最高分的元素组合
    if (candidates.length > 0) {
      const topCandidates = candidates.slice(0, Math.min(3, candidates.length));
      return this.extractTitleFromCandidates(topCandidates, 'intelligent');
    }

    return null;
  }

  /**
   * 从元素列表中提取标题
   * @param {NodeList|Array} elements - 元素列表
   * @param {string} selector - 使用的选择器
   * @param {string} method - 提取方法
   * @returns {Object|null} 标题信息
   */
  extractTitleFromElements(elements, selector, method) {
    const titleParts = [];
    const validElements = [];

    for (const element of elements) {
      if (this.isValidTitleElement(element)) {
        const text = this.getElementText(element);
        if (text && text.length >= this.config.minTextLength) {
          titleParts.push(text);
          validElements.push(element);

          // 限制标题片段数量
          if (titleParts.length >= this.config.maxTitleParts) {
            break;
          }
        }
      }
    }

    if (titleParts.length === 0) {
      return null;
    }

    return this.createTitleData(titleParts, validElements, selector, method);
  }

  /**
   * 从候选元素中提取标题
   * @param {Array} candidates - 候选元素列表
   * @param {string} method - 提取方法
   * @returns {Object|null} 标题信息
   */
  extractTitleFromCandidates(candidates, method) {
    const titleParts = [];
    const validElements = [];
    const selectors = [];
    const seenTexts = new Set();

    for (const candidate of candidates) {
      const text = this.getElementText(candidate.element);
      if (text && text.length >= this.config.minTextLength) {
        // 清理文本
        const cleanText = this.cleanTitleText(text);

        // 跳过重复内容
        if (seenTexts.has(cleanText) || cleanText.length < this.config.minTextLength) {
          continue;
        }

        // 跳过明显的错误内容
        if (this.isInvalidTitleText(cleanText)) {
          continue;
        }

        titleParts.push(cleanText);
        validElements.push(candidate.element);
        selectors.push(candidate.selector);
        seenTexts.add(cleanText);

        if (titleParts.length >= this.config.maxTitleParts) {
          break;
        }
      }
    }

    if (titleParts.length === 0) {
      return null;
    }

    return this.createTitleData(titleParts, validElements, selectors.join(', '), method);
  }

  /**
   * 清理标题文本
   * @param {string} text - 原始文本
   * @returns {string} 清理后的文本
   */
  cleanTitleText(text) {
    return text
      .replace(/ExtractionManager\s*/g, '') // 移除ExtractionManager
      .replace(/\+条评价.*?成交/g, '') // 移除评价和成交信息
      .replace(/一年内\d+万?\+?\s*双?件?成交/g, '') // 移除成交信息
      .replace(/\d+条评价/g, '') // 移除评价数
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();
  }

  /**
   * 检查是否为无效的标题文本
   * @param {string} text - 文本内容
   * @returns {boolean} 是否无效
   */
  isInvalidTitleText(text) {
    const invalidPatterns = [
      /^ExtractionManager$/,
      /^\d+条评价$/,
      /^一年内\d+万?\+?\s*双?件?成交$/,
      /^\+$/,
      /^\s*$/
    ];

    return invalidPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 创建标题数据对象
   * @param {Array} titleParts - 标题片段
   * @param {Array} elements - 元素列表
   * @param {string} selector - 选择器
   * @param {string} method - 提取方法
   * @returns {Object} 标题数据
   */
  createTitleData(titleParts, elements, selector, method) {
    const fullTitle = titleParts.join(' ').trim();

    return {
      title: fullTitle,
      titleParts: titleParts,
      partCount: titleParts.length,
      elements: elements.map(el => ({
        tagName: el.tagName.toLowerCase(),
        className: el.className,
        id: el.id,
        text: this.getElementText(el)
      })),
      extraction: {
        method: method,
        selector: selector,
        timestamp: Date.now()
      },
      metadata: {
        totalLength: fullTitle.length,
        wordCount: fullTitle.split(/\s+/).length,
        hasMultipleParts: titleParts.length > 1,
        averagePartLength: Math.round(titleParts.reduce((sum, part) => sum + part.length, 0) / titleParts.length)
      }
    };
  }

  /**
   * 验证是否为有效的标题元素
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否有效
   */
  isValidTitleElement(element) {
    if (!element) return false;

    // 检查是否在排除列表中
    const excludeSelectors = this.getSelectors().exclude;
    for (const excludeSelector of excludeSelectors) {
      if (element.matches && element.matches(excludeSelector)) {
        return false;
      }
      if (element.closest && element.closest(excludeSelector)) {
        return false;
      }
    }

    // 获取文本内容
    const text = this.getElementText(element);
    if (!text) return false;

    // 文本长度检查
    if (text.length < this.config.minTextLength || text.length > this.config.maxTextLength) {
      return false;
    }

    // 检查是否包含明显的非标题内容
    const nonTitleKeywords = [
      '登录', '注册', '搜索', '购物车', '收藏',
      '客服', '联系', '电话', '地址', '邮箱',
      '版权', '备案', '隐私', '条款', '协议',
      '首页', '导航', '菜单', '分类', '筛选',
      '排序', '页码', '上一页', '下一页',
      'ExtractionManager', 'Manager', 'Error', 'Debug',
      'Console', 'Log', 'Warning', 'Info'
    ];

    const hasNonKeyword = nonTitleKeywords.some(keyword => text.includes(keyword));
    if (hasNonKeyword) {
      return false;
    }

    // 检查是否包含错误相关的类名
    const errorClasses = ['error', 'debug', 'console', 'log', 'warning'];
    const className = element.className || '';
    const hasErrorClass = errorClasses.some(cls => className.toLowerCase().includes(cls));
    if (hasErrorClass) {
      return false;
    }

    // 检查元素可见性
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      return false;
    }

    return true;
  }

  /**
   * 计算标题元素分数
   * @param {Element} element - DOM元素
   * @returns {number} 分数
   */
  calculateTitleScore(element) {
    let score = 0;
    const text = this.getElementText(element);

    // 基础分数
    score += 10;

    // 标签名加分
    const tagName = element.tagName.toLowerCase();
    if (tagName === 'h1') score += 30;
    else if (tagName === 'h2') score += 25;
    else if (tagName === 'h3') score += 20;
    else if (tagName === 'div') score += 15;
    else if (tagName === 'span') score += 10;

    // 类名加分
    const className = element.className.toLowerCase();
    if (className.includes('title')) score += 25;
    if (className.includes('name')) score += 20;
    if (className.includes('product')) score += 15;
    if (className.includes('offer')) score += 15;
    if (className.includes('item')) score += 10;

    // 文本长度合理性
    if (text.length >= 10 && text.length <= 100) {
      score += 20;
    } else if (text.length > 100 && text.length <= 200) {
      score += 10;
    } else if (text.length > 200) {
      score -= 10;
    }

    // 位置加分（页面上方的元素更可能是标题）
    const rect = element.getBoundingClientRect();
    if (rect.top < window.innerHeight * 0.4) {
      score += 15;
    }

    // 可见性检查
    if (rect.width > 0 && rect.height > 0) {
      score += 5;
    }

    // 字体大小加分（较大的字体更可能是标题）
    const computedStyle = window.getComputedStyle(element);
    const fontSize = parseFloat(computedStyle.fontSize);
    if (fontSize >= 18) score += 15;
    else if (fontSize >= 16) score += 10;
    else if (fontSize >= 14) score += 5;

    // 字体粗细加分
    const fontWeight = computedStyle.fontWeight;
    if (fontWeight === 'bold' || parseInt(fontWeight) >= 600) {
      score += 10;
    }

    return score;
  }

  /**
   * 获取元素文本内容
   * @param {Element} element - DOM元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    if (!element) return '';

    try {
      // 获取原始文本
      let textContent = element.textContent || element.innerText || '';
      if (typeof textContent !== 'string') {
        textContent = String(textContent || '');
      }
      textContent = textContent.trim().replace(/\s+/g, ' ');

      // 过滤掉不相关的信息
      const unwantedPatterns = [
        /\d+(?:\.\d+)?条?评价/g, // 评价数量
        /全网销量\d+\+?/g, // 全网销量
        /\d+个?成交/g, // 成交数量
        /一年内\d+\+?\s*件成交/g, // 一年内成交信息
        /\d+\+?\s*件成交/g, // 件成交信息
        /月销\d+\+?/g, // 月销量
        /举报/g, // 举报
        /\d+\.\d+分?/g, // 评分
        /⭐+/g, // 星级符号
        /★+/g, // 星级符号
        /收藏\d+/g, // 收藏数
        /浏览\d+/g, // 浏览数
        /关注\d+/g, // 关注数
        /¥\d+(?:\.\d+)?/g, // 价格信息
        /批发|代发|起批/g, // 模式信息
        /包邮|运费/g // 运费信息
      ];

      // 移除不相关信息
      unwantedPatterns.forEach(pattern => {
        textContent = textContent.replace(pattern, '').trim();
      });

      // 处理重复标题问题 - 如果文本中包含重复的部分，只保留第一部分
      const words = textContent.split(' ');
      if (words.length > 10) {
        // 检查是否有重复的标题部分
        const firstHalf = words.slice(0, Math.floor(words.length / 2)).join(' ');
        const secondHalf = words.slice(Math.floor(words.length / 2)).join(' ');

        // 如果前半部分和后半部分相似度很高，只保留前半部分
        if (this.calculateSimilarity(firstHalf, secondHalf) > 0.8) {
          textContent = firstHalf;
        }
      }

      // 清理多余的空格
      textContent = textContent.replace(/\s+/g, ' ').trim();

      // 如果清理后文本太短，可能过度清理了，返回原始文本的前50个字符
      if (textContent.length < 10 && (element.textContent || element.innerText || '').length > 20) {
        const originalText = (element.textContent || element.innerText || '').trim();
        return originalText && originalText.substring ? originalText.substring(0, 50).replace(/\s+/g, ' ') : originalText;
      }

      return textContent;
    } catch (error) {
      console.warn('🔧 [商品标题] getElementText错误:', error.message);
      return '';
    }
  }

  /**
   * 计算两个字符串的相似度
   * @param {string} str1 - 第一个字符串
   * @param {string} str2 - 第二个字符串
   * @returns {number} 相似度 (0-1)
   */
  calculateSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * 计算编辑距离
   * @param {string} str1 - 第一个字符串
   * @param {string} str2 - 第二个字符串
   * @returns {number} 编辑距离
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 增强标题信息
   * @param {Object} titleInfo - 原始标题信息
   * @returns {Object} 增强后的标题信息
   */
  enhanceTitleInfo(titleInfo) {
    const enhanced = { ...titleInfo };

    // 提取成交信息（从原始元素中）
    enhanced.salesInfo = this.extractSalesInfo(titleInfo.elements);

    // 分析标题特征
    enhanced.analysis = this.analyzeTitleFeatures(titleInfo.title);

    // 提取关键词
    enhanced.keywords = this.extractTitleKeywords(titleInfo.title);

    // 计算可信度
    enhanced.confidence = this.calculateTitleConfidence(titleInfo);

    // 标题分类
    enhanced.category = this.categorizeTitleType(titleInfo.title);

    return enhanced;
  }

  /**
   * 提取成交信息
   * @param {Array} elements - 原始DOM元素
   * @returns {Object} 成交信息
   */
  extractSalesInfo(elements) {
    const salesInfo = {
      salesCount: null,
      salesText: null,
      period: null
    };

    if (!elements || elements.length === 0) return salesInfo;

    // 从原始元素中查找成交信息
    for (const element of elements) {
      const fullText = element.textContent || element.innerText || '';

      // 匹配成交信息模式
      const salesPatterns = [
        /一年内(\d+\+?)\s*件成交/,
        /(\d+\+?)\s*件成交/,
        /月销(\d+\+?)/,
        /(\d+)个?成交/
      ];

      for (const pattern of salesPatterns) {
        const match = fullText.match(pattern);
        if (match) {
          salesInfo.salesText = match[0];
          salesInfo.salesCount = match[1] || match[0].match(/\d+/)?.[0];

          // 判断时间周期
          if (match[0].includes('一年内')) {
            salesInfo.period = 'yearly';
          } else if (match[0].includes('月销')) {
            salesInfo.period = 'monthly';
          } else {
            salesInfo.period = 'total';
          }

          break;
        }
      }

      if (salesInfo.salesText) break;
    }

    return salesInfo;
  }

  /**
   * 分析标题特征
   * @param {string} title - 标题文本
   * @returns {Object} 分析结果
   */
  analyzeTitleFeatures(title) {
    const analysis = {
      hasNumbers: /\d/.test(title),
      hasEnglish: /[a-zA-Z]/.test(title),
      hasChinese: /[\u4e00-\u9fa5]/.test(title),
      hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(title),
      hasBrandName: false,
      hasModelNumber: /\b\d{4,}\b/.test(title),
      hasYear: /20\d{2}/.test(title)
    };

    // 检测可能的品牌名
    const brandPatterns = [
      /^[A-Z][a-z]+/,  // 首字母大写的英文单词
      /[A-Z]{2,}/      // 全大写字母组合
    ];

    analysis.hasBrandName = brandPatterns.some(pattern => pattern.test(title));

    return analysis;
  }

  /**
   * 提取标题关键词
   * @param {string} title - 标题文本
   * @returns {Array} 关键词列表
   */
  extractTitleKeywords(title) {
    const keywords = [];

    // 产品类型关键词
    const productTypes = [
      '包', '袋', '箱', '盒', '瓶', '杯', '衣', '裤', '鞋',
      '手机', '电脑', '相机', '耳机', '音响', '电视',
      '家具', '灯具', '装饰', '工具', '设备'
    ];

    productTypes.forEach(type => {
      if (title.includes(type)) {
        keywords.push({ type: 'product', value: type });
      }
    });

    // 材质关键词
    const materials = [
      '棉', '丝', '毛', '麻', '尼龙', '皮革', '塑料', '金属',
      '木', '竹', '玻璃', '陶瓷', '不锈钢', '铝合金'
    ];

    materials.forEach(material => {
      if (title.includes(material)) {
        keywords.push({ type: 'material', value: material });
      }
    });

    // 颜色关键词
    const colors = [
      '红', '橙', '黄', '绿', '蓝', '紫', '黑', '白', '灰',
      '粉', '棕', '银', '金', '透明'
    ];

    colors.forEach(color => {
      if (title.includes(color)) {
        keywords.push({ type: 'color', value: color });
      }
    });

    // 尺寸关键词
    const sizePattern = /\d+(\.\d+)?(cm|mm|m|寸|英寸)/g;
    const sizeMatches = title.match(sizePattern);
    if (sizeMatches) {
      sizeMatches.forEach(size => {
        keywords.push({ type: 'size', value: size });
      });
    }

    return keywords;
  }

  /**
   * 标题分类
   * @param {string} title - 标题文本
   * @returns {string} 分类结果
   */
  categorizeTitleType(title) {
    // 根据标题内容判断商品类别
    const categories = {
      'fashion': ['服装', '鞋', '包', '配饰', '首饰'],
      'electronics': ['电子', '数码', '手机', '电脑', '相机'],
      'home': ['家居', '家具', '装饰', '厨具', '床品'],
      'beauty': ['美妆', '护肤', '化妆品', '香水'],
      'sports': ['运动', '健身', '户外', '体育'],
      'toys': ['玩具', '游戏', '模型', '益智'],
      'food': ['食品', '零食', '饮料', '茶', '咖啡'],
      'books': ['图书', '教材', '文具', '办公']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => title.includes(keyword))) {
        return category;
      }
    }

    return 'general';
  }

  /**
   * 计算标题可信度
   * @param {Object} titleInfo - 标题信息
   * @returns {number} 可信度 (0-100)
   */
  calculateTitleConfidence(titleInfo) {
    let confidence = 0;

    // 基础分数
    confidence += 20;

    // 提取方法加分
    if (titleInfo.extraction.method === 'primary') {
      confidence += 40;
    } else if (titleInfo.extraction.method === 'context') {
      confidence += 30;
    } else if (titleInfo.extraction.method === 'intelligent') {
      confidence += 25;
    } else {
      confidence += 15;
    }

    // 标题长度合理性
    const titleLength = titleInfo.title.length;
    if (titleLength >= 10 && titleLength <= 100) {
      confidence += 20;
    } else if (titleLength > 100 && titleLength <= 200) {
      confidence += 10;
    } else if (titleLength < 10) {
      confidence -= 15;
    }

    // 多部分标题加分
    if (titleInfo.hasMultipleParts) {
      confidence += 10;
    }

    // 元素数量合理性
    if (titleInfo.partCount >= 1 && titleInfo.partCount <= 3) {
      confidence += 10;
    } else if (titleInfo.partCount > 3) {
      confidence -= 5;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }

    // 检查必要字段
    if (!data.title || typeof data.title !== 'string') {
      return false;
    }

    // 检查标题长度
    if (data.title.length < this.config.minTextLength ||
      data.title.length > this.config.maxTextLength) {
      return false;
    }

    // 检查可信度
    if (data.confidence && data.confidence < 30) {
      return false;
    }

    return true;
  }

  /**
   * 数据格式化
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    if (!data) return null;

    return {
      ...data,
      title: data.title.trim().replace(/\s+/g, ' '),
      titleParts: data.titleParts ? data.titleParts.map(part => part.trim()) : [],
      formatted: true,
      formatTime: Date.now()
    };
  }
}

// 注册到全局作用域// 全局注册
if (typeof window !== 'undefined') {
  window.WholesaleProductTitleExtractor = WholesaleProductTitleExtractor;
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WholesaleProductTitleExtractor;
}