/**
 * UI冲突修复测试脚本
 * 测试多重UI实现冲突的修复效果
 */

console.log('🧪 开始UI冲突修复测试...');

/**
 * 模拟物流数据
 */
const mockLogisticsData = {
  // 批发物流信息
  wholesaleLogistics: {
    success: true,
    name: '批发物流信息',
    data: {
      confidence: 90,
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起',
      deliveryTime: '承诺48小时发货'
    }
  },
  
  // 代发物流信息
  dropshipLogistics: {
    success: true,
    name: '代发物流信息',
    data: {
      confidence: 85,
      originCity: '广东东莞',
      destinationCity: '上海浦东',
      shippingFee: '¥15.00',
      deliveryTime: '24小时内发货'
    }
  }
};

/**
 * 测试结果格式化器
 */
function testResultFormatter() {
  console.log('\n📋 测试结果格式化器...');
  
  if (!window.resultFormatter) {
    console.error('❌ ResultFormatter 未找到');
    return false;
  }
  
  try {
    // 测试批发物流格式化
    const wholesaleHtml = window.resultFormatter.formatResultItem(
      'wholesale-logistics',
      mockLogisticsData.wholesaleLogistics,
      'product'
    );
    console.log('✅ 批发物流格式化成功');
    console.log('HTML长度:', wholesaleHtml.length);
    
    // 测试代发物流格式化
    const dropshipHtml = window.resultFormatter.formatResultItem(
      'dropship-logistics',
      mockLogisticsData.dropshipLogistics,
      'product'
    );
    console.log('✅ 代发物流格式化成功');
    console.log('HTML长度:', dropshipHtml.length);
    
    return true;
  } catch (error) {
    console.error('❌ 结果格式化器测试失败:', error);
    return false;
  }
}

/**
 * 测试UI管理器
 */
function testUIManager() {
  console.log('\n🎨 测试UI管理器...');
  
  if (!window.uiManager) {
    console.error('❌ UIManager 未找到');
    return false;
  }
  
  try {
    // 测试批发物流格式化
    const wholesaleHtml = window.uiManager.formatResultItem(
      'wholesale-logistics',
      mockLogisticsData.wholesaleLogistics
    );
    console.log('✅ UI管理器批发物流格式化成功');
    console.log('HTML长度:', wholesaleHtml.length);
    
    // 测试代发物流格式化
    const dropshipHtml = window.uiManager.formatResultItem(
      'dropship-logistics',
      mockLogisticsData.dropshipLogistics
    );
    console.log('✅ UI管理器代发物流格式化成功');
    console.log('HTML长度:', dropshipHtml.length);
    
    return true;
  } catch (error) {
    console.error('❌ UI管理器测试失败:', error);
    return false;
  }
}

/**
 * 测试结果显示
 */
function testResultsDisplay() {
  console.log('\n📊 测试结果显示...');
  
  if (!window.uiManager) {
    console.error('❌ UIManager 未找到');
    return false;
  }
  
  try {
    // 创建测试结果Map
    const testResults = new Map();
    testResults.set('wholesale-logistics', mockLogisticsData.wholesaleLogistics);
    testResults.set('dropship-logistics', mockLogisticsData.dropshipLogistics);
    
    // 显示结果公告板
    window.uiManager.showResultsBoard(testResults);
    console.log('✅ 结果公告板显示成功');
    
    // 检查是否只有一个结果公告板
    const boards = document.querySelectorAll('.extractor-results-board');
    if (boards.length === 1) {
      console.log('✅ 只有一个结果公告板，无重复显示');
    } else {
      console.warn(`⚠️ 发现 ${boards.length} 个结果公告板，可能存在重复显示`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 结果显示测试失败:', error);
    return false;
  }
}

/**
 * 检查冲突逻辑是否已移除
 */
function checkConflictRemoval() {
  console.log('\n🔍 检查冲突逻辑是否已移除...');
  
  // 检查UI管理器源码
  const uiManagerSource = window.uiManager.formatResultItem.toString();
  
  // 检查是否还包含旧的物流处理逻辑
  const hasOldLogisticsLogic = uiManagerSource.includes('originCity') && 
                               uiManagerSource.includes('destinationCity') &&
                               uiManagerSource.includes('shippingFee');
  
  if (hasOldLogisticsLogic) {
    console.warn('⚠️ UI管理器中仍包含旧的物流处理逻辑');
    return false;
  } else {
    console.log('✅ 旧的物流处理逻辑已成功移除');
    return true;
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行所有测试...\n');
  
  const tests = [
    { name: '结果格式化器测试', fn: testResultFormatter },
    { name: 'UI管理器测试', fn: testUIManager },
    { name: '结果显示测试', fn: testResultsDisplay },
    { name: '冲突逻辑检查', fn: checkConflictRemoval }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = test.fn();
      if (result) {
        passedTests++;
        console.log(`✅ ${test.name} 通过`);
      } else {
        console.log(`❌ ${test.name} 失败`);
      }
    } catch (error) {
      console.error(`💥 ${test.name} 异常:`, error);
    }
  }
  
  console.log(`\n📊 测试结果: ${passedTests}/${tests.length} 通过`);
  
  if (passedTests === tests.length) {
    console.log('🎉 所有测试通过！UI冲突问题已修复');
  } else {
    console.log('⚠️ 部分测试失败，需要进一步检查');
  }
}

// 等待页面加载完成后运行测试
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', runAllTests);
} else {
  // 延迟执行，确保所有组件都已初始化
  setTimeout(runAllTests, 1000);
}

// 导出测试函数供手动调用
window.uiConflictFixTest = {
  runAllTests,
  testResultFormatter,
  testUIManager,
  testResultsDisplay,
  checkConflictRemoval,
  mockLogisticsData
};

console.log('📝 UI冲突修复测试脚本已加载');
console.log('💡 可以通过 window.uiConflictFixTest.runAllTests() 手动运行测试');
