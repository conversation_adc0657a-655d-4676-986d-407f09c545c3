<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>1688商品信息提取器</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <!-- 头部 -->
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <img src="../icons/icon32.png" alt="Logo" class="logo-icon">
          <h1 class="title">1688商品信息提取器</h1>
        </div>
        <div class="version">v3.0.0</div>
      </div>
    </header>

    <!-- 页面状态显示 -->
    <section class="page-status">
      <div class="status-item">
        <span class="status-label">页面类型:</span>
        <span class="status-value" id="pageType">
          <span class="page-type-icon">❓</span>
          <span class="page-type-text">检测中...</span>
        </span>
      </div>
      <div class="status-item">
        <span class="status-label">状态:</span>
        <span class="status-value" id="extractionStatus">
          <span class="status-indicator idle"></span>
          <span class="status-text">就绪</span>
        </span>
      </div>
    </section>

    <!-- 进度显示区域 -->
    <section class="progress-section" id="progressSection">
      <h3 class="section-title">
        <span class="section-icon">📊</span>
        抓取进度
      </h3>
      <div class="progress-container" id="progressContainer">
        <!-- 进度条将通过JavaScript动态生成 -->
      </div>
      <div class="global-progress">
        <div class="global-progress-bar">
          <div class="global-progress-fill" id="globalProgressFill"></div>
        </div>
        <div class="global-progress-text" id="globalProgressText">0%</div>
      </div>
    </section>

    <!-- 结果显示区域 -->
    <section class="results-section" id="resultsSection">
      <h3 class="section-title">
        <span class="section-icon">📋</span>
        抓取结果
      </h3>
      <div class="results-container" id="resultsContainer">
        <div class="no-results" id="noResults">
          <div class="no-results-icon">📝</div>
          <div class="no-results-text">暂无抓取结果</div>
          <div class="no-results-hint">点击"开始抓取"按钮开始提取信息</div>
        </div>
      </div>
    </section>

    <!-- 操作按钮区域 -->
    <section class="actions-section">
      <div class="action-buttons">
        <button class="btn btn-primary" id="startBtn">
          <span class="btn-icon">▶️</span>
          <span class="btn-text">开始抓取</span>
        </button>
        <button class="btn btn-secondary" id="stopBtn" disabled>
          <span class="btn-icon">⏹️</span>
          <span class="btn-text">停止</span>
        </button>
        <button class="btn btn-secondary" id="resetBtn">
          <span class="btn-icon">🔄</span>
          <span class="btn-text">重置</span>
        </button>
      </div>
      <div class="secondary-actions">
        <button class="btn btn-outline" id="exportBtn" disabled>
          <span class="btn-icon">💾</span>
          <span class="btn-text">导出数据</span>
        </button>
        <button class="btn btn-outline" id="settingsBtn">
          <span class="btn-icon">⚙️</span>
          <span class="btn-text">设置</span>
        </button>
      </div>
    </section>

    <!-- 设置面板 -->
    <section class="settings-panel" id="settingsPanel" style="display: none;">
      <h3 class="section-title">
        <span class="section-icon">⚙️</span>
        设置选项
      </h3>
      <div class="settings-content">
        <div class="setting-item">
          <label class="setting-label">
            <input type="checkbox" id="autoStartSetting" class="setting-checkbox">
            <span class="setting-text">自动开始抓取</span>
          </label>
          <div class="setting-description">页面加载完成后自动开始信息提取</div>
        </div>
        <div class="setting-item">
          <label class="setting-label">
            <input type="checkbox" id="notificationSetting" class="setting-checkbox">
            <span class="setting-text">显示通知</span>
          </label>
          <div class="setting-description">抓取完成后显示系统通知</div>
        </div>
        <div class="setting-item">
          <label class="setting-label">
            <input type="checkbox" id="debugModeSetting" class="setting-checkbox">
            <span class="setting-text">调试模式</span>
          </label>
          <div class="setting-description">显示详细的调试信息</div>
        </div>
      </div>
      <div class="settings-actions">
        <button class="btn btn-primary" id="saveSettingsBtn">
          <span class="btn-icon">💾</span>
          <span class="btn-text">保存设置</span>
        </button>
        <button class="btn btn-secondary" id="cancelSettingsBtn">
          <span class="btn-icon">❌</span>
          <span class="btn-text">取消</span>
        </button>
      </div>
    </section>

    <!-- 错误提示 -->
    <div class="error-message" id="errorMessage" style="display: none;">
      <div class="error-icon">⚠️</div>
      <div class="error-text" id="errorText"></div>
      <button class="error-close" id="errorClose">×</button>
    </div>

    <!-- 成功提示 -->
    <div class="success-message" id="successMessage" style="display: none;">
      <div class="success-icon">✅</div>
      <div class="success-text" id="successText"></div>
      <button class="success-close" id="successClose">×</button>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在处理...</div>
    </div>
  </div>

  <!-- 脚本文件 -->
  <script src="popup.js"></script>
</body>
</html>