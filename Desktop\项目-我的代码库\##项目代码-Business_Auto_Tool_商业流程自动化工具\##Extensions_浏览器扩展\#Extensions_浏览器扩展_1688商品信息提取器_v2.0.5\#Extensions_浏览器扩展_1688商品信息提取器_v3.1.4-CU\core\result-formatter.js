/**
 * 结果格式化器 - 统一的数据格式化接口
 * 替代复杂的UI委托系统，提供简洁高效的格式化方案
 * <AUTHOR>
 * @version 1.0.0
 */

class ResultFormatter {
  constructor() {
    this.formatters = new Map();
    this.initFormatters();
  }

  /**
   * 初始化格式化器
   */
  initFormatters() {
    // 批发模式格式化器
    this.formatters.set('wholesale', {
      priceFormat: 'wholesale-price',
      showBatchInfo: true,
      keywordLimit: 5,
      priceLabel: '批发价',
      specialFields: ['couponPrice', 'priceRange', 'minBatch']
    });

    // 代发模式格式化器
    this.formatters.set('consign', {
      priceFormat: 'consign-price',
      showRetailPrice: true,
      keywordLimit: 3,
      priceLabel: '代发价',
      specialFields: ['retailPrice', 'profit', 'minOrder']
    });

    // 默认格式化器
    this.formatters.set('default', {
      priceFormat: 'default-price',
      showBatchInfo: false,
      keywordLimit: 4,
      priceLabel: '价格',
      specialFields: []
    });
  }

  /**
   * 格式化结果项
   * @param {string} extractorId - 抽取器ID
   * @param {Object} result - 结果数据
   * @param {string} pageType - 页面类型
   * @returns {string} 格式化的HTML
   */
  formatResultItem(extractorId, result, pageType = 'default') {
    try {
      // 调试信息
      console.log('🔍 [ResultFormatter] 格式化结果:', {
        extractorId,
        pageType,
        hasTitle: !!result.data?.title,
        hasMerchant: !!(result.data?.name || result.data?.companyName),
        hasPrice: !!(result.data?.price || result.data?.couponPrice),
        hasSpecs: !!(result.data?.attributes || result.data?.specs),
        dataKeys: Object.keys(result.data || {}),
        titleValue: result.data?.title,
        merchantValue: result.data?.name || result.data?.companyName
      });
      
      // 获取对应的格式化配置
      const config = this.formatters.get(pageType) || this.formatters.get('default');
      
      // 基础信息
      const name = result.name || extractorId;
      const confidence = result.data?.confidence || 0;
      const confidenceClass = this.getConfidenceClass(confidence);

      // 构建HTML结构
      let content = '';

      // 优先显示标题信息，特别是批发模式
      if (result.data?.title) {
        content = this.formatTitleResult(result.data, config);
      } else if (result.data?.name || result.data?.companyName) {
        content = this.formatMerchantResult(result.data, config);
      } else if (result.data?.price || result.data?.couponPrice || result.data?.priceRange) {
        content = this.formatPriceResult(result.data, config, pageType);
      } else if (result.data?.attributes || result.data?.specs || result.data?.specifications) {
        content = this.formatSpecsResult(result.data, config);
      } else if (result.data?.rating || result.data?.score) {
        content = this.formatRatingResult(result.data, config);
      } else if (result.data?.imageCount || result.data?.images) {
        content = this.formatImagesResult(result.data, config);
      } else {
        content = this.formatGenericResult(result.data, config);
      }

      // 返回完整的结果项HTML
      return `
        <div class="result-item ${confidenceClass}">
          <div class="result-header">
            <div class="result-name">${name}</div>
            <div class="result-confidence ${confidenceClass}">${confidence}%</div>
          </div>
          <div class="result-content">
            ${content}
          </div>
        </div>
      `;

    } catch (error) {
      console.error('格式化结果项失败:', error);
      return this.formatErrorResult(extractorId, error);
    }
  }

  /**
   * 格式化标题结果
   */
  formatTitleResult(data, config) {
    // 确保标题存在
    const title = data.title || data.name || '商品标题';
    let content = `<div class="result-value title-content">${title}</div>`;
    
    // 添加标题长度信息
    if (title.length > 20) {
      content += `<div class="result-detail title-info">标题长度: ${title.length}字</div>`;
    }
    
    // 关键词
    if (data.keywords && data.keywords.length > 0) {
      content += `<div class="result-keywords">`;
      data.keywords.slice(0, config.keywordLimit || 5).forEach(keyword => {
        const keywordValue = typeof keyword === 'object' ? keyword.value : keyword;
        content += `<span class="keyword-tag">${keywordValue}</span>`;
      });
      content += `</div>`;
    }

    // 成交信息（批发模式）
    if (config.showBatchInfo && data.salesInfo?.salesText) {
      content += `<div class="result-detail wholesale-sales">${data.salesInfo.salesText}</div>`;
    }
    
    // 标题特征分析
    if (data.titleParts && data.titleParts.length > 1) {
      content += `<div class="result-detail title-parts">标题分段: ${data.titleParts.length}段</div>`;
    }

    return content;
  }

  /**
   * 格式化价格结果
   */
  formatPriceResult(data, config, pageType) {
    let content = '';

    if (pageType === 'wholesale') {
      // 批发模式价格
      if (data.couponPrice) {
        const price = this.extractPriceValue(data.couponPrice);
        content = `<div class="result-value">券后价: ¥${price}</div>`;
      } else if (data.priceRange && Array.isArray(data.priceRange) && data.priceRange.length > 0) {
        const minPrice = this.extractPriceValue(data.priceRange[0]);
        const maxPrice = this.extractPriceValue(data.priceRange[data.priceRange.length - 1]);
        content = `<div class="result-value">${config.priceLabel}: ¥${minPrice} ~ ¥${maxPrice}</div>`;
      } else if (data.price) {
        const price = this.extractPriceValue(data.price);
        content = `<div class="result-value">${config.priceLabel}: ¥${price}</div>`;
      }

      // 起批量信息
      if (data.minBatch) {
        const quantity = this.extractValue(data.minBatch, 'quantity');
        const unit = this.extractValue(data.minBatch, 'unit', '个');
        content += `<div class="result-detail">${quantity}${unit}起批</div>`;
      }
    } else if (pageType === 'consign') {
      // 代发模式价格
      const price = this.extractPriceValue(data.price);
      content = `<div class="result-value">${config.priceLabel}: ¥${price}</div>`;

      // 起订量
      if (data.minOrder) {
        const quantity = this.extractValue(data.minOrder, 'quantity');
        const unit = this.extractValue(data.minOrder, 'unit', '个');
        content += `<div class="result-detail">起订量: ${quantity}${unit}</div>`;
      }

      // 建议零售价
      if (config.showRetailPrice && data.retailPrice) {
        content += `<div class="result-detail consign-retail">建议零售价: ¥${data.retailPrice}</div>`;
      }

      // 利润信息
      if (data.profit) {
        content += `<div class="result-detail consign-profit">预估利润: ¥${data.profit}</div>`;
      }
    }

    return content;
  }

  /**
   * 格式化评价结果
   */
  formatRatingResult(data, config) {
    const rating = data.rating || data.score;
    let content = `<div class="result-value">评分: ${rating}分</div>`;

    if (data.reviewCount) {
      content += `<div class="result-detail">评价数: ${data.reviewCount}</div>`;
    }

    if (data.salesCount) {
      content += `<div class="result-detail">成交数: ${data.salesCount}</div>`;
    }

    return content;
  }

  /**
   * 格式化图片结果
   */
  formatImagesResult(data, config) {
    const count = data.imageCount || (data.images ? data.images.length : 0);
    return `<div class="result-value">图片: ${count} 张</div>`;
  }

  /**
   * 格式化规格结果
   */
  formatSpecsResult(data, config) {
    const specs = data.attributes || data.specs || data.specifications;
    
    if (!specs || typeof specs !== 'object') {
      return `<div class="result-value">商品属性: 暂无数据</div>`;
    }
    
    const specEntries = Object.entries(specs);
    const specCount = specEntries.length;
    
    let content = `<div class="result-value">商品属性: ${specCount} 项</div>`;
    
    // 显示所有属性，用户要求看到全部
    specEntries.forEach(([key, value]) => {
      // 处理值的显示，避免过长
      let displayValue = String(value);
      if (displayValue.length > 50) {
        displayValue = displayValue.substring(0, 50) + '...';
      }
      content += `<div class="result-detail">${key}: ${displayValue}</div>`;
    });
    
    // 如果属性很多，添加折叠样式提示
    if (specCount > 10) {
      content += `<div class="result-detail specs-summary">共 ${specCount} 项完整属性</div>`;
    }

    return content;
  }

  /**
   * 格式化商家结果
   */
  formatMerchantResult(data, config) {
    const merchantName = data.name || data.companyName || data.merchantName || '商家信息';
    let content = `<div class="result-value merchant-name">${merchantName}</div>`;
    
    // 商家名称长度信息
    if (merchantName.length > 10) {
      content += `<div class="result-detail merchant-info">商家名称: ${merchantName.length}字</div>`;
    }
    
    // 地址信息
    if (data.location) {
      content += `<div class="result-detail">地址: ${data.location}</div>`;
    }
    
    // 商家类型
    if (data.businessType || data.type) {
      content += `<div class="result-detail">类型: ${data.businessType || data.type}</div>`;
    }
    
    // 成立年份
    if (data.establishedYear) {
      content += `<div class="result-detail">成立: ${data.establishedYear}年</div>`;
    }
    
    // 商家关键词
    if (data.keywords && data.keywords.length > 0) {
      content += `<div class="result-detail">关键词: ${data.keywords.slice(0, 3).join(', ')}</div>`;
    }
    
    // 商家评级
    if (data.rating) {
      content += `<div class="result-detail">评级: ${data.rating}</div>`;
    }

    return content;
  }

  /**
   * 格式化通用结果
   */
  formatGenericResult(data, config) {
    if (!data || typeof data !== 'object') {
      return `<div class="result-value">数据提取完成</div>`;
    }

    // 尝试显示一些基本信息
    const keys = Object.keys(data).filter(key => 
      !['confidence', 'timestamp', 'success'].includes(key)
    );

    if (keys.length > 0) {
      const firstKey = keys[0];
      const value = data[firstKey];
      return `<div class="result-value">${firstKey}: ${this.formatValue(value)}</div>`;
    }

    return `<div class="result-value">数据提取完成</div>`;
  }

  /**
   * 格式化错误结果
   */
  formatErrorResult(extractorId, error) {
    return `
      <div class="result-item error">
        <div class="result-header">
          <div class="result-name">${extractorId}</div>
          <div class="result-confidence low">0%</div>
        </div>
        <div class="result-content">
          <div class="result-value">格式化失败: ${error.message}</div>
        </div>
      </div>
    `;
  }

  /**
   * 获取置信度样式类
   */
  getConfidenceClass(confidence) {
    if (confidence >= 80) return 'high';
    if (confidence >= 60) return 'medium';
    return 'low';
  }

  /**
   * 提取价格值
   */
  extractPriceValue(priceData) {
    if (typeof priceData === 'string' || typeof priceData === 'number') {
      return priceData;
    }
    if (typeof priceData === 'object' && priceData) {
      return priceData.value || priceData.price || priceData.amount || '未知';
    }
    return '未知';
  }

  /**
   * 提取对象值
   */
  extractValue(obj, key, defaultValue = '') {
    if (typeof obj === 'object' && obj && obj[key] !== undefined) {
      return obj[key];
    }
    if (typeof obj === 'string' || typeof obj === 'number') {
      return obj;
    }
    return defaultValue;
  }

  /**
   * 格式化值
   */
  formatValue(value) {
    if (Array.isArray(value)) {
      return value.length > 0 ? `${value.length} 项` : '无';
    }
    if (typeof value === 'object' && value) {
      return Object.keys(value).length > 0 ? '详细信息' : '无';
    }
    return String(value).substring(0, 50);
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.ResultFormatter = ResultFormatter;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResultFormatter;
}