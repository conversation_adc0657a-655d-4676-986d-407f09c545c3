/**
 * 1688验证码自动关闭监控器
 * 专门用于监控和自动关闭1688网站的验证码弹窗
 * 作者: Trae AI Assistant
 * 版本: 1.0.0
 */

class VerificationAutoCloser {
    constructor(loggerManager) {
        this.loggerManager = loggerManager;
        this.isMonitoring = false;
        this.monitorInterval = null;
        this.observer = null;
        this.checkIntervalMs = 1000; // 每秒检查一次
        this.maxRetries = 3;
        this.retryCount = 0;
        this.pageMode = this.detectPageMode(); // 检测页面模式
        
        // 验证码弹窗选择器
        this.verificationSelectors = [
            '.verification-modal',
            '.verify-modal', 
            '.captcha-modal',
            '[class*="verification"]',
            '[class*="verify"]',
            '[class*="captcha"]',
            '.nc_wrapper', // 阿里云验证码
            '.nc-container', // 阿里云验证码容器
            '.slide-verify', // 滑动验证
            '.verify-code', // 验证码
            '.security-verify', // 安全验证
            '.human-verify', // 人机验证
            '[id*="verify"]', // ID包含verify的元素
            '[id*="captcha"]', // ID包含captcha的元素
            '.modal-dialog', // 通用模态框
            '.popup-dialog', // 弹窗对话框
            '.J_MIDDLEWARE_FRAME_WIDGET' // 1688特有的中间件框架
        ];
        
        // 代发模式特有的验证码选择器
        this.consignModeSelectors = [
            '.consign-verify-modal',
            '.dropship-verification',
            '[class*="consign"][class*="verify"]',
            '[class*="dropship"][class*="verify"]',
            '.agent-verify',
            '.proxy-verify'
        ];
        
        // 关闭按钮选择器
        this.closeButtonSelectors = [
            '.close',
            '.close-btn',
            '.modal-close',
            '[aria-label="关闭"]',
            '[aria-label="Close"]', 
            'button[title="关闭"]',
            'button[title="Close"]',
            '.icon-close',
            '.fa-times',
            '.fa-close',
            '.error-report-close',
            '.dialog-close',
            '.popup-close',
            '.modal-header .close',
            '.btn-close',
            '[class*="close"]',
            'button:contains("×")',
            'button:contains("✕")',
            'span:contains("×")',
            'span:contains("✕")',
            '.nc-close',
            '.verify-close'
        ];
        
        // 验证码内容关键词
        this.verificationKeywords = [
            '验证', '滑动验证', '点击验证', '图形验证', '拖拽验证',
            'verification', 'captcha', 'verify', 'slide', 'drag',
            '请拖动', '请点击', '请滑动', '安全验证', '人机验证',
            '请按住滑块', '拖动完成验证', '滑动解锁', '向右滑动',
            '请完成安全验证', '请拖动滑块', '滑块验证', '行为验证',
            '智能验证', '无感验证', '请稍候', '验证中', '加载中',
            '网络异常', '请重试', '验证失败', '验证成功',
            'nc_wrapper', 'nc-container', 'slide-verify'
        ];
        
        this.log('VerificationAutoCloser initialized');
    }
    
    /**
     * 开始监控验证码弹窗
     */
    startMonitoring() {
        if (this.isMonitoring) {
            this.log('Already monitoring verification popups');
            return;
        }
        
        this.isMonitoring = true;
        this.log('Starting verification popup monitoring');
        
        // 定时检查
        this.monitorInterval = setInterval(() => {
            this.checkAndCloseVerificationPopups();
        }, this.checkIntervalMs);
        
        // DOM变化监听
        this.startDOMObserver();
        
        // 立即执行一次检查
        setTimeout(() => {
            this.checkAndCloseVerificationPopups();
        }, 500); // 延迟500ms执行首次检查
        
        // 页面加载完成后再次检查
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    this.checkAndCloseVerificationPopups();
                }, 1000);
            });
        }
    }
    
    /**
     * 停止监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        
        this.isMonitoring = false;
        this.log('Stopping verification popup monitoring');
        
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }
        
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }
    
    /**
     * 启动DOM观察器
     */
    startDOMObserver() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        this.observer = new MutationObserver((mutations) => {
            let shouldCheck = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查新增的节点是否包含验证码相关内容
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (this.isVerificationElement(node)) {
                                shouldCheck = true;
                                break;
                            }
                        }
                    }
                }
            });
            
            if (shouldCheck) {
                setTimeout(() => {
                    this.checkAndCloseVerificationPopups();
                }, 100); // 延迟100ms确保DOM完全加载
            }
        });
        
        this.observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * 检查并关闭验证码弹窗
     */
    checkAndCloseVerificationPopups() {
        try {
            // 使用增强的检测方法
            const verificationPopups = this.findVerificationPopupsEnhanced();
            
            if (verificationPopups.length > 0) {
                this.log(`Found ${verificationPopups.length} verification popup(s) (${this.pageMode} mode), attempting to close...`);
                
                // 代发模式下使用更激进的策略
                if (this.pageMode === 'consign') {
                    verificationPopups.forEach((popup, index) => {
                        setTimeout(() => {
                            this.log(`Aggressively closing verification popup ${index + 1}`);
                            this.aggressiveClose(popup);
                        }, 50 * index); // 代发模式下更快的处理间隔
                    });
                } else {
                    // 批发模式下使用标准策略
                    verificationPopups.forEach((popup, index) => {
                        setTimeout(() => {
                            this.log(`Attempting to close verification popup ${index + 1}`);
                            this.closeVerificationPopup(popup);
                        }, 100 * index); // 延迟处理多个弹窗
                    });
                }
            } else {
                // 定期检查是否有新的验证码弹窗出现
                this.log(`No verification popups found in ${this.pageMode} mode`, 'debug');
            }
        } catch (error) {
            this.log(`Error checking verification popups: ${error.message}`, 'error');
        }
    }
    
    /**
     * 查找验证码弹窗
     */
    findVerificationPopups() {
        const popups = [];
        const foundSelectors = [];
        
        // 使用选择器查找
        this.verificationSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (this.isVisibleElement(element) && this.isVerificationContent(element)) {
                        // 避免重复添加同一个元素
                        if (!popups.includes(element)) {
                            popups.push(element);
                            foundSelectors.push(selector);
                        }
                    }
                });
            } catch (error) {
                this.log(`Error with selector ${selector}: ${error.message}`, 'debug');
            }
        });
        
        if (popups.length > 0) {
            this.log(`Found verification popups with selectors: ${foundSelectors.join(', ')}`);
        }
        
        // 去重
        return [...new Set(popups)];
    }
    
    /**
     * 检查元素是否可见
     */
    isVisibleElement(element) {
        if (!element) {
            return false;
        }
        
        try {
            const style = window.getComputedStyle(element);
            const rect = element.getBoundingClientRect();
            
            return (
                element.offsetWidth > 0 &&
                element.offsetHeight > 0 &&
                style.display !== 'none' &&
                style.visibility !== 'hidden' &&
                style.opacity !== '0' &&
                rect.width > 0 &&
                rect.height > 0 &&
                // 检查元素是否在视口内或接近视口
                rect.top > -window.innerHeight &&
                rect.left > -window.innerWidth
            );
        } catch (error) {
            this.log(`Error checking element visibility: ${error.message}`, 'debug');
            return false;
        }
    }
    
    /**
     * 检查是否为验证码元素
     */
    isVerificationElement(element) {
        if (!element || element.nodeType !== Node.ELEMENT_NODE) {
            return false;
        }
        
        const className = (element.className && typeof element.className === 'string') ? element.className : '';
        const id = element.id || '';
        
        return this.verificationSelectors.some(selector => {
            try {
                return element.matches(selector.replace(/:/g, ''));
            } catch (error) {
                try {
                    return className.includes(selector.replace(/[.\[\]]/g, '')) ||
                           id.includes(selector.replace(/[.\[\]]/g, ''));
                } catch (innerError) {
                    console.warn('检查验证码元素时出错:', innerError, element);
                    return false;
                }
            }
        });
    }
    
    /**
     * 检查是否包含验证码内容
     */
    isVerificationContent(element) {
        const text = element.textContent || element.innerText || '';
        const className = (element.className && typeof element.className === 'string') ? element.className : '';
        const id = element.id || '';
        
        // 检查文本内容
        const hasKeyword = this.verificationKeywords.some(keyword => {
            try {
                return text.toLowerCase().includes(keyword.toLowerCase());
            } catch (error) {
                console.warn('检查文本关键词时出错:', error, element);
                return false;
            }
        });
        
        // 检查类名和ID
        const hasVerifyClass = this.verificationKeywords.some(keyword => {
            try {
                return className.toLowerCase().includes(keyword.toLowerCase()) ||
                       id.toLowerCase().includes(keyword.toLowerCase());
            } catch (error) {
                console.warn('检查元素关键词时出错:', error, element);
                return false;
            }
        });
        
        return hasKeyword || hasVerifyClass;
    }
    
    /**
     * 关闭验证码弹窗
     */
    closeVerificationPopup(popup, index = 0) {
        if (!popup || !this.isVisibleElement(popup)) {
            this.log('Popup is not visible or does not exist', 'debug');
            return false;
        }
        
        this.log(`Attempting to close verification popup ${index + 1}`);
        
        try {
            // 增加重试机制
            let attempts = 0;
            const maxAttempts = 3;
            
            const tryClose = () => {
                attempts++;
                this.log(`Close attempt ${attempts}/${maxAttempts}`);
                
                // 方法1: 查找关闭按钮
                const closeButton = this.findCloseButton(popup);
                if (closeButton && this.clickElement(closeButton)) {
                    this.log('Successfully closed popup using close button');
                    return true;
                }
                
                // 方法2: 尝试点击遮罩层
                if (this.clickOverlay(popup)) {
                    this.log('Successfully closed popup by clicking overlay');
                    return true;
                }
                
                // 方法3: 按ESC键
                if (this.pressEscapeKey()) {
                    this.log('Successfully closed popup using ESC key');
                    return true;
                }
                
                // 方法4: 直接隐藏元素
                if (this.hideElement(popup)) {
                    this.log('Successfully hid popup element');
                    return true;
                }
                
                return false;
            };
            
            // 尝试关闭
            if (tryClose()) {
                return true;
            }
            
            // 如果第一次失败，等待一段时间后重试
            if (attempts < maxAttempts) {
                setTimeout(() => {
                    if (this.isVisibleElement(popup)) {
                        tryClose();
                    }
                }, 1000);
            }
            
            this.log('Failed to close verification popup after all attempts', 'warn');
            return false;
            
        } catch (error) {
            this.log(`Error closing verification popup: ${error.message}`, 'error');
            return false;
        }
    }
    
    /**
     * 查找关闭按钮
     */
    findCloseButton(popup) {
        // 在弹窗内查找关闭按钮
        for (let selector of this.closeButtonSelectors) {
            try {
                const button = popup.querySelector(selector);
                if (button && this.isVisibleElement(button)) {
                    return button;
                }
            } catch (error) {
                // 忽略选择器错误
            }
        }
        
        // 在弹窗父级查找关闭按钮
        const parent = popup.parentElement;
        if (parent) {
            for (let selector of this.closeButtonSelectors) {
                try {
                    const button = parent.querySelector(selector);
                    if (button && this.isVisibleElement(button)) {
                        return button;
                    }
                } catch (error) {
                    // 忽略选择器错误
                }
            }
        }
        
        return null;
    }
    
    /**
     * 点击元素
     */
    clickElement(element) {
        try {
            // 尝试多种点击方式
            const methods = [
                () => element.click(),
                () => {
                    const event = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    element.dispatchEvent(event);
                },
                () => {
                    const rect = element.getBoundingClientRect();
                    const x = rect.left + rect.width / 2;
                    const y = rect.top + rect.height / 2;
                    
                    const event = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        clientX: x,
                        clientY: y
                    });
                    element.dispatchEvent(event);
                }
            ];
            
            for (let method of methods) {
                try {
                    method();
                    return true;
                } catch (error) {
                    continue;
                }
            }
            
            return false;
        } catch (error) {
            this.log(`Error clicking element: ${error.message}`, 'error');
            return false;
        }
    }
    
    /**
     * 点击遮罩层
     */
    clickOverlay(popup) {
        try {
            // 查找可能的遮罩层
            const overlaySelectors = [
                '.modal-backdrop',
                '.overlay',
                '.mask',
                '[class*="backdrop"]',
                '[class*="overlay"]',
                '[class*="mask"]'
            ];
            
            for (let selector of overlaySelectors) {
                const overlay = document.querySelector(selector);
                if (overlay && this.isVisibleElement(overlay)) {
                    return this.clickElement(overlay);
                }
            }
            
            return false;
        } catch (error) {
            return false;
        }
    }
    
    /**
     * 按ESC键
     */
    pressEscapeKey() {
        try {
            const event = new KeyboardEvent('keydown', {
                key: 'Escape',
                keyCode: 27,
                which: 27,
                bubbles: true,
                cancelable: true
            });
            
            document.dispatchEvent(event);
            return true;
        } catch (error) {
            return false;
        }
    }
    
    /**
     * 隐藏元素
     */
    hideElement(element) {
        try {
            element.style.display = 'none';
            element.style.visibility = 'hidden';
            element.style.opacity = '0';
            
            // 尝试移除元素
            setTimeout(() => {
                try {
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                } catch (error) {
                    // 忽略移除错误
                }
            }, 100);
            
            return true;
        } catch (error) {
            return false;
        }
    }
    
    /**
     * 日志记录
     */
    log(message, level = 'info') {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [VerificationAutoCloser] ${message}`;
        
        if (this.loggerManager) {
            this.loggerManager.log('VerificationAutoCloser', message, level);
        } else {
            console.log(logMessage);
        }
    }
    
    /**
     * 获取监控状态
     */
    getStatus() {
        return {
            isMonitoring: this.isMonitoring,
            checkInterval: this.checkIntervalMs,
            retryCount: this.retryCount,
            maxRetries: this.maxRetries
        };
    }
    
    /**
     * 手动检查验证码弹窗
     */
    manualCheck() {
        this.log('Manual verification check triggered');
        this.checkAndCloseVerificationPopups();
    }
    
    /**
     * 检测页面模式
     */
    detectPageMode() {
        try {
            const url = window.location.href;
            if (url.includes('sk=consign') || url.includes('代发')) {
                this.log('Detected consign mode page');
                return 'consign';
            } else {
                this.log('Detected wholesale mode page');
                return 'wholesale';
            }
        } catch (error) {
            this.log('Failed to detect page mode: ' + error.message, 'warn');
            return 'wholesale'; // 默认为批发模式
        }
    }
    
    /**
     * 获取适合当前页面模式的验证码选择器
     */
    getVerificationSelectors() {
        const baseSelectors = [...this.verificationSelectors];
        
        if (this.pageMode === 'consign') {
            // 代发模式下添加特有选择器，并调整检查频率
            this.checkIntervalMs = 800; // 代发模式下更频繁检查
            return [...baseSelectors, ...this.consignModeSelectors];
        }
        
        return baseSelectors;
    }
    
    /**
     * 增强的验证码检测（针对代发模式优化）
     */
    findVerificationPopupsEnhanced() {
        const selectors = this.getVerificationSelectors();
        const popups = [];
        const foundSelectors = [];
        
        try {
            for (const selector of selectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        if (this.isVisibleElement(element) && this.isVerificationElement(element)) {
                            popups.push(element);
                            foundSelectors.push(selector);
                            
                            // 代发模式下的特殊处理
                            if (this.pageMode === 'consign') {
                                this.log(`Found consign mode verification popup with selector: ${selector}`);
                                // 立即尝试多种关闭方式
                                this.aggressiveClose(element);
                            }
                        }
                    }
                } catch (selectorError) {
                    this.log(`Selector error for ${selector}: ${selectorError.message}`, 'warn');
                }
            }
            
            // 去重
            const uniquePopups = [...new Set(popups)];
            
            if (uniquePopups.length > 0) {
                this.log(`Found ${uniquePopups.length} verification popups using selectors: ${foundSelectors.join(', ')}`);
            }
            
            return uniquePopups;
            
        } catch (error) {
            this.log('Error in enhanced popup detection: ' + error.message, 'error');
            return [];
        }
    }
    
    /**
     * 激进的关闭方式（用于代发模式）
     */
    aggressiveClose(popup) {
        try {
            // 方式1: 查找并点击关闭按钮
            const closeButton = this.findCloseButton(popup);
            if (closeButton) {
                this.clickElement(closeButton);
                this.log('Aggressively closed popup via close button');
                return true;
            }
            
            // 方式2: 点击遮罩层
            this.clickOverlay(popup);
            
            // 方式3: 按ESC键
            this.pressEscapeKey();
            
            // 方式4: 直接隐藏元素
            setTimeout(() => {
                if (this.isVisibleElement(popup)) {
                    this.hideElement(popup);
                    this.log('Aggressively hid popup element');
                }
            }, 500);
            
            return true;
            
        } catch (error) {
            this.log('Aggressive close failed: ' + error.message, 'warn');
            return false;
        }
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.VerificationAutoCloser = VerificationAutoCloser;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = VerificationAutoCloser;
}