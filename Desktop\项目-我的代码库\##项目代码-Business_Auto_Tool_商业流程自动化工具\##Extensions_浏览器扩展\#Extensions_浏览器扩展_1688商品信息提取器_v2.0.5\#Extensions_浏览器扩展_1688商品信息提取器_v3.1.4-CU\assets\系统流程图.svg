<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .module { font-family: Arial, sans-serif; font-size: 12px; fill: #34495e; }
      .flow-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .box { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 8; }
      .process-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .decision-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .data-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .ui-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 8; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #7f8c8d; stroke-width: 1; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">1688商品信息提取器 - 系统流程图</text>
  
  <!-- 用户访问1688页面 -->
  <rect x="50" y="60" width="120" height="60" class="box"/>
  <text x="110" y="85" text-anchor="middle" class="module">用户访问</text>
  <text x="110" y="105" text-anchor="middle" class="module">1688页面</text>
  
  <!-- URL检测器 -->
  <rect x="220" y="60" width="120" height="60" class="process-box"/>
  <text x="280" y="85" text-anchor="middle" class="module" fill="white">URL检测器</text>
  <text x="280" y="105" text-anchor="middle" class="module" fill="white">detector_url_pattern</text>
  
  <!-- 页面类型判断 -->
  <polygon points="420,60 480,90 420,120 360,90" class="decision-box"/>
  <text x="420" y="85" text-anchor="middle" class="module" fill="white">页面类型</text>
  <text x="420" y="105" text-anchor="middle" class="module" fill="white">判断</text>
  
  <!-- 批发模式分支 -->
  <rect x="520" y="20" width="140" height="60" class="data-box"/>
  <text x="590" y="40" text-anchor="middle" class="module" fill="white">批发模式</text>
  <text x="590" y="55" text-anchor="middle" class="module" fill="white">extractor_wholesale</text>
  <text x="590" y="70" text-anchor="middle" class="module" fill="white">_merchant_v1.0</text>
  
  <!-- 代发模式分支 -->
  <rect x="520" y="100" width="140" height="60" class="data-box"/>
  <text x="590" y="120" text-anchor="middle" class="module" fill="white">代发模式</text>
  <text x="590" y="135" text-anchor="middle" class="module" fill="white">extractor_consign</text>
  <text x="590" y="150" text-anchor="middle" class="module" fill="white">_merchant_v1.0</text>
  
  <!-- 进度管理器 -->
  <rect x="720" y="60" width="120" height="60" class="process-box"/>
  <text x="780" y="85" text-anchor="middle" class="module" fill="white">进度管理器</text>
  <text x="780" y="105" text-anchor="middle" class="module" fill="white">ProgressManager</text>
  
  <!-- DOM查询执行 -->
  <rect x="220" y="200" width="120" height="60" class="process-box"/>
  <text x="280" y="220" text-anchor="middle" class="module" fill="white">DOM查询</text>
  <text x="280" y="235" text-anchor="middle" class="module" fill="white">CSS选择器</text>
  <text x="280" y="250" text-anchor="middle" class="module" fill="white">执行</text>
  
  <!-- 数据提取处理 -->
  <rect x="380" y="200" width="120" height="60" class="data-box"/>
  <text x="440" y="220" text-anchor="middle" class="module" fill="white">数据提取</text>
  <text x="440" y="235" text-anchor="middle" class="module" fill="white">文本处理</text>
  <text x="440" y="250" text-anchor="middle" class="module" fill="white">格式化</text>
  
  <!-- 数据验证 -->
  <rect x="540" y="200" width="120" height="60" class="process-box"/>
  <text x="600" y="220" text-anchor="middle" class="module" fill="white">数据验证</text>
  <text x="600" y="235" text-anchor="middle" class="module" fill="white">完整性检查</text>
  <text x="600" y="250" text-anchor="middle" class="module" fill="white">错误处理</text>
  
  <!-- 后台数据处理 -->
  <rect x="700" y="200" width="120" height="60" class="process-box"/>
  <text x="760" y="220" text-anchor="middle" class="module" fill="white">后台处理</text>
  <text x="760" y="235" text-anchor="middle" class="module" fill="white">Background</text>
  <text x="760" y="250" text-anchor="middle" class="module" fill="white">Script</text>
  
  <!-- UI更新 -->
  <rect x="880" y="200" width="120" height="60" class="ui-box"/>
  <text x="940" y="220" text-anchor="middle" class="module" fill="white">UI更新</text>
  <text x="940" y="235" text-anchor="middle" class="module" fill="white">Popup界面</text>
  <text x="940" y="250" text-anchor="middle" class="module" fill="white">实时显示</text>
  
  <!-- 进度条组件 -->
  <rect x="880" y="300" width="120" height="60" class="ui-box"/>
  <text x="940" y="320" text-anchor="middle" class="module" fill="white">进度条</text>
  <text x="940" y="335" text-anchor="middle" class="module" fill="white">ui_progress_bar</text>
  <text x="940" y="350" text-anchor="middle" class="module" fill="white">_v1.0</text>
  
  <!-- 信息面板 -->
  <rect x="880" y="400" width="120" height="60" class="ui-box"/>
  <text x="940" y="420" text-anchor="middle" class="module" fill="white">信息面板</text>
  <text x="940" y="435" text-anchor="middle" class="module" fill="white">ui_info_panel</text>
  <text x="940" y="450" text-anchor="middle" class="module" fill="white">_v1.0</text>
  
  <!-- 错误处理模块 -->
  <rect x="50" y="350" width="120" height="60" class="decision-box"/>
  <text x="110" y="375" text-anchor="middle" class="module" fill="white">错误处理</text>
  <text x="110" y="395" text-anchor="middle" class="module" fill="white">异常捕获</text>
  
  <!-- 重试机制 -->
  <rect x="220" y="350" width="120" height="60" class="process-box"/>
  <text x="280" y="375" text-anchor="middle" class="module" fill="white">重试机制</text>
  <text x="280" y="395" text-anchor="middle" class="module" fill="white">智能重试</text>
  
  <!-- 日志记录 -->
  <rect x="380" y="350" width="120" height="60" class="data-box"/>
  <text x="440" y="375" text-anchor="middle" class="module" fill="white">日志记录</text>
  <text x="440" y="395" text-anchor="middle" class="module" fill="white">调试信息</text>
  
  <!-- 配置管理 -->
  <rect x="540" y="350" width="120" height="60" class="process-box"/>
  <text x="600" y="375" text-anchor="middle" class="module" fill="white">配置管理</text>
  <text x="600" y="395" text-anchor="middle" class="module" fill="white">模块配置</text>
  
  <!-- 扩展接口 -->
  <rect x="700" y="350" width="120" height="60" class="data-box"/>
  <text x="760" y="375" text-anchor="middle" class="module" fill="white">扩展接口</text>
  <text x="760" y="395" text-anchor="middle" class="module" fill="white">插件API</text>
  
  <!-- 主要流程箭头 -->
  <line x1="170" y1="90" x2="220" y2="90" class="arrow"/>
  <line x1="340" y1="90" x2="360" y2="90" class="arrow"/>
  
  <!-- 分支箭头 -->
  <line x1="450" y1="75" x2="520" y2="50" class="arrow"/>
  <line x1="450" y1="105" x2="520" y2="130" class="arrow"/>
  
  <!-- 汇聚到进度管理 -->
  <line x1="660" y1="50" x2="720" y2="80" class="arrow"/>
  <line x1="660" y1="130" x2="720" y2="100" class="arrow"/>
  
  <!-- 向下流程 -->
  <line x1="590" y1="160" x2="280" y2="200" class="arrow"/>
  <line x1="340" y1="230" x2="380" y2="230" class="arrow"/>
  <line x1="500" y1="230" x2="540" y2="230" class="arrow"/>
  <line x1="660" y1="230" x2="700" y2="230" class="arrow"/>
  <line x1="820" y1="230" x2="880" y2="230" class="arrow"/>
  
  <!-- UI组件连接 -->
  <line x1="940" y1="260" x2="940" y2="300" class="arrow"/>
  <line x1="940" y1="360" x2="940" y2="400" class="arrow"/>
  
  <!-- 进度反馈箭头 -->
  <line x1="780" y1="120" x2="780" y2="200" class="dashed-arrow"/>
  <line x1="840" y1="230" x2="880" y2="330" class="dashed-arrow"/>
  
  <!-- 错误处理流程 -->
  <line x1="280" y1="260" x2="110" y2="350" class="dashed-arrow"/>
  <line x1="170" y1="380" x2="220" y2="380" class="arrow"/>
  <line x1="340" y1="380" x2="380" y2="380" class="arrow"/>
  <line x1="500" y1="380" x2="540" y2="380" class="arrow"/>
  <line x1="660" y1="380" x2="700" y2="380" class="arrow"/>
  
  <!-- 重试回路 -->
  <path d="M 280 350 Q 150 320 150 280 Q 150 240 280 200" class="dashed-arrow"/>
  
  <!-- 流程说明文字 -->
  <text x="190" y="50" class="flow-text">页面加载检测</text>
  <text x="430" y="50" class="flow-text">sk=consign?</text>
  <text x="480" y="35" class="flow-text">否(批发)</text>
  <text x="480" y="175" class="flow-text">是(代发)</text>
  <text x="350" y="180" class="flow-text">DOM操作</text>
  <text x="950" y="280" class="flow-text">实时更新</text>
  <text x="200" y="320" class="flow-text">异常处理</text>
  <text x="100" y="320" class="flow-text">失败重试</text>
  
  <!-- 图例 -->
  <rect x="50" y="500" width="1100" height="250" fill="none" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="70" y="520" class="module" font-weight="bold">图例说明:</text>
  
  <rect x="70" y="540" width="80" height="30" class="box"/>
  <text x="110" y="560" text-anchor="middle" class="flow-text">起始/结束</text>
  
  <rect x="170" y="540" width="80" height="30" class="process-box"/>
  <text x="210" y="560" text-anchor="middle" class="flow-text" fill="white">处理过程</text>
  
  <polygon points="270,540 310,555 270,570 230,555" class="decision-box"/>
  <text x="270" y="560" text-anchor="middle" class="flow-text" fill="white">判断决策</text>
  
  <rect x="330" y="540" width="80" height="30" class="data-box"/>
  <text x="370" y="560" text-anchor="middle" class="flow-text" fill="white">数据处理</text>
  
  <rect x="430" y="540" width="80" height="30" class="ui-box"/>
  <text x="470" y="560" text-anchor="middle" class="flow-text" fill="white">用户界面</text>
  
  <line x1="530" y1="555" x2="580" y2="555" class="arrow"/>
  <text x="590" y="560" class="flow-text">主要流程</text>
  
  <line x1="650" y1="555" x2="700" y2="555" class="dashed-arrow"/>
  <text x="710" y="560" class="flow-text">反馈/异常</text>
  
  <!-- 模块详细说明 -->
  <text x="70" y="600" class="module" font-weight="bold">核心模块说明:</text>
  
  <text x="70" y="620" class="flow-text">• URL检测器: 通过URL参数识别页面类型(sk=consign判断代发模式)</text>
  <text x="70" y="635" class="flow-text">• 批发抓取器: 处理批发页面的&lt;h1&gt;标签商家信息提取</text>
  <text x="70" y="650" class="flow-text">• 代发抓取器: 处理代发页面的&lt;span&gt;标签商家信息提取</text>
  <text x="70" y="665" class="flow-text">• 进度管理器: 实时跟踪各模块执行状态和进度</text>
  <text x="70" y="680" class="flow-text">• UI组件: 进度条和信息面板独立管理显示内容</text>
  
  <text x="70" y="710" class="module" font-weight="bold">数据流向:</text>
  <text x="70" y="730" class="flow-text">页面URL → 类型识别 → 模块选择 → DOM查询 → 数据提取 → 验证处理 → UI展示 → 用户反馈</text>
</svg>