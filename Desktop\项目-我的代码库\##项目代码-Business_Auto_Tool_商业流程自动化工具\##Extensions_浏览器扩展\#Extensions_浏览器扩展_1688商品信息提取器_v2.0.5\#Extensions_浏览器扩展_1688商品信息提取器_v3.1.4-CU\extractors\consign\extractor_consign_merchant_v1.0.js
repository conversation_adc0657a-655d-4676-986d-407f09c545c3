/**
 * 代发模式商家信息抽取器
 * 专门处理1688代发页面的商家信息提取
 * 目标元素: <span> 标签中的商家信息
 * <AUTHOR>
 * @version 1.0.0
 */

class ConsignMerchantExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_consign_merchant_001',
      '代发商家信息',
      '提取1688代发页面的商家名称和相关信息'
    );
    
    // 抽取器配置
    this.config = {
      timeout: 10000, // 10秒超时
      retryDelay: 2000, // 重试延迟2秒
      maxTextLength: 200, // 最大文本长度
      minTextLength: 2 // 最小文本长度
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 主要选择器 - 优先级从高到低（v3.1.2增强）
      primary: [
        // 1688新版页面结构（基于用户提供的HTML）
        'span[title*="公司"][style*="max-width"]', // 1688新版商家名称
        'span[title*="企业"][style*="max-width"]', // 1688新版企业名称
        'span[title*="厂"][style*="max-width"]', // 1688新版工厂名称
        'div[style*="font-size: 20px"][style*="font-weight: bold"] span[title]', // 1688新版标题区域
        
        // 传统选择器
        'span[title*="公司"]', // 包含"公司"的title属性
        'span[title*="企业"]', // 包含"企业"的title属性
        'span[title*="厂"]', // 包含"厂"的title属性
        'span[title*="商行"]', // 包含"商行"的title属性
        'span[title*="有限公司"]', // 包含"有限公司"的title属性
        'span.company-name', // 公司名称类
        'span.merchant-name', // 商家名称类
        'span.supplier-name' // 供应商名称类
      ],
      
      // 备用选择器（v3.1.2增强）
      fallback: [
        // 1688新版结构
        'div[style*="font-family: PingFang SC"] span[title]', // 1688新版字体区域
        'div[style*="font-size: 20px"] span', // 大字体标题区域
        'div[style*="font-weight: bold"] span', // 粗体文本区域
        
        // 传统选择器
        'span[title]', // 所有带title的span
        '.company-info span',
        '.merchant-info span',
        '.supplier-info span',
        '[data-company-name]',
        '[data-merchant-name]',
        '[data-supplier-name]'
      ],
      
      // 上下文选择器 - 用于缩小搜索范围（v3.1.2增强）
      context: [
        // 1688新版容器
        '#shop-container-header', // 1688商家容器头部
        'div[style*="margin: 30px 0px 0px 18px"]', // 1688商家信息区域
        'div[style*="font-family: PingFang SC"]', // 1688新版字体容器
        
        // 传统容器
        '.company-info',
        '.merchant-info', 
        '.supplier-info',
        '.shop-info',
        '.consign-info',
        '.dropship-info',
        'header',
        '.page-header',
        '.product-header'
      ],
      
      // 排除选择器 - 避免误选
      exclude: [
        '.advertisement',
        '.ad-content',
        '.popup',
        '.modal',
        '.tooltip',
        '.price',
        '.quantity',
        '.specification',
        'script',
        'style',
        'noscript'
      ]
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的商家信息
   */
  async extract() {
    const selectors = this.getSelectors();
    let merchantInfo = null;
    
    try {
      // 第一步：尝试主要选择器
      merchantInfo = await this.extractWithPrimarySelectors(selectors.primary);
      
      if (!merchantInfo) {
        // 第二步：尝试在上下文中查找
        merchantInfo = await this.extractWithContextSelectors(selectors.context, selectors.primary);
      }
      
      if (!merchantInfo) {
        // 第三步：尝试备用选择器
        merchantInfo = await this.extractWithFallbackSelectors(selectors.fallback);
      }
      
      if (!merchantInfo) {
        // 第四步：智能搜索所有span元素
        merchantInfo = await this.intelligentSpanSearch();
      }
      
      if (!merchantInfo) {
        throw new Error('未找到代发商家信息元素');
      }
      
      // 记录DOM数据到调试面板
      if (window.debugPanel && typeof window.debugPanel.addDOMData === 'function') {
        const merchantElements = document.querySelectorAll('.merchant, .company, .supplier, [class*="merchant"], [class*="company"], [class*="supplier"]');
        if (merchantElements.length > 0) {
          window.debugPanel.addDOMData('商家信息', {
            selector: '.merchant等商家相关元素',
            count: merchantElements.length,
            elements: Array.from(merchantElements).slice(0, 3).map(el => ({
              tagName: el.tagName,
              className: el.className,
              textContent: el.textContent ? el.textContent.substring(0, 50) + '...' : ''
            }))
          });
        }
      }
      
      // 数据清理和增强
      const enhancedInfo = this.enhanceMerchantInfo(merchantInfo);
      
      // 记录提取结果到调试面板
      if (window.debugPanel && typeof window.debugPanel.addResult === 'function') {
        window.debugPanel.addResult('商家信息', {
          success: true,
          data: enhancedInfo,
          extractorType: 'consign_merchant',
          timestamp: Date.now(),
          confidence: enhancedInfo.confidence || 0
        });
      }
      
      console.log('✅ [商家信息调试] 商家信息提取完成，最终数据:', JSON.stringify(enhancedInfo, null, 2));
      
      return enhancedInfo;
      
    } catch (error) {
      console.error('代发商家信息提取失败:', error);
      throw error;
    }
  }

  /**
   * 使用主要选择器提取
   * @param {Array} selectors - 选择器列表
   * @returns {Promise<Object|null>} 商家信息
   */
  async extractWithPrimarySelectors(selectors) {
    for (const selector of selectors) {
      try {
        const element = await this.waitForElement(selector, 3000);
        if (element && this.isValidMerchantElement(element)) {
          return this.extractMerchantData(element, selector, 'primary');
        }
      } catch (error) {
        console.debug(`主选择器 ${selector} 未找到元素:`, error.message);
        continue;
      }
    }
    return null;
  }

  /**
   * 在上下文中查找
   * @param {Array} contextSelectors - 上下文选择器
   * @param {Array} targetSelectors - 目标选择器
   * @returns {Promise<Object|null>} 商家信息
   */
  async extractWithContextSelectors(contextSelectors, targetSelectors) {
    for (const contextSelector of contextSelectors) {
      const contextElement = this.safeQuerySelector(contextSelector);
      if (!contextElement) continue;
      
      for (const targetSelector of targetSelectors) {
        const element = this.safeQuerySelector(targetSelector, contextElement);
        if (element && this.isValidMerchantElement(element)) {
          return this.extractMerchantData(element, `${contextSelector} ${targetSelector}`, 'context');
        }
      }
    }
    return null;
  }

  /**
   * 使用备用选择器提取
   * @param {Array} selectors - 备用选择器列表
   * @returns {Promise<Object|null>} 商家信息
   */
  async extractWithFallbackSelectors(selectors) {
    for (const selector of selectors) {
      const elements = this.safeQuerySelectorAll(selector);
      
      for (const element of elements) {
        if (this.isValidMerchantElement(element)) {
          return this.extractMerchantData(element, selector, 'fallback');
        }
      }
    }
    return null;
  }

  /**
   * 智能搜索所有span元素
   * @returns {Promise<Object|null>} 商家信息
   */
  async intelligentSpanSearch() {
    const allSpans = this.safeQuerySelectorAll('span');
    const candidates = [];
    
    // 收集候选元素
    for (const span of allSpans) {
      if (this.isValidMerchantElement(span)) {
        const score = this.calculateElementScore(span);
        candidates.push({ element: span, score });
      }
    }
    
    // 按分数排序
    candidates.sort((a, b) => b.score - a.score);
    
    // 返回最高分的元素
    if (candidates.length > 0) {
      const best = candidates[0];
      return this.extractMerchantData(best.element, 'span (intelligent)', 'intelligent');
    }
    
    return null;
  }

  /**
   * 计算元素分数
   * @param {Element} element - DOM元素
   * @returns {number} 分数
   */
  calculateElementScore(element) {
    let score = 0;
    const text = this.getElementText(element);
    
    // 基础分数
    score += 10;
    
    // title属性加分
    if (element.hasAttribute('title')) {
      score += 20;
      const title = element.getAttribute('title');
      if (title && title.includes('公司')) score += 15;
      if (title && title.includes('企业')) score += 10;
      if (title && title.includes('厂')) score += 8;
    }
    
    // 文本内容加分
    if (text.includes('有限公司')) score += 25;
    if (text.includes('股份有限公司')) score += 20;
    if (text.includes('公司')) score += 15;
    if (text.includes('企业')) score += 10;
    if (text.includes('厂')) score += 8;
    if (text.includes('商行')) score += 8;
    
    // 类名加分
    const className = element.className.toLowerCase();
    if (className.includes('company')) score += 15;
    if (className.includes('merchant')) score += 15;
    if (className.includes('supplier')) score += 10;
    if (className.includes('shop')) score += 8;
    
    // 位置加分（页面上方的元素更可能是商家信息）
    const rect = element.getBoundingClientRect();
    if (rect.top < window.innerHeight * 0.3) {
      score += 10;
    }
    
    // 可见性检查
    if (rect.width > 0 && rect.height > 0) {
      score += 5;
    }
    
    // 文本长度合理性
    if (text.length >= 4 && text.length <= 50) {
      score += 10;
    } else if (text.length > 50) {
      score -= 5;
    }
    
    return score;
  }

  /**
   * 验证是否为有效的商家元素
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否有效
   */
  isValidMerchantElement(element) {
    if (!element || element.tagName.toLowerCase() !== 'span') return false;
    
    // 检查是否在排除列表中
    const excludeSelectors = this.getSelectors().exclude;
    for (const excludeSelector of excludeSelectors) {
      if (element.matches && element.matches(excludeSelector)) {
        return false;
      }
      if (element.closest && element.closest(excludeSelector)) {
        return false;
      }
    }
    
    // 获取文本内容
    const text = this.getElementText(element);
    if (!text) return false;
    
    // 文本长度检查
    if (text.length < this.config.minTextLength || text.length > this.config.maxTextLength) {
      return false;
    }
    
    // 检查是否包含商家相关关键词
    const merchantKeywords = [
      '公司', '企业', '商行', '厂', '店', '铺',
      '有限公司', '股份', '集团', '实业',
      '贸易', '制造', '生产', '经营',
      '工厂', '制品', '科技'
    ];
    
    const hasKeyword = merchantKeywords.some(keyword => text.includes(keyword));
    
    // 检查是否为明显的非商家信息
    const nonMerchantKeywords = [
      '登录', '注册', '搜索', '购物车', '收藏',
      '价格', '数量', '规格', '型号', '品牌',
      '评价', '评论', '问答', '客服', '联系',
      '立即', '现在', '马上', '点击', '查看',
      '元', '¥', '$', '件', '个', '套'
    ];
    
    const hasNonKeyword = nonMerchantKeywords.some(keyword => text.includes(keyword));
    
    // 如果有商家关键词且没有非商家关键词，或者文本看起来像公司名
    return (hasKeyword && !hasNonKeyword) || (!hasNonKeyword && this.looksLikeCompanyName(text));
  }

  /**
   * 判断文本是否像公司名称
   * @param {string} text - 文本内容
   * @returns {boolean} 是否像公司名称
   */
  looksLikeCompanyName(text) {
    // 长度合理
    if (text.length < 4 || text.length > 50) return false;
    
    // 包含地名 + 行业词汇的模式
    const regions = ['北京', '上海', '广州', '深圳', '杭州', '义乌', '温州', '东莞', '佛山', '苏州', '无锡'];
    const industries = ['电子', '服装', '家具', '五金', '化工', '食品', '玩具', '箱包', '纺织', '机械'];
    
    const hasRegion = regions.some(region => text.includes(region));
    const hasIndustry = industries.some(industry => text.includes(industry));
    
    if (hasRegion && hasIndustry) return true;
    
    // 典型的公司名称模式
    const companyPatterns = [
      /^.+有限公司$/,
      /^.+股份有限公司$/,
      /^.+集团.*/,
      /^.+实业.*/,
      /^.+贸易.*/,
      /^.+制造.*/,
      /^.+科技.*/
    ];
    
    return companyPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 提取商家数据
   * @param {Element} element - DOM元素
   * @param {string} selector - 使用的选择器
   * @param {string} method - 提取方法
   * @returns {Object} 商家信息
   */
  extractMerchantData(element, selector, method) {
    const merchantName = this.getElementText(element);
    const title = element.getAttribute('title') || merchantName;
    
    return {
      name: merchantName,
      title: title,
      element: {
        tagName: element.tagName.toLowerCase(),
        className: element.className,
        id: element.id,
        selector: selector,
        attributes: this.getRelevantAttributes(element)
      },
      extraction: {
        method: method,
        selector: selector,
        timestamp: Date.now(),
        pageType: 'consign'
      },
      metadata: {
        textLength: merchantName.length,
        hasTitle: !!element.getAttribute('title'),
        position: this.getElementPosition(element),
        score: method === 'intelligent' ? this.calculateElementScore(element) : null
      }
    };
  }
  

  
  /**
   * 获取相关属性
   * @param {Element} element - DOM元素
   * @returns {Object} 属性对象
   */
  getRelevantAttributes(element) {
    const attributes = {};
    const relevantAttrs = ['title', 'data-company-name', 'data-merchant-name', 'data-supplier-name'];
    
    relevantAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        attributes[attr] = element.getAttribute(attr);
      }
    });
    
    return attributes;
  }

  /**
   * 获取元素文本内容
   * @param {Element} element - DOM元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    if (!element) return '';
    
    // 优先使用title属性
    const title = element.getAttribute('title');
    if (title && title.trim()) {
      return title.trim();
    }
    
    // 使用textContent
    const textContent = element.textContent || element.innerText || '';
    return textContent.trim().replace(/\s+/g, ' ');
  }

  /**
   * 获取元素位置信息
   * @param {Element} element - DOM元素
   * @returns {Object} 位置信息
   */
  getElementPosition(element) {
    try {
      const rect = element.getBoundingClientRect();
      return {
        top: Math.round(rect.top),
        left: Math.round(rect.left),
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        visible: rect.width > 0 && rect.height > 0,
        inViewport: rect.top >= 0 && rect.top <= window.innerHeight
      };
    } catch (error) {
      return { visible: false, inViewport: false };
    }
  }

  /**
   * 增强商家信息
   * @param {Object} merchantInfo - 原始商家信息
   * @returns {Object} 增强后的商家信息
   */
  enhanceMerchantInfo(merchantInfo) {
    const enhanced = { ...merchantInfo };
    
    // 分析商家类型
    enhanced.analysis = this.analyzeMerchantType(merchantInfo.name);
    
    // 提取关键信息
    enhanced.keywords = this.extractKeywords(merchantInfo.name);
    
    // 计算可信度
    enhanced.confidence = this.calculateConfidence(merchantInfo);
    
    // 代发模式特有信息
    enhanced.consignInfo = this.analyzeConsignCapability(merchantInfo.name);
    
    return enhanced;
  }

  /**
   * 分析代发能力
   * @param {string} merchantName - 商家名称
   * @returns {Object} 代发能力分析
   */
  analyzeConsignCapability(merchantName) {
    const analysis = {
      likelyConsignSupplier: false,
      indicators: [],
      riskFactors: []
    };
    
    // 代发友好的指标
    const consignFriendlyKeywords = [
      '贸易', '商贸', '经贸', '批发', '供应',
      '电商', '网络', '在线', '跨境'
    ];
    
    consignFriendlyKeywords.forEach(keyword => {
      if (merchantName.includes(keyword)) {
        analysis.indicators.push(keyword);
        analysis.likelyConsignSupplier = true;
      }
    });
    
    // 风险因素
    const riskKeywords = [
      '个体', '小作坊', '家庭'
    ];
    
    riskKeywords.forEach(keyword => {
      if (merchantName.includes(keyword)) {
        analysis.riskFactors.push(keyword);
      }
    });
    
    return analysis;
  }

  /**
   * 分析商家类型
   * @param {string} merchantName - 商家名称
   * @returns {Object} 分析结果
   */
  analyzeMerchantType(merchantName) {
    const analysis = {
      type: 'unknown',
      subtype: null,
      indicators: []
    };
    
    // 公司类型判断
    if (merchantName.includes('有限公司') || merchantName.includes('有限责任公司')) {
      analysis.type = 'company';
      analysis.subtype = 'limited';
      analysis.indicators.push('有限公司');
    } else if (merchantName.includes('股份有限公司')) {
      analysis.type = 'company';
      analysis.subtype = 'corporation';
      analysis.indicators.push('股份公司');
    } else if (merchantName.includes('个体工商户') || merchantName.includes('个体户')) {
      analysis.type = 'individual';
      analysis.indicators.push('个体户');
    } else if (merchantName.includes('合作社')) {
      analysis.type = 'cooperative';
      analysis.indicators.push('合作社');
    }
    
    return analysis;
  }

  /**
   * 提取关键词
   * @param {string} merchantName - 商家名称
   * @returns {Array} 关键词列表
   */
  extractKeywords(merchantName) {
    const keywords = [];
    
    // 地区关键词
    const regions = ['北京', '上海', '广州', '深圳', '杭州', '义乌', '温州', '东莞', '佛山'];
    regions.forEach(region => {
      if (merchantName.includes(region)) {
        keywords.push({ type: 'region', value: region });
      }
    });
    
    // 行业关键词
    const industries = ['电子', '服装', '家具', '五金', '化工', '食品', '玩具', '箱包'];
    industries.forEach(industry => {
      if (merchantName.includes(industry)) {
        keywords.push({ type: 'industry', value: industry });
      }
    });
    
    return keywords;
  }

  /**
   * 计算提取可信度
   * @param {Object} merchantInfo - 商家信息
   * @returns {number} 可信度 (0-100)
   */
  calculateConfidence(merchantInfo) {
    let confidence = 0;
    
    // 基础分数：有商家名称 (35分)
    if (merchantInfo.name && merchantInfo.name.trim().length > 0) {
      confidence += 35;
      
      // 商家名称质量评估 (额外15分)
      if (this.looksLikeCompanyName(merchantInfo.name)) {
        confidence += 15;
      }
    }
    
    // 评价标签评估 (20分) - 新增
    if (merchantInfo.reviewTags && merchantInfo.reviewTags.length > 0) {
      confidence += 10; // 基础分
      
      // 根据评价标签数量和质量加分
      const tagCount = merchantInfo.reviewTags.length;
      if (tagCount >= 3) {
        confidence += 10; // 有3个或更多评价标签
      } else if (tagCount >= 1) {
        confidence += 5; // 有1-2个评价标签
      }
    }
    
    // 提取方法加分 (15分)
    if (merchantInfo.extraction.method === 'primary') {
      confidence += 15;
    } else if (merchantInfo.extraction.method === 'context') {
      confidence += 12;
    } else if (merchantInfo.extraction.method === 'intelligent') {
      confidence += 10;
      // 智能搜索的分数加成
      if (merchantInfo.metadata.score) {
        confidence += Math.min(merchantInfo.metadata.score / 10, 5);
      }
    } else {
      confidence += 8;
    }
    
    // 元素属性加分 (10分)
    if (merchantInfo.metadata.hasTitle) {
      confidence += 10;
    }
    
    // 文本长度合理性 (5分)
    const textLength = merchantInfo.metadata.textLength;
    if (textLength >= 4 && textLength <= 50) {
      confidence += 5;
    } else if (textLength > 50) {
      confidence -= 3;
    }
    
    // 可见性检查 (3分)
    if (merchantInfo.metadata.position && merchantInfo.metadata.position.visible) {
      confidence += 3;
    }
    
    // 在视口内加分 (2分)
    if (merchantInfo.metadata.position && merchantInfo.metadata.position.inViewport) {
      confidence += 2;
    }
    
    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 检查必要字段
    if (!data.name || typeof data.name !== 'string') {
      return false;
    }
    
    // 检查文本长度
    if (data.name.length < this.config.minTextLength || 
        data.name.length > this.config.maxTextLength) {
      return false;
    }
    
    // 检查可信度
    if (data.confidence && data.confidence < 25) {
      return false;
    }
    
    return true;
  }

  /**
   * 数据格式化
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    if (!data) return null;
    
    return {
      ...data,
      name: data.name.trim().replace(/\s+/g, ' '),
      title: data.title ? data.title.trim().replace(/\s+/g, ' ') : data.name,
      formatted: true,
      formatTime: Date.now()
    };
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.ConsignMerchantExtractor = ConsignMerchantExtractor;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ConsignMerchantExtractor;
}