/**
 * Popup样式文件 - 现代化UI设计
 * 提供美观、响应式的用户界面
 * <AUTHOR>
 * @version 1.0.0
 */

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 容器 */
.container {
  width: 380px;
  min-height: 500px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 16px 20px;
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.version {
  font-size: 12px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
}

/* 页面状态 */
.page-status {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-weight: 500;
  color: #6c757d;
}

.status-value {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
}

.page-type-icon {
  font-size: 16px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.idle {
  background: #6c757d;
}

.status-indicator.ready {
  background: #28a745;
  animation: pulse 2s infinite;
}

.status-indicator.extracting {
  background: #ffc107;
  animation: blink 1s infinite;
}

.status-indicator.completed {
  background: #28a745;
}

.status-indicator.failed {
  background: #dc3545;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 章节样式 */
section {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  padding: 16px 20px 12px;
  margin: 0;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.section-icon {
  font-size: 16px;
}

/* 进度区域 */
.progress-section {
  background: #ffffff;
}

.progress-container {
  padding: 0 20px 16px;
  max-height: 200px;
  overflow-y: auto;
}

.progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.progress-item:last-child {
  border-bottom: none;
}

.progress-info {
  flex: 1;
  margin-right: 12px;
}

.progress-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.progress-status {
  font-size: 12px;
  color: #6c757d;
}

.progress-bar {
  width: 80px;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-fill.completed {
  background: linear-gradient(90deg, #56ab2f 0%, #a8e6cf 100%);
}

.progress-fill.failed {
  background: linear-gradient(90deg, #ff416c 0%, #ff4b2b 100%);
}

.progress-percent {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  min-width: 35px;
  text-align: right;
}

/* 全局进度条 */
.global-progress {
  padding: 12px 20px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.global-progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.global-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.global-progress-text {
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

/* 结果区域 */
.results-section {
  background: #ffffff;
  border-top: 1px solid #e9ecef;
}

.results-container {
  padding: 0 20px 16px;
  max-height: 250px;
  overflow-y: auto;
}

.no-results {
  text-align: center;
  padding: 32px 16px;
  color: #6c757d;
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.no-results-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.no-results-hint {
  font-size: 12px;
  opacity: 0.8;
}

.result-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.result-item:hover {
  border-color: #4facfe;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.1);
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-name {
  font-weight: 600;
  color: #333;
}

.result-confidence {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background: #e9ecef;
  color: #495057;
}

.result-confidence.high {
  background: #d4edda;
  color: #155724;
}

.result-confidence.medium {
  background: #fff3cd;
  color: #856404;
}

.result-confidence.low {
  background: #f8d7da;
  color: #721c24;
}

.result-content {
  font-size: 13px;
  color: #495057;
  line-height: 1.4;
}

.result-value {
  font-weight: 500;
  color: #333;
  word-break: break-all;
}

/* 操作按钮区域 */
.actions-section {
  padding: 16px 20px;
  background: #ffffff;
  border-top: 1px solid #e9ecef;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.secondary-actions {
  display: flex;
  gap: 8px;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-height: 36px;
  flex: 1;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(79, 172, 254, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 172, 254, 0.4);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.btn-outline:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.btn-icon {
  font-size: 14px;
}

.btn-text {
  font-size: 13px;
}

/* 设置面板 */
.settings-panel {
  background: #ffffff;
  border-top: 1px solid #e9ecef;
}

.settings-content {
  padding: 0 20px 16px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 4px;
}

.setting-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #4facfe;
}

.setting-text {
  font-weight: 500;
  color: #333;
}

.setting-description {
  font-size: 12px;
  color: #6c757d;
  margin-left: 24px;
  line-height: 1.4;
}

.settings-actions {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 8px;
}

/* 消息提示 */
.error-message,
.success-message {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  padding: 12px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

.error-message {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.success-message {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.error-icon,
.success-icon {
  font-size: 16px;
}

.error-text,
.success-text {
  flex: 1;
  font-size: 13px;
}

.error-close,
.success-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.error-close:hover,
.success-close:hover {
  opacity: 1;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #4facfe;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  font-size: 14px;
  color: #6c757d;
}

/* 滚动条样式 */
.progress-container::-webkit-scrollbar,
.results-container::-webkit-scrollbar {
  width: 6px;
}

.progress-container::-webkit-scrollbar-track,
.results-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.progress-container::-webkit-scrollbar-thumb,
.results-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.progress-container::-webkit-scrollbar-thumb:hover,
.results-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .container {
    width: 100vw;
    min-height: 100vh;
    border-radius: 0;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .secondary-actions {
    flex-direction: column;
  }
  
  .btn {
    flex: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .container {
    background: #2c3e50;
    color: #ecf0f1;
  }
  
  .page-status {
    background: #34495e;
    border-color: #4a5f7a;
  }
  
  .section-title {
    background: #2c3e50;
    color: #ecf0f1;
    border-color: #4a5f7a;
  }
  
  .progress-section,
  .results-section,
  .actions-section,
  .settings-panel {
    background: #2c3e50;
    border-color: #4a5f7a;
  }
  
  .global-progress {
    background: #34495e;
    border-color: #4a5f7a;
  }
  
  .result-item {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .result-name,
  .result-value {
    color: #ecf0f1;
  }
  
  .btn-outline {
    color: #bdc3c7;
    border-color: #4a5f7a;
  }
  
  .btn-outline:hover:not(:disabled) {
    background: #34495e;
    border-color: #5a6f8a;
  }
}