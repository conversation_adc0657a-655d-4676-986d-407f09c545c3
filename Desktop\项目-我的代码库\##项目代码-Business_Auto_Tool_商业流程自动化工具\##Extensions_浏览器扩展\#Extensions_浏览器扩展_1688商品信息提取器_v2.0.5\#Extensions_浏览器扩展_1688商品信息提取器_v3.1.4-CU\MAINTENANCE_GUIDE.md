# 📋 项目维护指南 - 1688商品信息提取器

## 🗂️ 项目组织结构

### 📁 目录结构说明

```
📦 1688商品信息提取器_v3.0.0
├── 📁 core/                    # 核心功能模块
├── 📁 extractors/              # 提取器模块
│   ├── 📁 wholesale/           # 批发模式提取器
│   └── 📁 consign/            # 代发模式提取器
├── 📁 content/                 # 内容脚本
├── 📁 background/              # 后台脚本
├── 📁 popup/                   # 弹窗界面
├── 📁 styles/                  # 样式文件
├── 📁 icons/                   # 图标资源
├── 📁 assets/                  # 静态资源 (新增)
│   └── 系统流程图.svg
├── 📁 docs/                    # 文档中心 (新增)
│   ├── 📁 development/         # 开发文档
│   ├── 📁 architecture/        # 架构文档
│   ├── 📁 fixes/              # 修复报告
│   ├── 📁 guides/             # 使用指南
│   └── README.md              # 文档索引
├── 📁 temp/                    # 临时文件 (新增)
│   └── test_consign_fix_validation.js
├── 📁 .claude/                 # Claude配置
├── .gitignore                  # Git忽略规则 (新增)
├── package.json                # 项目配置 (新增)
├── manifest.json               # 扩展清单
├── README.md                   # 项目说明
└── CHANGELOG.md                # 更新日志
```

## 🧹 维护任务清单

### 📅 日常维护

#### 🔄 代码提交前检查
- [ ] 运行 `npm run lint` 检查代码质量
- [ ] 运行 `npm test` 执行测试
- [ ] 确保版本号一致性 (manifest.json, package.json, README.md)
- [ ] 更新 CHANGELOG.md

#### 📂 文件组织规则
- **临时文件**: 所有 `test_*.js` 文件放入 `temp/` 目录
- **文档文件**: 按类型分类到 `docs/` 对应子目录
- **资源文件**: 图片、SVG等放入 `assets/` 目录
- **源代码**: 保持模块化结构，新功能按模块分类

### 🔧 版本更新流程

#### 1. 版本号管理
```bash
# 更新版本号的文件(保持一致)：
- manifest.json (line 4: "version")
- package.json (line 3: "version") 
- README.md (line 1: 标题)
```

#### 2. 更新清单
- [ ] 更新 `CHANGELOG.md` 记录变更
- [ ] 更新 `README.md` 功能描述
- [ ] 检查 `manifest.json` 权限和文件引用
- [ ] 更新相关文档

#### 3. 测试验证
```bash
npm test          # 运行测试
npm run build     # 构建检查
npm run package   # 打包验证
```

### 📁 目录维护规范

#### 🚫 避免的行为
- ❌ 在根目录放置临时文件
- ❌ 混合不同类型的文档文件
- ❌ 直接修改已打包的扩展文件
- ❌ 忽略版本号同步

#### ✅ 推荐的行为
- ✅ 使用 `temp/` 目录存放测试文件
- ✅ 按功能分类组织文档到 `docs/` 子目录
- ✅ 定期清理无用的临时文件
- ✅ 保持版本号一致性

### 🔍 故障排查

#### 📊 日志文件位置
```bash
temp/           # 测试日志
docs/fixes/     # 修复报告
```

#### 🛠️ 常用维护命令
```bash
npm run clean   # 清理临时文件
npm run dev     # 开发模式提示
npm run package # 打包扩展
```

#### 🚨 紧急修复流程
1. 创建修复分支
2. 在 `docs/fixes/` 创建修复报告
3. 测试文件放入 `temp/`
4. 更新版本号和 CHANGELOG
5. 验证修复效果

### 📈 性能监控

#### 🎯 关键指标
- 扩展加载时间 < 500ms
- 内存使用 < 50MB
- DOM提取准确率 > 90%

#### 📊 监控文件
- `temp/test_*.js` - 性能测试脚本
- `docs/fixes/` - 性能问题修复记录

### 🔒 安全维护

#### 🛡️ 权限检查
定期检查 `manifest.json` 权限设置：
```json
"permissions": [
  "activeTab",      # 仅当前标签页
  "storage",        # 本地存储
  "scripting",      # 脚本注入
  "clipboardWrite"  # 剪贴板写入
]
```

#### 🔐 安全最佳实践
- 最小权限原则
- 定期更新依赖
- 避免执行不可信代码
- 验证输入数据

### 📋 检查清单模板

#### 🚀 发版前检查
- [ ] 版本号已同步 (manifest.json, package.json, README.md)
- [ ] CHANGELOG.md 已更新
- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 临时文件已清理
- [ ] 权限设置合理
- [ ] 功能验证完成

#### 🧹 月度维护
- [ ] 清理 `temp/` 目录旧文件
- [ ] 检查依赖更新
- [ ] 审查文档完整性
- [ ] 性能测试和优化
- [ ] 安全漏洞扫描

### 📞 支持联系

#### 📚 文档资源
- 项目文档: `docs/README.md`
- 架构说明: `docs/architecture/`
- 开发指南: `docs/development/`
- 修复记录: `docs/fixes/`

#### 🔧 开发工具
- Chrome开发者工具
- Extension Reloader (开发扩展)
- Git版本控制

---

**维护指南版本**: v1.0.0  
**创建日期**: 2025-09-10  
**适用版本**: 1688商品信息提取器 v3.0.7+  

**更新说明**: 本维护指南将随项目发展持续更新，建议定期查看最新版本。