/**
 * 进度管理器 - 统一管理所有模块的执行进度
 * 提供实时进度跟踪、状态管理和事件通知功能
 * <AUTHOR>
 * @version 1.0.0
 */

class ProgressManager {
  /**
   * 构造函数
   */
  constructor() {
    this.tasks = new Map(); // 任务列表
    this.listeners = new Set(); // 事件监听器
    this.globalStatus = 'idle'; // idle, running, completed, failed
    this.startTime = null;
    this.endTime = null;
    this.totalTasks = 0;
    this.completedTasks = 0;
    this.failedTasks = 0;
    
    // 状态常量
    this.STATUS = {
      IDLE: 'idle',
      PENDING: 'pending',
      PROCESSING: 'processing',
      COMPLETED: 'completed',
      FAILED: 'failed',
      CANCELLED: 'cancelled'
    };
    
    // 全局状态
    this.GLOBAL_STATUS = {
      IDLE: 'idle',
      RUNNING: 'running',
      COMPLETED: 'completed',
      FAILED: 'failed',
      CANCELLED: 'cancelled'
    };
  }

  /**
   * 添加任务
   * @param {string} taskId - 任务ID
   * @param {string} taskName - 任务名称
   * @param {Object} options - 任务选项
   */
  addTask(taskId, taskName, options = {}) {
    const task = {
      id: taskId,
      name: taskName,
      status: this.STATUS.PENDING,
      progress: 0,
      startTime: null,
      endTime: null,
      duration: null,
      result: null,
      error: null,
      retryCount: 0,
      maxRetries: options.maxRetries || 3,
      priority: options.priority || 'normal', // low, normal, high
      dependencies: options.dependencies || [], // 依赖的任务ID
      metadata: options.metadata || {},
      createdAt: Date.now()
    };
    
    this.tasks.set(taskId, task);
    this.totalTasks++;
    
    this.notifyListeners('taskAdded', { task });
    this.updateGlobalProgress();
    
    console.log(`Task added: ${taskId} - ${taskName}`);
  }

  /**
   * 移除任务
   * @param {string} taskId - 任务ID
   */
  removeTask(taskId) {
    const task = this.tasks.get(taskId);
    if (task) {
      this.tasks.delete(taskId);
      this.totalTasks--;
      
      if (task.status === this.STATUS.COMPLETED) {
        this.completedTasks--;
      } else if (task.status === this.STATUS.FAILED) {
        this.failedTasks--;
      }
      
      this.notifyListeners('taskRemoved', { taskId, task });
      this.updateGlobalProgress();
      
      console.log(`Task removed: ${taskId}`);
    }
  }

  /**
   * 更新任务进度
   * @param {string} taskId - 任务ID
   * @param {number} progress - 进度值 (0-100)
   * @param {string} status - 任务状态
   * @param {Object} data - 附加数据
   */
  updateProgress(taskId, progress, status, data = {}) {
    const task = this.tasks.get(taskId);
    if (!task) {
      console.warn(`Task not found: ${taskId}`);
      return;
    }

    const oldStatus = task.status;
    const oldProgress = task.progress;
    
    // 更新任务信息
    task.progress = Math.max(0, Math.min(100, progress));
    task.status = status;
    
    // 处理状态变化
    if (status === this.STATUS.PROCESSING && !task.startTime) {
      task.startTime = Date.now();
    }
    
    if ([this.STATUS.COMPLETED, this.STATUS.FAILED, this.STATUS.CANCELLED].includes(status)) {
      if (!task.endTime) {
        task.endTime = Date.now();
        task.duration = task.startTime ? task.endTime - task.startTime : null;
      }
      
      // 更新完成/失败计数
      if (oldStatus !== this.STATUS.COMPLETED && status === this.STATUS.COMPLETED) {
        this.completedTasks++;
      } else if (oldStatus !== this.STATUS.FAILED && status === this.STATUS.FAILED) {
        this.failedTasks++;
      }
    }
    
    // 更新附加数据
    if (data.result !== undefined) {
      task.result = data.result;
    }
    if (data.error !== undefined) {
      task.error = data.error;
    }
    if (data.retryCount !== undefined) {
      task.retryCount = data.retryCount;
    }
    
    // 通知监听器
    this.notifyListeners('progressUpdate', {
      taskId,
      task,
      oldStatus,
      oldProgress,
      changes: {
        progress: progress !== oldProgress,
        status: status !== oldStatus
      }
    });
    
    // 更新全局进度
    this.updateGlobalProgress();
    
    console.log(`Progress updated: ${taskId} - ${progress}% (${status})`);
  }

  /**
   * 开始执行所有任务
   */
  startAll() {
    if (this.globalStatus === this.GLOBAL_STATUS.RUNNING) {
      console.warn('Tasks are already running');
      return;
    }
    
    this.globalStatus = this.GLOBAL_STATUS.RUNNING;
    this.startTime = Date.now();
    this.endTime = null;
    this.completedTasks = 0;
    this.failedTasks = 0;
    
    // 重置所有任务状态
    for (const task of this.tasks.values()) {
      if (task.status !== this.STATUS.PROCESSING) {
        task.status = this.STATUS.PENDING;
        task.progress = 0;
        task.startTime = null;
        task.endTime = null;
        task.duration = null;
        task.result = null;
        task.error = null;
        task.retryCount = 0;
      }
    }
    
    this.notifyListeners('globalStart', {
      totalTasks: this.totalTasks,
      startTime: this.startTime
    });
    
    console.log(`Started ${this.totalTasks} tasks`);
  }

  /**
   * 停止所有任务
   */
  stopAll() {
    this.globalStatus = this.GLOBAL_STATUS.CANCELLED;
    this.endTime = Date.now();
    
    // 取消所有进行中的任务
    for (const task of this.tasks.values()) {
      if ([this.STATUS.PENDING, this.STATUS.PROCESSING].includes(task.status)) {
        task.status = this.STATUS.CANCELLED;
        if (!task.endTime) {
          task.endTime = Date.now();
          task.duration = task.startTime ? task.endTime - task.startTime : null;
        }
      }
    }
    
    this.notifyListeners('globalStop', {
      endTime: this.endTime,
      duration: this.startTime ? this.endTime - this.startTime : null
    });
    
    console.log('All tasks stopped');
  }

  /**
   * 重置所有任务
   */
  reset() {
    this.tasks.clear();
    this.globalStatus = this.GLOBAL_STATUS.IDLE;
    this.startTime = null;
    this.endTime = null;
    this.totalTasks = 0;
    this.completedTasks = 0;
    this.failedTasks = 0;
    
    this.notifyListeners('globalReset', {});
    
    console.log('Progress manager reset');
  }

  /**
   * 更新全局进度
   */
  updateGlobalProgress() {
    if (this.totalTasks === 0) {
      return;
    }
    
    // 计算全局进度
    let totalProgress = 0;
    let runningTasks = 0;
    
    for (const task of this.tasks.values()) {
      totalProgress += task.progress;
      if (task.status === this.STATUS.PROCESSING) {
        runningTasks++;
      }
    }
    
    const globalProgress = Math.round(totalProgress / this.totalTasks);
    
    // 更新全局状态
    let newGlobalStatus = this.globalStatus;
    
    if (this.completedTasks === this.totalTasks) {
      newGlobalStatus = this.GLOBAL_STATUS.COMPLETED;
      if (!this.endTime) {
        this.endTime = Date.now();
      }
    } else if (this.failedTasks > 0 && (this.completedTasks + this.failedTasks) === this.totalTasks) {
      newGlobalStatus = this.GLOBAL_STATUS.FAILED;
      if (!this.endTime) {
        this.endTime = Date.now();
      }
    } else if (runningTasks > 0) {
      newGlobalStatus = this.GLOBAL_STATUS.RUNNING;
    }
    
    const statusChanged = newGlobalStatus !== this.globalStatus;
    this.globalStatus = newGlobalStatus;
    
    // 通知全局进度更新
    this.notifyListeners('globalProgress', {
      progress: globalProgress,
      status: this.globalStatus,
      totalTasks: this.totalTasks,
      completedTasks: this.completedTasks,
      failedTasks: this.failedTasks,
      runningTasks: runningTasks,
      statusChanged
    });
  }

  /**
   * 获取任务信息
   * @param {string} taskId - 任务ID
   * @returns {Object|null} 任务信息
   */
  getTask(taskId) {
    return this.tasks.get(taskId) || null;
  }

  /**
   * 获取所有任务
   * @returns {Array} 任务列表
   */
  getAllTasks() {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取指定状态的任务
   * @param {string} status - 任务状态
   * @returns {Array} 任务列表
   */
  getTasksByStatus(status) {
    return Array.from(this.tasks.values()).filter(task => task.status === status);
  }

  /**
   * 获取全局统计信息
   * @returns {Object} 统计信息
   */
  getGlobalStats() {
    const duration = this.endTime && this.startTime ? this.endTime - this.startTime : null;
    const currentDuration = this.startTime ? Date.now() - this.startTime : null;
    
    return {
      status: this.globalStatus,
      totalTasks: this.totalTasks,
      completedTasks: this.completedTasks,
      failedTasks: this.failedTasks,
      pendingTasks: this.totalTasks - this.completedTasks - this.failedTasks,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: duration,
      currentDuration: currentDuration,
      successRate: this.totalTasks > 0 ? Math.round((this.completedTasks / this.totalTasks) * 100) : 0
    };
  }

  /**
   * 添加事件监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    if (typeof listener === 'function') {
      this.listeners.add(listener);
    }
  }

  /**
   * 移除事件监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器
   * @param {string} eventType - 事件类型
   * @param {Object} data - 事件数据
   */
  notifyListeners(eventType, data) {
    const event = {
      type: eventType,
      timestamp: Date.now(),
      data: data
    };
    
    for (const listener of this.listeners) {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in progress listener:', error);
      }
    }
  }

  /**
   * 生成进度报告
   * @returns {string} 格式化的进度报告
   */
  generateReport() {
    const stats = this.getGlobalStats();
    const lines = [
      `=== 进度报告 ===`,
      `全局状态: ${stats.status}`,
      `总任务数: ${stats.totalTasks}`,
      `已完成: ${stats.completedTasks}`,
      `失败: ${stats.failedTasks}`,
      `待处理: ${stats.pendingTasks}`,
      `成功率: ${stats.successRate}%`
    ];
    
    if (stats.duration) {
      lines.push(`总耗时: ${Math.round(stats.duration / 1000)}秒`);
    } else if (stats.currentDuration) {
      lines.push(`当前耗时: ${Math.round(stats.currentDuration / 1000)}秒`);
    }
    
    lines.push('', '=== 任务详情 ===');
    
    for (const task of this.tasks.values()) {
      const duration = task.duration ? `${Math.round(task.duration / 1000)}s` : '-';
      lines.push(`${task.name}: ${task.progress}% (${task.status}) [${duration}]`);
      
      if (task.error) {
        lines.push(`  错误: ${task.error}`);
      }
    }
    
    return lines.join('\n');
  }
}

// 创建全局实例
if (typeof window !== 'undefined') {
  window.ProgressManager = ProgressManager;
  window.progressManager = new ProgressManager();
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ProgressManager;
}