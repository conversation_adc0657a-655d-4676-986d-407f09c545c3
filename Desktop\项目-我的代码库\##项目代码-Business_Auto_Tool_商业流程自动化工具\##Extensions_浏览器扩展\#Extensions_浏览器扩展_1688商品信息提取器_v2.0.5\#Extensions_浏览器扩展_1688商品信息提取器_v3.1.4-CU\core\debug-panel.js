/**
 * 调试面板 - 显示提取器调试信息和原始数据
 * <AUTHOR>
 * @version 1.0.0
 */

class DebugPanel {
  constructor(loggerManager) {
    this.logger = loggerManager;
    this.isVisible = false;
    this.debugData = new Map();
    this.panel = null;
    
    this.init();
  }
  
  /**
   * 初始化调试面板
   */
  init() {
    this.createPanel();
    this.setupEventListeners();
    this.logger.info('DebugPanel', 'Debug panel initialized');
  }
  
  /**
   * 创建调试面板UI
   */
  createPanel() {
    // 创建调试按钮
    const debugButton = document.createElement('div');
    debugButton.id = 'extractor-debug-button';
    debugButton.innerHTML = '🔍 调试';
    debugButton.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 20px;
      width: 60px;
      height: 30px;
      background: #ff6b35;
      color: white;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 12px;
      font-weight: bold;
      z-index: 10001;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
    `;
    
    // 创建调试面板
    const panel = document.createElement('div');
    panel.id = 'extractor-debug-panel';
    panel.style.cssText = `
      position: fixed;
      bottom: 60px;
      left: 20px;
      width: 800px;
      height: 600px;
      background: #1e1e1e;
      color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.5);
      z-index: 10002;
      display: none;
      flex-direction: column;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 12px;
    `;
    
    // 面板头部
    const header = document.createElement('div');
    header.style.cssText = `
      padding: 10px 15px;
      background: #2d2d2d;
      border-radius: 8px 8px 0 0;
      border-bottom: 1px solid #404040;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    header.innerHTML = `
      <span>🔍 提取器调试面板</span>
      <div>
        <button id="debug-clear-btn" style="margin-right: 10px; padding: 4px 8px; background: #404040; color: white; border: none; border-radius: 4px; cursor: pointer;">清除</button>
        <button id="debug-close-btn" style="padding: 4px 8px; background: #ff4444; color: white; border: none; border-radius: 4px; cursor: pointer;">×</button>
      </div>
    `;
    
    // 标签页
    const tabs = document.createElement('div');
    tabs.style.cssText = `
      display: flex;
      background: #2d2d2d;
      border-bottom: 1px solid #404040;
      overflow-x: auto;
    `;
    tabs.innerHTML = `
      <div class="debug-tab active" data-tab="console" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid #ff6b35; white-space: nowrap;">总控制台</div>
      <div class="debug-tab" data-tab="system" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">🔧 系统状态</div>
      <div class="debug-tab" data-tab="logistics" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">🚚 物流信息</div>
      <div class="debug-tab" data-tab="specs" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">商品属性</div>
      <div class="debug-tab" data-tab="price" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">价格信息</div>
      <div class="debug-tab" data-tab="title" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">商品标题</div>
      <div class="debug-tab" data-tab="rating" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">商品评价</div>
      <div class="debug-tab" data-tab="images" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">商品图片</div>
      <div class="debug-tab" data-tab="merchant" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">商家信息</div>
      <div class="debug-tab" data-tab="dom" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">DOM数据</div>
      <div class="debug-tab" data-tab="results" style="padding: 8px 12px; cursor: pointer; border-bottom: 2px solid transparent; white-space: nowrap;">提取结果</div>
    `;
    
    // 内容区域
    const content = document.createElement('div');
    content.style.cssText = `
      flex: 1;
      overflow-y: auto;
      padding: 10px;
    `;
    
    // 创建所有内容区域
    const contentAreas = [
      { id: 'debug-console', display: 'block' },
      { id: 'debug-system', display: 'none' },
      { id: 'debug-logistics', display: 'none' },
      { id: 'debug-specs', display: 'none' },
      { id: 'debug-price', display: 'none' },
      { id: 'debug-title', display: 'none' },
      { id: 'debug-rating', display: 'none' },
      { id: 'debug-images', display: 'none' },
      { id: 'debug-merchant', display: 'none' },
      { id: 'debug-dom', display: 'none' },
      { id: 'debug-results', display: 'none' }
    ];
    
    contentAreas.forEach(area => {
      const div = document.createElement('div');
      div.id = area.id;
      div.style.cssText = `
        display: ${area.display};
        white-space: pre-wrap;
        word-break: break-all;
      `;
      content.appendChild(div);
    });
    
    panel.appendChild(header);
    panel.appendChild(tabs);
    panel.appendChild(content);
    
    document.body.appendChild(debugButton);
    document.body.appendChild(panel);
    
    this.panel = panel;
    this.debugButton = debugButton;
  }
  
  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 调试按钮点击
    this.debugButton.addEventListener('click', () => {
      this.toggle();
    });
    
    // 关闭按钮
    document.getElementById('debug-close-btn').addEventListener('click', () => {
      this.hide();
    });
    
    // 清除按钮
    document.getElementById('debug-clear-btn').addEventListener('click', () => {
      this.clear();
    });
    
    // 标签页切换
    document.querySelectorAll('.debug-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });
    
    // 拦截console.log
    this.interceptConsole();
  }
  
  /**
   * 拦截控制台输出
   */
  interceptConsole() {
    const originalLog = console.log;
    const self = this;
    
    console.log = function(...args) {
      // 调用原始console.log
      originalLog.apply(console, args);
      
      // 如果是调试信息，添加到面板
      const message = args.join(' ');
      if (message.includes('[商品属性调试]') || message.includes('[提取器调试]') || 
          message.includes('[价格调试]') || message.includes('[标题调试]') || 
          message.includes('[评价调试]') || message.includes('[图片调试]') || 
          message.includes('[商家调试]')) {
        
        // 确定消息类型和目标标签
        let targetTab = 'console';
        if (message.includes('[商品属性调试]')) targetTab = 'specs';
        else if (message.includes('[价格调试]')) targetTab = 'price';
        else if (message.includes('[标题调试]')) targetTab = 'title';
        else if (message.includes('[评价调试]')) targetTab = 'rating';
        else if (message.includes('[图片调试]')) targetTab = 'images';
        else if (message.includes('[商家调试]')) targetTab = 'merchant';
        
        // 如果是新的提取开始，清除对应模块的旧信息
        if (message.includes('========== 开始') && message.includes('提取 ==========')) {
          self.clearModuleConsole(targetTab);
        }
        
        // 添加到总控制台和对应模块
        self.addConsoleMessage(message, 'console');
        if (targetTab !== 'console') {
          self.addConsoleMessage(message, targetTab);
        }
      }
    };
  }
  
  /**
   * 添加控制台消息
   */
  addConsoleMessage(message, targetTab = 'console') {
    const consoleDiv = document.getElementById(`debug-${targetTab}`);
    if (consoleDiv) {
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = document.createElement('div');
      
      // 根据消息类型设置不同的颜色
      let borderColor = '#ff6b35';
      let bgColor = 'rgba(255, 107, 53, 0.1)';
      
      if (message.includes('✅')) {
        borderColor = '#28a745';
        bgColor = 'rgba(40, 167, 69, 0.1)';
      } else if (message.includes('❌')) {
        borderColor = '#dc3545';
        bgColor = 'rgba(220, 53, 69, 0.1)';
      } else if (message.includes('⚠️')) {
        borderColor = '#ffc107';
        bgColor = 'rgba(255, 193, 7, 0.1)';
      } else if (message.includes('🔍')) {
        borderColor = '#17a2b8';
        bgColor = 'rgba(23, 162, 184, 0.1)';
      }
      
      logEntry.style.cssText = `
        margin-bottom: 5px;
        padding: 5px;
        border-left: 3px solid ${borderColor};
        background: ${bgColor};
        font-size: 12px;
        line-height: 1.4;
      `;
      logEntry.innerHTML = `<span style="color: #888;">[${timestamp}]</span> ${message}`;
      consoleDiv.appendChild(logEntry);
      consoleDiv.scrollTop = consoleDiv.scrollHeight;
    }
  }
  
  /**
   * 添加DOM数据
   */
  addDOMData(extractorId, selector, element) {
    const domDiv = document.getElementById('debug-dom');
    if (domDiv) {
      const entry = document.createElement('div');
      entry.style.cssText = `
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #404040;
        border-radius: 4px;
      `;
      
      const html = element && element.outerHTML ? element.outerHTML : '未找到元素';
      const elementInfo = element ? {
        tagName: element.tagName || 'unknown',
        className: element.className || '',
        id: element.id || '',
        textContent: element.textContent ? element.textContent.substring(0, 100) + '...' : '',
        attributes: Array.from(element.attributes || []).map(attr => `${attr.name}="${attr.value}"`).join(' ')
      } : null;
      
      entry.innerHTML = `
        <div style="color: #ff6b35; font-weight: bold; margin-bottom: 5px;">${extractorId}</div>
        <div style="color: #888; margin-bottom: 5px;">选择器: ${selector}</div>
        ${elementInfo ? `
          <div style="color: #17a2b8; margin-bottom: 5px; font-size: 11px;">
            标签: ${elementInfo.tagName} | 类名: ${elementInfo.className} | ID: ${elementInfo.id}
          </div>
          <div style="color: #28a745; margin-bottom: 5px; font-size: 11px;">
            文本内容: ${elementInfo.textContent}
          </div>
        ` : ''}
        <div style="background: #2d2d2d; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 200px; overflow-y: auto;">
          <pre style="margin: 0; white-space: pre-wrap; word-break: break-all; font-size: 11px;">${this.escapeHtml(typeof html === 'string' ? html.substring(0, 1000) : String(html).substring(0, 1000))}${(typeof html === 'string' ? html.length : String(html).length) > 1000 ? '\n... (内容过长，已截断)' : ''}</pre>
        </div>
        <div style="margin-top: 5px; font-size: 10px; color: #888;">
          <button onclick="navigator.clipboard.writeText('${selector}')" style="margin-right: 5px; padding: 2px 6px; background: #007bff; color: white; border: none; border-radius: 2px; cursor: pointer;">复制选择器</button>
          <button onclick="navigator.clipboard.writeText(\`${this.escapeHtml(typeof html === 'string' ? html : String(html)).replace(/`/g, '\\`')}\`)" style="padding: 2px 6px; background: #28a745; color: white; border: none; border-radius: 2px; cursor: pointer;">复制HTML</button>
        </div>
      `;
      domDiv.appendChild(entry);
    }
  }
  
  /**
   * 添加系统状态信息
   * @param {Object} systemInfo 系统状态信息
   */
  addSystemInfo(systemInfo) {
    const systemArea = document.getElementById('debug-system');
    if (!systemArea) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const statusHtml = `
      <div style="margin-bottom: 15px; padding: 10px; background: #2d2d2d; border-radius: 5px; border-left: 4px solid #4CAF50;">
        <div style="color: #4CAF50; font-weight: bold; margin-bottom: 8px;">[${timestamp}] 🔧 系统状态更新</div>
        <div style="margin-left: 10px;">
          <div><strong>页面类型:</strong> <span style="color: #FFD700;">${systemInfo.pageType || 'unknown'}</span></div>
          <div><strong>检测置信度:</strong> <span style="color: #FFD700;">${systemInfo.confidence || 0}%</span></div>
          <div><strong>检测方法:</strong> <span style="color: #FFD700;">${systemInfo.detectionMethod || 'URL'}</span></div>
          <div><strong>已注册提取器:</strong> <span style="color: #FFD700;">${systemInfo.extractorCount || 0}个</span></div>
          <div><strong>物流提取器状态:</strong> <span style="color: ${systemInfo.logisticsExtractorLoaded ? '#4CAF50' : '#FF5722'};">${systemInfo.logisticsExtractorLoaded ? '✅ 已加载' : '❌ 未加载'}</span></div>
          ${systemInfo.details ? `<div><strong>详细信息:</strong> <pre style="color: #CCCCCC; margin-top: 5px;">${JSON.stringify(systemInfo.details, null, 2)}</pre></div>` : ''}
        </div>
      </div>
    `;
    
    systemArea.innerHTML = statusHtml + systemArea.innerHTML;
    
    // 限制显示条数
    const entries = systemArea.querySelectorAll('div[style*="margin-bottom: 15px"]');
    if (entries.length > 10) {
      for (let i = 10; i < entries.length; i++) {
        entries[i].remove();
      }
    }
  }
  
  /**
   * 添加物流信息调试数据
   * @param {Object} logisticsData 物流调试数据
   */
  addLogisticsInfo(logisticsData) {
    const logisticsArea = document.getElementById('debug-logistics');
    if (!logisticsArea) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const logisticsHtml = `
      <div style="margin-bottom: 15px; padding: 10px; background: #2d2d2d; border-radius: 5px; border-left: 4px solid #2196F3;">
        <div style="color: #2196F3; font-weight: bold; margin-bottom: 8px;">[${timestamp}] 🚚 物流信息调试</div>
        <div style="margin-left: 10px;">
          <div><strong>发货地:</strong> <span style="color: #FFD700;">${logisticsData.originCity || '未找到'}</span></div>
          <div><strong>目的地:</strong> <span style="color: #FFD700;">${logisticsData.destinationCity || '未找到'}</span></div>
          <div><strong>运费:</strong> <span style="color: #FFD700;">${logisticsData.shippingFee || '未找到'}</span></div>
          <div><strong>物流方式:</strong> <span style="color: #FFD700;">${logisticsData.logisticsMethod || '未找到'}</span></div>
          <div><strong>是否包邮:</strong> <span style="color: ${logisticsData.isFreeship ? '#4CAF50' : '#FF5722'};">${logisticsData.isFreeship ? '是' : '否'}</span></div>
          <div><strong>提取置信度:</strong> <span style="color: #FFD700;">${logisticsData.confidence || 0}%</span></div>
          ${logisticsData.containerFound ? `<div><strong>容器状态:</strong> <span style="color: #4CAF50;">✅ 找到物流容器</span></div>` : `<div><strong>容器状态:</strong> <span style="color: #FF5722;">❌ 未找到物流容器</span></div>`}
          ${logisticsData.selectors ? `<div><strong>使用的选择器:</strong> <pre style="color: #CCCCCC; margin-top: 5px;">${JSON.stringify(logisticsData.selectors, null, 2)}</pre></div>` : ''}
          ${logisticsData.rawData ? `<div><strong>原始数据:</strong> <pre style="color: #CCCCCC; margin-top: 5px; max-height: 200px; overflow-y: auto;">${JSON.stringify(logisticsData.rawData, null, 2)}</pre></div>` : ''}
        </div>
      </div>
    `;
    
    logisticsArea.innerHTML = logisticsHtml + logisticsArea.innerHTML;
    
    // 限制显示条数
    const entries = logisticsArea.querySelectorAll('div[style*="margin-bottom: 15px"]');
    if (entries.length > 5) {
      for (let i = 5; i < entries.length; i++) {
        entries[i].remove();
      }
    }
  }

  /**
   * 添加提取结果
   */
  addResult(extractorId, data) {
    const resultsDiv = document.getElementById('debug-results');
    if (resultsDiv) {
      const entry = document.createElement('div');
      entry.style.cssText = `
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #404040;
        border-radius: 4px;
      `;
      
      // 分析数据结构
      const dataAnalysis = this.analyzeData(data);
      const jsonString = JSON.stringify(data, null, 2);
      
      entry.innerHTML = `
        <div style="color: #ff6b35; font-weight: bold; margin-bottom: 5px;">${extractorId}</div>
        <div style="margin-bottom: 10px;">
          <div style="color: #17a2b8; font-size: 12px; margin-bottom: 3px;">📊 数据统计: ${dataAnalysis.summary}</div>
          <div style="color: #28a745; font-size: 11px; margin-bottom: 3px;">✅ 成功字段: ${dataAnalysis.successFields.join(', ')}</div>
          ${dataAnalysis.emptyFields.length > 0 ? `<div style="color: #ffc107; font-size: 11px; margin-bottom: 3px;">⚠️ 空字段: ${dataAnalysis.emptyFields.join(', ')}</div>` : ''}
          <div style="color: #888; font-size: 11px;">🔍 置信度: ${dataAnalysis.confidence || 'N/A'} | 提取方法: ${dataAnalysis.method || 'N/A'}</div>
        </div>
        <div style="background: #2d2d2d; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px; overflow-y: auto;">
          <pre style="margin: 0; white-space: pre-wrap; font-size: 11px;">${jsonString}</pre>
        </div>
        <div style="margin-top: 5px; font-size: 10px; color: #888;">
          <button onclick="navigator.clipboard.writeText(\`${jsonString.replace(/`/g, '\\`')}\`)" style="margin-right: 5px; padding: 2px 6px; background: #28a745; color: white; border: none; border-radius: 2px; cursor: pointer;">复制JSON</button>
          <button onclick="console.log('${extractorId} 提取结果:', ${jsonString.replace(/'/g, "\\'")})" style="margin-right: 5px; padding: 2px 6px; background: #6f42c1; color: white; border: none; border-radius: 2px; cursor: pointer;">输出到控制台</button>
          <button onclick="window.debugPanel.exportData('${extractorId}', ${jsonString.replace(/'/g, "\\'")})" style="padding: 2px 6px; background: #fd7e14; color: white; border: none; border-radius: 2px; cursor: pointer;">导出数据</button>
        </div>
      `;
      resultsDiv.appendChild(entry);
    }
  }
  
  /**
   * 切换标签页
   */
  switchTab(tabName) {
    // 更新标签样式
    document.querySelectorAll('.debug-tab').forEach(tab => {
      if (tab.dataset.tab === tabName) {
        tab.classList.add('active');
        tab.style.borderBottomColor = '#ff6b35';
      } else {
        tab.classList.remove('active');
        tab.style.borderBottomColor = 'transparent';
      }
    });
    
    // 切换内容 - 隐藏所有内容区域
    const contentIds = [
      'debug-console', 'debug-specs', 'debug-price', 'debug-title',
      'debug-rating', 'debug-images', 'debug-merchant', 'debug-dom', 'debug-results'
    ];
    
    contentIds.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.style.display = 'none';
      }
    });
    
    // 显示选中的内容区域
    const targetId = `debug-${tabName}`;
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.style.display = 'block';
    }
  }
  
  /**
   * 显示面板
   */
  show() {
    this.panel.style.display = 'flex';
    this.isVisible = true;
  }
  
  /**
   * 隐藏面板
   */
  hide() {
    this.panel.style.display = 'none';
    this.isVisible = false;
  }
  
  /**
   * 切换显示状态
   */
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }
  
  /**
   * 清除所有内容
   */
  clear() {
    const contentIds = [
      'debug-console', 'debug-specs', 'debug-price', 'debug-title',
      'debug-rating', 'debug-images', 'debug-merchant', 'debug-dom', 'debug-results'
    ];
    
    contentIds.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.innerHTML = '';
      }
    });
  }
  
  /**
   * 只清除控制台内容
   */
  clearConsoleOnly() {
    const consoleDiv = document.getElementById('debug-console');
    if (consoleDiv) {
      consoleDiv.innerHTML = '';
    }
  }
  
  /**
   * 清除指定模块的控制台内容
   */
  clearModuleConsole(module) {
    const moduleDiv = document.getElementById(`debug-${module}`);
    if (moduleDiv) {
      moduleDiv.innerHTML = '';
    }
    
    // 如果清除的是总控制台，也清除所有模块
    if (module === 'console') {
      this.clear();
    }
  }
  
  /**
   * 分析数据结构
   */
  analyzeData(data) {
    if (!data || typeof data !== 'object') {
      return {
        summary: '无效数据',
        successFields: [],
        emptyFields: [],
        confidence: null,
        method: null
      };
    }
    
    const successFields = [];
    const emptyFields = [];
    let totalFields = 0;
    
    for (const [key, value] of Object.entries(data)) {
      totalFields++;
      if (value !== null && value !== undefined && value !== '' && 
          !(Array.isArray(value) && value.length === 0) &&
          !(typeof value === 'object' && Object.keys(value).length === 0)) {
        successFields.push(key);
      } else {
        emptyFields.push(key);
      }
    }
    
    const successRate = totalFields > 0 ? Math.round((successFields.length / totalFields) * 100) : 0;
    
    return {
      summary: `${successFields.length}/${totalFields} 字段有数据 (${successRate}%)`,
      successFields,
      emptyFields,
      confidence: data.confidence || data.extractionConfidence,
      method: data.extractionMethod || data.method
    };
  }
  
  /**
   * 导出数据
   */
  exportData(extractorId, data) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${extractorId}_${timestamp}.json`;
      const jsonString = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
      
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      console.log(`✅ 数据已导出: ${filename}`);
    } catch (error) {
      console.error('❌ 数据导出失败:', error);
    }
  }
  
  /**
   * 转义HTML
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    if (this.panel) {
      this.panel.remove();
    }
    if (this.debugButton) {
      this.debugButton.remove();
    }
  }
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.DebugPanel = DebugPanel;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = DebugPanel;
}