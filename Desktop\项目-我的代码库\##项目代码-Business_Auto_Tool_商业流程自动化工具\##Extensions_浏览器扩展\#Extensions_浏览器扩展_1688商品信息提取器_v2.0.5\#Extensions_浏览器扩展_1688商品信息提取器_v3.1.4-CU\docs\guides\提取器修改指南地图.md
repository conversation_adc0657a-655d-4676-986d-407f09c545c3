# 🗺️ 提取器修改指南地图

## 📋 概述

本指南提供了增量式修改提取器的完整地图导航，帮助您精确定位需要修改的文件和位置。

## 🎯 修改类型导航

### 1️⃣ 添加新的提取元素

**场景**：需要提取页面上的新元素（如新增的价格字段、属性字段等）

**修改地图**：
```
📁 extractors/[模式]/extractor_[模式]_[功能]_v1.0.js
├── 🔧 getSelectors() 方法
│   └── 添加新的CSS选择器
├── 🔧 extract[功能]() 方法
│   └── 添加新的提取逻辑
└── 🔧 enhance[功能]Info() 方法
    └── 添加数据处理逻辑
```

**具体步骤**：
1. **定位文件**：`extractors/wholesale/extractor_wholesale_[功能]_v1.0.js`
2. **修改选择器**：在`getSelectors()`方法中添加新选择器
3. **添加提取逻辑**：在对应的提取方法中添加新的DOM查询和数据提取
4. **更新数据结构**：确保返回的数据包含新字段
5. **测试验证**：使用调试面板验证提取结果

### 2️⃣ 修改现有提取元素

**场景**：页面结构变化，需要更新选择器或提取逻辑

**修改地图**：
```
📁 extractors/[模式]/extractor_[模式]_[功能]_v1.0.js
├── 🔧 getSelectors() 方法
│   └── 更新CSS选择器
├── 🔧 extract[功能]() 方法
│   └── 修改提取逻辑
└── 🔧 validate() 方法
    └── 更新验证规则
```

**具体步骤**：
1. **使用调试面板**：查看当前选择器是否还能找到元素
2. **更新选择器**：在`getSelectors()`中修改对应的CSS选择器
3. **调整提取逻辑**：根据新的DOM结构调整提取方法
4. **验证数据格式**：确保返回数据格式保持一致
5. **测试兼容性**：确保修改不影响其他功能

### 3️⃣ 删除不需要的提取元素

**场景**：某些元素不再需要提取，或页面已移除该元素

**修改地图**：
```
📁 extractors/[模式]/extractor_[模式]_[功能]_v1.0.js
├── 🔧 getSelectors() 方法
│   └── 移除相关选择器
├── 🔧 extract[功能]() 方法
│   └── 移除提取逻辑
└── 📁 core/ui-manager.js
    └── 🔧 formatResultItem() 方法
        └── 移除UI显示逻辑
```

**具体步骤**：
1. **移除选择器**：从`getSelectors()`中删除不需要的选择器
2. **清理提取逻辑**：移除相关的DOM查询和数据处理代码
3. **更新数据结构**：从返回数据中移除对应字段
4. **更新UI显示**：从UI管理器中移除相关显示逻辑
5. **清理验证规则**：更新`validate()`方法中的验证逻辑

## 🗂️ 文件结构地图

### 核心文件定位

```
📁 项目根目录/
├── 📁 core/                           # 核心管理层
│   ├── 📄 extraction-manager.js       # 🎯 提取器注册和协调
│   ├── 📄 ui-manager.js               # 🎯 UI显示逻辑
│   ├── 📄 debug-panel.js              # 🆕 调试面板
│   └── 📄 base-extractor.js           # 🎯 提取器基类
├── 📁 extractors/                     # 数据提取层
│   ├── 📁 wholesale/                  # 批发模式提取器
│   │   ├── 📄 extractor_wholesale_merchant_v1.0.js     # 商家信息
│   │   ├── 📄 extractor_wholesale_price_v1.0.js        # 价格信息
│   │   ├── 📄 extractor_wholesale_product_title_v1.0.js # 商品标题
│   │   ├── 📄 extractor_wholesale_product_rating_v1.0.js # 商品评价
│   │   ├── 📄 extractor_wholesale_product_images_v1.0.js # 商品图片
│   │   └── 📄 extractor_wholesale_product_specs_v1.0.js  # 🎯 商品属性
│   └── 📁 consign/                    # 代发模式提取器
│       └── 📄 [相同的文件结构]
├── 📁 styles/
│   └── 📄 content.css                 # 🎯 UI样式
└── 📄 manifest.json                   # 🎯 脚本加载配置
```

### 关键方法地图

**每个提取器的标准结构**：
```javascript
class [功能]Extractor extends BaseExtractor {
  constructor()                    // 🏗️ 初始化配置
  getSelectors()                  // 🎯 CSS选择器配置
  async extract()                 // 🚀 主提取方法
  async extract[功能]Info()        // 🔧 具体提取逻辑
  enhance[功能]Info()             // ✨ 数据增强处理
  validate()                      // ✅ 数据验证
  format()                        // 📝 数据格式化
}
```

## 🛠️ 常见修改场景

### 场景1：添加新的商品属性字段

**目标**：提取商品的"保质期"信息

**修改路径**：
1. **📄 extractor_wholesale_product_specs_v1.0.js**
   ```javascript
   // 在getSelectors()中添加
   shelfLife: [
     '.shelf-life',
     '.expiry-date',
     '[data-shelf-life]'
   ]
   ```

2. **在extractSpecsInfo()中添加**
   ```javascript
   // 提取保质期信息
   specsInfo.shelfLife = await this.extractShelfLife(selectors.primary.shelfLife);
   ```

3. **添加提取方法**
   ```javascript
   async extractShelfLife(selectors) {
     for (const selector of selectors) {
       const element = this.safeQuerySelector(selector);
       if (element) {
         return this.getElementText(element);
       }
     }
     return null;
   }
   ```

### 场景2：修改价格提取逻辑

**目标**：页面价格结构变化，需要更新选择器

**修改路径**：
1. **📄 extractor_wholesale_price_v1.0.js**
   ```javascript
   // 在getSelectors()中更新
   couponPrice: [
     '.new-price-selector',    // 新增
     '.coupon-price',          // 保留
     '.wholesale-price'        // 保留
   ]
   ```

2. **使用调试面板验证**：
   - 打开调试面板
   - 查看"DOM数据"标签
   - 确认新选择器能找到正确元素

### 场景3：删除不需要的图片类型

**目标**：不再提取缩略图，只提取主图

**修改路径**：
1. **📄 extractor_wholesale_product_images_v1.0.js**
   ```javascript
   // 从getSelectors()中移除
   // thumbnails: [...] // 删除这个配置
   ```

2. **从extractImagesInfo()中移除**
   ```javascript
   // 删除缩略图提取逻辑
   // imagesInfo.thumbnails = await this.extractThumbnails(...);
   ```

3. **📄 core/ui-manager.js**
   ```javascript
   // 从formatResultItem()中移除缩略图显示逻辑
   ```

## 🔍 调试工具使用指南

### 调试面板功能

**位置**：页面左下角的"🔍 调试"按钮

**功能标签**：
- **控制台**：查看实时调试信息
- **DOM数据**：查看找到的DOM元素和HTML代码
- **提取结果**：查看最终提取的数据结构

### 调试流程

1. **打开调试面板**：点击左下角调试按钮
2. **触发提取**：刷新页面或手动触发提取
3. **查看控制台**：观察提取过程中的日志信息
4. **检查DOM数据**：确认选择器是否找到正确元素
5. **验证结果**：查看最终提取的数据是否正确

### 常见调试信息解读

**正常情况**：
```
✅ [商品属性调试] 找到容器: <div class="od-collapse-module">
✅ [商品属性调试] 添加属性: 颜色 = 红色
🎯 [商品属性调试] 总共提取到 5 个属性
```

**问题情况**：
```
❌ [商品属性调试] 选择器无效: .od-collapse-module
⚠️ [商品属性调试] 未找到任何规格容器
❌ [商品属性调试] 该行未提取到有效数据
```

## 📝 修改检查清单

### 添加新功能时
- [ ] 更新选择器配置
- [ ] 添加提取逻辑
- [ ] 更新数据结构
- [ ] 添加UI显示逻辑
- [ ] 更新验证规则
- [ ] 同步批发和代发模式
- [ ] 使用调试面板测试

### 修改现有功能时
- [ ] 备份原有配置
- [ ] 更新选择器
- [ ] 调整提取逻辑
- [ ] 验证数据兼容性
- [ ] 测试边界情况
- [ ] 同步双模式修改
- [ ] 验证UI显示正常

### 删除功能时
- [ ] 移除选择器配置
- [ ] 清理提取逻辑
- [ ] 更新数据结构
- [ ] 移除UI显示逻辑
- [ ] 更新验证规则
- [ ] 清理相关依赖
- [ ] 测试系统稳定性

## 🚀 快速修改模板

### 新增属性提取模板

```javascript
// 1. 在getSelectors()中添加
[属性名]: [
  '.[新选择器1]',
  '.[新选择器2]',
  '[data-[属性名]]'
],

// 2. 在extractSpecsInfo()中添加
specsInfo.[属性名] = await this.extract[属性名](selectors.primary.[属性名]);

// 3. 添加提取方法
async extract[属性名](selectors) {
  for (const selector of selectors) {
    const element = this.safeQuerySelector(selector);
    if (element) {
      return this.getElementText(element);
    }
  }
  return null;
}
```

### 选择器更新模板

```javascript
// 原有选择器
[字段名]: [
  '.old-selector',     // 可能失效
  '.backup-selector'   // 备用选择器
],

// 更新后选择器
[字段名]: [
  '.new-selector',     // 新增的主选择器
  '.old-selector',     // 保留作为备用
  '.backup-selector'   // 继续保留
],
```

## 📞 技术支持

如果在修改过程中遇到问题：

1. **使用调试面板**：查看详细的提取过程和错误信息
2. **检查控制台**：查看浏览器开发者工具中的错误信息
3. **参考现有代码**：查看其他提取器的实现方式
4. **逐步测试**：每次只修改一个小部分，逐步验证

---

**版本**: v1.0.0  
**更新日期**: 2025年1月  
**适用范围**: 1688商品信息提取器 v3.0.0+