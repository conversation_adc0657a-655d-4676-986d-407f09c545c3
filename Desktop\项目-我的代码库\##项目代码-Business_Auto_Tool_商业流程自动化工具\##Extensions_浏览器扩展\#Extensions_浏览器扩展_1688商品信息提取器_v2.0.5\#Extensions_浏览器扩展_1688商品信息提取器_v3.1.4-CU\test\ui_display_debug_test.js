/**
 * UI显示调试测试脚本
 * 专门用于调试物流信息UI显示问题
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * 测试UI管理器的物流信息显示
 */
function testUILogisticsDisplay() {
  console.log('🧪 开始测试UI物流信息显示...');
  
  if (!window.UIManager || !window.uiManager) {
    console.error('❌ UIManager 未找到');
    return false;
  }
  
  const uiManager = window.uiManager;
  
  // 测试用例1: 简单物流数据结构
  console.log('\n📋 测试用例1: 简单物流数据结构');
  const simpleLogisticsResult = {
    name: '批发物流信息',
    data: {
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起',
      deliveryPromise: '承诺48小时发货',
      isFreeship: false
    }
  };
  
  const simpleHTML = uiManager.formatResultItem('1688_wholesale_logistics_001', simpleLogisticsResult);
  console.log('简单结构HTML:', simpleHTML);
  
  // 测试用例2: 嵌套物流数据结构
  console.log('\n📋 测试用例2: 嵌套物流数据结构');
  const nestedLogisticsResult = {
    name: '代发物流信息',
    data: {
      logistics: {
        route: {
          origin: '广东东莞',
          destination: '上海浦东'
        },
        shipping: {
          fee: '¥15.00'
        },
        service: {
          method: '顺丰快递',
          deliveryTime: '24小时内发货'
        }
      }
    }
  };
  
  const nestedHTML = uiManager.formatResultItem('1688_consign_logistics_001', nestedLogisticsResult);
  console.log('嵌套结构HTML:', nestedHTML);
  
  // 测试用例3: 实际提取器返回的数据结构
  console.log('\n📋 测试用例3: 实际提取器数据结构');
  const actualResult = {
    success: true,
    extractorId: '1688_wholesale_logistics_001',
    extractorName: '批发物流信息',
    data: {
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起',
      deliveryPromise: '承诺48小时发货',
      isFreeship: false,
      extractedFields: 4,
      dataCompleteness: 100,
      extractorType: 'wholesale',
      timestamp: Date.now()
    },
    confidence: 90,
    extractedAt: new Date().toISOString(),
    source: 'WholesaleLogisticsExtractor'
  };
  
  const actualHTML = uiManager.formatResultItem('1688_wholesale_logistics_001', actualResult);
  console.log('实际结构HTML:', actualHTML);
  
  // 测试用例4: 未知数据结构（触发通用显示）
  console.log('\n📋 测试用例4: 未知数据结构');
  const unknownResult = {
    name: '未知数据',
    data: {
      someField: 'someValue',
      anotherField: 'anotherValue'
    }
  };
  
  const unknownHTML = uiManager.formatResultItem('unknown_extractor', unknownResult);
  console.log('未知结构HTML:', unknownHTML);
  
  return true;
}

/**
 * 检查当前页面的提取结果
 */
function checkCurrentExtractionResults() {
  console.log('🔍 检查当前页面的提取结果...');
  
  if (!window.extractionManager) {
    console.error('❌ ExtractionManager 未找到');
    return;
  }
  
  const results = window.extractionManager.results;
  console.log('📊 当前提取结果:', results);
  
  // 检查物流相关的结果
  for (const [extractorId, result] of results) {
    if (extractorId.includes('logistics')) {
      console.log(`\n🚚 物流提取器结果 (${extractorId}):`);
      console.log('- 成功:', result.success);
      console.log('- 数据结构:', Object.keys(result.data || {}));
      console.log('- 完整数据:', result.data);
      
      // 测试UI显示
      if (window.uiManager) {
        const html = window.uiManager.formatResultItem(extractorId, result);
        console.log('- UI显示HTML:', html);
      }
    }
  }
}

/**
 * 模拟结果板显示
 */
function simulateResultsBoard() {
  console.log('🎨 模拟结果板显示...');
  
  if (!window.uiManager) {
    console.error('❌ UIManager 未找到');
    return;
  }
  
  // 创建测试结果
  const testResults = new Map();
  
  // 添加批发物流结果
  testResults.set('1688_wholesale_logistics_001', {
    success: true,
    name: '批发物流信息',
    data: {
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起',
      deliveryPromise: '承诺48小时发货',
      confidence: 90
    },
    timestamp: Date.now()
  });
  
  // 添加代发物流结果
  testResults.set('1688_consign_logistics_001', {
    success: true,
    name: '代发物流信息',
    data: {
      originCity: '广东东莞',
      destinationCity: '上海浦东',
      shippingFee: '¥15.00',
      deliveryTime: '24小时内发货',
      confidence: 85
    },
    timestamp: Date.now()
  });
  
  // 显示结果板
  try {
    window.uiManager.showResultsBoard(testResults);
    console.log('✅ 结果板显示成功');
  } catch (error) {
    console.error('❌ 结果板显示失败:', error);
  }
}

/**
 * 调试物流数据结构转换
 */
function debugLogisticsDataTransformation() {
  console.log('🔧 调试物流数据结构转换...');
  
  // 模拟不同的数据结构
  const testCases = [
    {
      name: '简单结构',
      data: {
        originCity: '浙江金华',
        destinationCity: '北京朝阳',
        shippingFee: '运费¥4起'
      }
    },
    {
      name: '嵌套结构',
      data: {
        data: {
          originCity: '浙江金华',
          destinationCity: '北京朝阳',
          shippingFee: '运费¥4起'
        }
      }
    },
    {
      name: '复杂嵌套结构',
      data: {
        logistics: {
          route: {
            origin: '浙江金华',
            destination: '北京朝阳'
          },
          shipping: {
            fee: '运费¥4起'
          }
        }
      }
    },
    {
      name: '实际提取器结构',
      data: {
        success: true,
        data: {
          originCity: '浙江金华',
          destinationCity: '北京朝阳',
          shippingFee: '运费¥4起'
        },
        confidence: 90
      }
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📋 测试案例 ${index + 1}: ${testCase.name}`);
    console.log('输入数据:', testCase.data);
    
    if (window.uiManager) {
      const html = window.uiManager.formatResultItem('test_logistics', testCase);
      console.log('UI输出:', html);
      
      // 检查是否包含"详细信息"
      if (html.includes('详细信息')) {
        console.warn('⚠️ 仍然显示"详细信息"，需要进一步调试');
      } else {
        console.log('✅ 成功显示具体内容');
      }
    }
  });
}

/**
 * 运行完整的UI显示调试测试
 */
function runUIDisplayDebugTest() {
  console.log('🚀 开始运行UI显示调试测试...');
  console.log('=' .repeat(60));
  
  try {
    // 测试UI物流信息显示
    testUILogisticsDisplay();
    
    // 检查当前提取结果
    checkCurrentExtractionResults();
    
    // 调试数据结构转换
    debugLogisticsDataTransformation();
    
    // 模拟结果板显示
    setTimeout(() => {
      simulateResultsBoard();
    }, 2000);
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 UI显示调试测试完成！');
    console.log('\n💡 请查看上述输出，确认物流信息是否正确显示。');
    console.log('💡 如果仍显示"详细信息"，请检查数据结构是否匹配。');
    
  } catch (error) {
    console.error('❌ 测试过程中出现异常:', error);
  }
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.UIDisplayDebugTest = {
    testUILogisticsDisplay,
    checkCurrentExtractionResults,
    simulateResultsBoard,
    debugLogisticsDataTransformation,
    runUIDisplayDebugTest
  };
  
  // 自动运行测试（延迟3秒等待页面加载）
  setTimeout(() => {
    if (window.location.href.includes('1688.com')) {
      console.log('🔧 检测到1688页面，3秒后自动运行UI显示调试测试...');
      setTimeout(runUIDisplayDebugTest, 3000);
    }
  }, 1000);
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testUILogisticsDisplay,
    checkCurrentExtractionResults,
    simulateResultsBoard,
    debugLogisticsDataTransformation,
    runUIDisplayDebugTest
  };
}