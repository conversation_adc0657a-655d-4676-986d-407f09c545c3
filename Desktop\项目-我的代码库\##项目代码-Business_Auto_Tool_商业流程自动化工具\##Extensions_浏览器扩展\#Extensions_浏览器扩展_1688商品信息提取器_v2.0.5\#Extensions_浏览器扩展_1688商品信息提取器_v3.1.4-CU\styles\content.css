/**
 * Content Script 样式文件
 * 为注入到1688页面的内容提供样式支持
 * <AUTHOR>
 * @version 3.0.2
 */

/* 防止样式冲突的命名空间 */
.extractor-1688 {
  /* 基础样式重置 */
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.extractor-1688 *,
.extractor-1688 *::before,
.extractor-1688 *::after {
  box-sizing: inherit;
}

/* 提取状态指示器 - 移到左上角 */
.extractor-status-indicator {
  position: fixed;
  top: 80px;
  left: 20px;
  z-index: 10000;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #333;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  max-width: 300px;
}

.extractor-status-indicator.hidden {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
}

.extractor-status-indicator .status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
}

.extractor-status-indicator .status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-block;
}

.extractor-status-indicator .status-icon.ready {
  background: #28a745;
  animation: pulse 2s infinite;
}

.extractor-status-indicator .status-icon.extracting {
  background: #ffc107;
  animation: spin 1s linear infinite;
}

.extractor-status-indicator .status-icon.completed {
  background: #28a745;
}

.extractor-status-indicator .status-icon.failed {
  background: #dc3545;
}

.extractor-status-indicator .status-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 高亮被提取的元素 */
.extractor-highlight {
  position: relative;
  outline: 2px solid #4facfe !important;
  outline-offset: 2px;
  background: rgba(79, 172, 254, 0.1) !important;
  transition: all 0.3s ease;
}

.extractor-highlight::after {
  content: '正在提取';
  position: absolute;
  top: -25px;
  left: 0;
  background: #4facfe;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.extractor-highlight.success {
  outline-color: #28a745 !important;
  background: rgba(40, 167, 69, 0.1) !important;
}

.extractor-highlight.success::after {
  content: '提取成功';
  background: #28a745;
}

.extractor-highlight.failed {
  outline-color: #dc3545 !important;
  background: rgba(220, 53, 69, 0.1) !important;
}

.extractor-highlight.failed::after {
  content: '提取失败';
  background: #dc3545;
}

/* 进度提示浮层 - 移到左上角，位于状态指示器下方 */
.extractor-progress-toast {
  position: fixed;
  top: 160px;
  left: 20px;
  transform: translateY(-100%);
  z-index: 10001;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  max-width: 300px;
  text-align: center;
}

.extractor-progress-toast.hidden {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
}

.extractor-progress-toast.show {
  opacity: 1;
  transform: translateY(0);
}

.extractor-progress-toast .toast-icon {
  display: inline-block;
  margin-right: 8px;
  font-size: 16px;
}

.extractor-progress-toast .toast-text {
  display: inline-block;
}

/* 错误提示 - 移到左侧中间 */
.extractor-error-toast {
  position: fixed;
  top: 50%;
  left: 20px;
  transform: translateX(-100%) translateY(-50%);
  z-index: 10001;
  background: #fff;
  border: 1px solid #dc3545;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  text-align: center;
}

.extractor-error-toast .error-icon {
  font-size: 48px;
  color: #dc3545;
  margin-bottom: 12px;
}

.extractor-error-toast .error-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.extractor-error-toast .error-message {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16px;
}

.extractor-error-toast .error-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.extractor-error-toast .error-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.extractor-error-toast .error-btn.primary {
  background: #4facfe;
  color: white;
}

.extractor-error-toast .error-btn.primary:hover {
  background: #3a9bfd;
}

.extractor-error-toast .error-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.extractor-error-toast .error-btn.secondary:hover {
  background: #e9ecef;
}

.extractor-error-toast.show {
  transform: translateX(0) translateY(-50%);
}

.extractor-error-toast.hidden {
  transform: translateX(-100%) translateY(-50%);
}

/* 调试信息面板 - 移到右上角避免冲突 */
.extractor-debug-panel {
  position: fixed;
  top: 80px;
  right: 490px;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  padding: 12px;
  border-radius: 4px;
  max-width: 350px;
  max-height: 250px;
  overflow-y: auto;
  backdrop-filter: blur(5px);
}

.extractor-debug-panel.hidden {
  display: none;
}

.extractor-debug-panel .debug-header {
  color: #ffff00;
  font-weight: bold;
  margin-bottom: 8px;
  border-bottom: 1px solid #333;
  padding-bottom: 4px;
}

.extractor-debug-panel .debug-line {
  margin-bottom: 2px;
  word-break: break-all;
}

.extractor-debug-panel .debug-timestamp {
  color: #888;
  margin-right: 8px;
}

.extractor-debug-panel .debug-level {
  margin-right: 8px;
  font-weight: bold;
}

.extractor-debug-panel .debug-level.info {
  color: #00bfff;
}

.extractor-debug-panel .debug-level.warn {
  color: #ffa500;
}

.extractor-debug-panel .debug-level.error {
  color: #ff4444;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(20px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .extractor-status-indicator {
    bottom: 10px;
    left: 10px;
    right: auto;
    max-width: 280px;
    font-size: 13px;
  }
  
  .extractor-results-board {
    top: 80px;
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
    max-height: calc(100vh - 120px);
  }
  
  .extractor-progress-toast {
    top: 80px;
    right: 10px;
    left: 10px;
    transform: translateY(-100%);
    max-width: none;
  }
  
  .extractor-progress-toast.hidden {
    transform: translateY(-100%);
  }
  
  .extractor-progress-toast.show {
    transform: translateY(0);
  }
  
  .extractor-debug-panel {
    top: 80px;
    left: 10px;
    right: 10px;
    max-width: none;
  }
  
  .extractor-error-toast {
    top: 50%;
    left: 10px;
    right: 10px;
    transform: translateY(-50%);
    max-width: none;
  }
}

/* 模式专用样式 */
.wholesale-item {
  border-left-color: #ff6b35 !important;
}

.consign-item {
  border-left-color: #4ecdc4 !important;
}

.result-mode-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  margin-left: 8px;
}

.wholesale-badge {
  background: #ff6b35;
  color: white;
}

.consign-badge {
  background: #4ecdc4;
  color: white;
}

.wholesale-price {
  color: #ff6b35;
  font-weight: 600;
}

.consign-price {
  color: #4ecdc4;
  font-weight: 600;
}

.wholesale-batch,
.wholesale-discount,
.wholesale-capacity {
  color: #ff8c42;
  font-size: 12px;
}

.consign-profit,
.consign-service,
.consign-oem {
  color: #26a69a;
  font-size: 12px;
}

.wholesale-company {
  color: #ff6b35;
  font-weight: 500;
}

.consign-supplier {
  color: #4ecdc4;
  font-weight: 500;
}

.keyword-tag.consign-tag {
  background: #e0f2f1;
  color: #00695c;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .extractor-status-indicator {
    background: rgba(45, 45, 45, 0.95);
    border-color: #555;
    color: #e0e0e0;
  }
  
  .extractor-error-toast {
    background: #2d2d2d;
    border-color: #dc3545;
    color: #e0e0e0;
  }
  
  .extractor-error-toast .error-title {
    color: #e0e0e0;
  }
  
  .extractor-error-toast .error-message {
    color: #b0b0b0;
  }
  
  .keyword-tag.consign-tag {
    background: #004d40;
    color: #4db6ac;
  }
}

/* 打印样式 */
@media print {
  .extractor-status-indicator,
  .extractor-progress-toast,
  .extractor-error-toast,
  .extractor-debug-panel {
    display: none !important;
  }
  
  .extractor-highlight {
    outline: none !important;
    background: none !important;
  }
  
  .extractor-highlight::after {
    display: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .extractor-status-indicator {
    border-width: 2px;
    border-color: #000;
  }
  
  .extractor-highlight {
    outline-width: 3px !important;
  }
  
  .extractor-progress-toast {
    background: #000;
    border: 2px solid #fff;
  }
}

/* 结果公告板 - 移到右上角，扩大高度，恢复宽度 */
.extractor-results-board {
  position: fixed;
  top: 80px;
  right: 20px;
  transform: translateX(100%) scale(0.8);
  z-index: 10002;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 15px 45px rgba(0, 0, 0, 0.25);
  max-width: 420px;
  max-height: 1150px;
  width: 420px;
  height: 1150px;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.extractor-results-board.show {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.extractor-results-board .board-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 14px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.extractor-results-board .board-controls {
  display: flex;
  gap: 8px;
}

.extractor-results-board .board-minimize {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.extractor-results-board .board-minimize:hover {
  background: rgba(255, 255, 255, 0.3);
}

.extractor-results-board .board-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.extractor-results-board .board-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.extractor-results-board.minimized {
  height: auto;
  max-height: none;
}

.extractor-results-board.minimized .board-content {
  display: none;
}

.extractor-results-board.minimized .board-footer {
  display: none;
}

.extractor-results-board .board-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.extractor-results-board .board-icon {
  font-size: 24px;
}

.extractor-results-board .board-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}



.extractor-results-board .board-content {
  padding: 20px;
  max-height: calc(1150px - 120px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #4facfe #f1f1f1;
}

.extractor-results-board .board-content::-webkit-scrollbar {
  width: 6px;
}

.extractor-results-board .board-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.extractor-results-board .board-content::-webkit-scrollbar-thumb {
  background: #4facfe;
  border-radius: 3px;
}

.extractor-results-board .board-content::-webkit-scrollbar-thumb:hover {
  background: #3a8bfd;
}

.extractor-results-board .result-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  border-left: 4px solid #4facfe;
}

.extractor-results-board .result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.extractor-results-board .result-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.extractor-results-board .result-confidence {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.extractor-results-board .result-confidence.high {
  background: #d4edda;
  color: #155724;
}

.extractor-results-board .result-confidence.medium {
  background: #fff3cd;
  color: #856404;
}

.extractor-results-board .result-confidence.low {
  background: #f8d7da;
  color: #721c24;
}

.extractor-results-board .result-content {
  color: #666;
}

.extractor-results-board .result-value {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.extractor-results-board .result-value.rating-main {
  font-size: 18px;
  color: #ff6b35;
  font-weight: 600;
}

.extractor-results-board .result-detail {
  font-size: 12px;
  color: #888;
}

.extractor-results-board .result-detail-group {
  margin: 8px 0;
  padding: 8px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.extractor-results-board .result-detail-group.rating-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 4px;
}

.extractor-results-board .result-detail.consign-metric {
  color: #dc3545;
  font-weight: 500;
}

.extractor-results-board .result-detail.review-details {
  color: #28a745;
  font-weight: 500;
  cursor: pointer;
}

.extractor-results-board .result-detail.review-details:hover {
  text-decoration: underline;
}

.extractor-results-board .result-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.extractor-results-board .keyword-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.extractor-results-board .result-image {
  margin-top: 8px;
}

.extractor-results-board .result-image img {
  border-radius: 4px;
  border: 1px solid #ddd;
}

.extractor-results-board .no-results {
  text-align: center;
  color: #888;
  padding: 40px 20px;
  font-size: 14px;
}

.extractor-results-board .board-footer {
  background: #f8f9fa;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e9ecef;
}

.extractor-results-board .board-stats {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
}

.extractor-results-board .board-actions {
  display: flex;
  gap: 8px;
}

.extractor-results-board .board-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.extractor-results-board .export-btn {
  background: #4facfe;
  color: white;
}

.extractor-results-board .export-btn:hover {
  background: #3a9bfd;
  transform: translateY(-1px);
}

.extractor-results-board .close-btn {
  background: #6c757d;
  color: white;
}

.extractor-results-board .close-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* 错误报告UI */
.extractor-error-report {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10003;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: 4px solid #dc3545;
}

.extractor-error-report.hidden {
  opacity: 0;
  transform: translateX(100%);
  pointer-events: none;
}

.extractor-error-report .error-report-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.extractor-error-report .error-report-controls {
  display: flex;
  gap: 8px;
}

.extractor-error-report .error-report-minimize {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.extractor-error-report .error-report-minimize:hover {
  background: rgba(255, 255, 255, 0.3);
}

.extractor-error-report.minimized {
  height: auto;
  max-height: none;
}

.extractor-error-report.minimized .error-report-content {
  display: none;
}

.extractor-error-report.minimized .error-report-footer {
  display: none;
}

.extractor-error-report .error-report-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.extractor-error-report .error-report-icon {
  font-size: 20px;
}

.extractor-error-report .error-report-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.extractor-error-report .error-report-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.extractor-error-report .error-report-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.extractor-error-report .error-report-content {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.extractor-error-report .error-report-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.extractor-error-report .error-count,
.extractor-error-report .module-count {
  font-size: 14px;
  color: #666;
}

.extractor-error-report .error-count span,
.extractor-error-report .module-count span {
  font-weight: 600;
  color: #dc3545;
}

.extractor-error-report .error-module-item {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.extractor-error-report .error-module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.extractor-error-report .error-module-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.extractor-error-report .error-module-count {
  background: #dc3545;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.extractor-error-report .error-module-latest {
  margin-bottom: 8px;
}

.extractor-error-report .error-time {
  font-size: 12px;
  color: #888;
  margin-bottom: 4px;
}

.extractor-error-report .error-message {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.extractor-error-report .error-module-actions {
  display: flex;
  gap: 8px;
}

.extractor-error-report .error-action-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.extractor-error-report .error-action-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.extractor-error-report .error-report-footer {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e9ecef;
}

.extractor-error-report .error-report-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 4px;
}

.extractor-error-report .clear-btn {
  background: #ffc107;
  color: #212529;
}

.extractor-error-report .clear-btn:hover {
  background: #e0a800;
}

.extractor-error-report .export-btn {
  background: #17a2b8;
  color: white;
}

.extractor-error-report .export-btn:hover {
  background: #138496;
}

.extractor-error-report .close-btn {
  background: #6c757d;
  color: white;
}

.extractor-error-report .close-btn:hover {
  background: #5a6268;
}

/* 错误详情弹窗 */
.extractor-error-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10002;
  display: flex;
  align-items: center;
  justify-content: center;
}

.extractor-error-modal .error-modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.extractor-error-modal .error-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #dc3545;
  color: white;
  border-radius: 8px 8px 0 0;
}

.extractor-error-modal .error-modal-header h3 {
  margin: 0;
  font-size: 16px;
}

.extractor-error-modal .error-modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.extractor-error-modal .error-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.extractor-error-modal .error-modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.extractor-error-modal .error-detail-item {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #dc3545;
}

.extractor-error-modal .error-detail-time {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

.extractor-error-modal .error-detail-message {
  font-weight: bold;
  color: #dc3545;
  margin-bottom: 10px;
}

.extractor-error-modal .error-detail-data,
.extractor-error-modal .error-detail-stack {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 8px;
}

/* 错误详情模态框 */
.extractor-error-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10004;
  display: flex;
  align-items: center;
  justify-content: center;
}

.extractor-error-modal .error-modal-content {
  background: #fff;
  border-radius: 12px;
  max-width: 600px;
  max-height: 80vh;
  width: 90vw;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.extractor-error-modal .error-modal-header {
  background: #dc3545;
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.extractor-error-modal .error-modal-header h3 {
  margin: 0;
  font-size: 16px;
}

.extractor-error-modal .error-modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.extractor-error-modal .error-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.extractor-error-modal .error-modal-body {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.extractor-error-modal .error-detail-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  border-left: 4px solid #dc3545;
}

.extractor-error-modal .error-detail-time {
  font-size: 12px;
  color: #888;
  margin-bottom: 8px;
}

.extractor-error-modal .error-detail-message {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}

.extractor-error-modal .error-detail-data,
.extractor-error-modal .error-detail-stack {
  background: #2d3748;
  color: #e2e8f0;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
}

/* 错误提示Toast */
.extractor-error-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10005;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 16px;
  max-width: 400px;
  width: 90vw;
  border-left: 4px solid #dc3545;
  animation: slideDown 0.3s ease;
}

.extractor-error-toast .error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.extractor-error-toast .error-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.extractor-error-toast .error-message {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12px;
}

.extractor-error-toast .error-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.extractor-error-toast .error-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.extractor-error-toast .error-btn.primary {
  background: #dc3545;
  color: white;
}

.extractor-error-toast .error-btn.primary:hover {
  background: #c82333;
}

.extractor-error-toast .error-btn.secondary {
  background: #6c757d;
  color: white;
}

.extractor-error-toast .error-btn.secondary:hover {
  background: #5a6268;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 扩展程序主容器样式 */
.extractor-container {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 380px;
  max-height: 85vh;
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

/* 信息区域样式 */
.info-section {
  margin: 12px 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.section-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #dee2e6;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 1.4;
}

.item-label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
  margin-right: 8px;
}

.item-value {
  color: #212529;
  flex: 1;
  word-break: break-all;
}

/* 评价详情区域样式 */
.review-section {
  margin: 12px 0;
  padding: 10px;
  background: #fff3cd;
  border-radius: 6px;
  border-left: 3px solid #ffc107;
}

.review-content {
  max-height: 200px;
  overflow-y: auto;
}

.review-item {
  margin: 8px 0;
  padding: 8px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 12px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.review-user {
  color: #495057;
}

.review-rating {
  color: #ff6b35;
  font-weight: 600;
}

.review-date {
  color: #6c757d;
  font-size: 11px;
}

.review-content-text {
  color: #212529;
  line-height: 1.4;
  margin: 6px 0;
  padding: 4px 0;
  border-bottom: 1px solid #f8f9fa;
}

.review-details {
  display: flex;
  justify-content: space-between;
  color: #6c757d;
  font-size: 11px;
  margin-top: 4px;
}

.review-quantity, .review-spec {
  flex: 1;
}

/* 代发模式专用样式 */
.extractor-results-board.consign-mode {
  border-left: 4px solid #28a745;
}

.extractor-results-board.consign-mode .board-header {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.mode-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  margin-left: 8px;
}

.mode-badge.consign {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.result-item.rating-item.expanded {
  border-left: 3px solid #ffc107;
  background: #fffbf0;
}

.result-badge {
  display: inline-block;
  padding: 2px 6px;
  background: #17a2b8;
  color: white;
  border-radius: 8px;
  font-size: 10px;
  margin-left: 8px;
}

.rating-content {
  padding: 0;
}

.info-section {
  margin: 12px 0;
  border-radius: 6px;
  overflow: hidden;
}

.info-section.basic-info {
  background: #f8f9fa;
  border-left: 3px solid #007bff;
}

.info-section.rating-stats {
  background: #fff3cd;
  border-left: 3px solid #ffc107;
}

.info-section.sales-info {
  background: #d1ecf1;
  border-left: 3px solid #17a2b8;
}

.info-section.review-details {
  background: #d4edda;
  border-left: 3px solid #28a745;
}

.section-title {
  padding: 8px 12px;
  font-weight: 600;
  font-size: 13px;
  background: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.section-content {
  padding: 8px 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.info-item {
  display: flex;
  margin: 4px 0;
  font-size: 12px;
  line-height: 1.4;
}

.item-label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
  margin-right: 8px;
}

.item-value {
  color: #212529;
  flex: 1;
  word-break: break-all;
}

.review-list {
  max-height: 200px;
  overflow-y: auto;
}

.review-item {
  margin: 8px 0;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 11px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.review-user {
  color: #495057;
}

.review-rating {
  color: #ff6b35;
  font-weight: 600;
}

.review-date {
  color: #6c757d;
  font-size: 10px;
}

.review-content {
  color: #212529;
  line-height: 1.4;
  margin: 6px 0;
  padding: 4px 0;
  border-bottom: 1px solid #f8f9fa;
}

.review-meta {
  display: flex;
  justify-content: space-between;
  color: #6c757d;
  font-size: 10px;
  margin-top: 4px;
}

.more-reviews {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin-top: 8px;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media (max-width: 1200px) {
  .extractor-results-board {
    width: calc(100vw - 40px);
    max-width: calc(100vw - 40px);
    height: calc(100vh - 120px);
    max-height: calc(100vh - 120px);
    right: 20px;
  }
  
  .extractor-results-board .board-content {
    max-height: calc(100vh - 200px);
  }
}

@media (max-width: 768px) {
  .extractor-results-board {
    width: calc(100vw - 20px);
    max-width: calc(100vw - 20px);
    height: calc(100vh - 80px);
    max-height: calc(100vh - 80px);
    right: 10px;
    top: 60px;
  }
  
  .extractor-results-board .board-content {
    max-height: calc(100vh - 160px);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .extractor-status-indicator,
  .extractor-highlight,
  .extractor-progress-toast,
  .extractor-error-toast,
  .extractor-results-board,
  .extractor-error-report {
    transition: none;
    animation: none;
  }
  
  .extractor-status-indicator .status-icon {
    animation: none;
  }
  
  .extractor-results-board.show {
    transform: translate(-50%, -50%) scale(1);
  }
  
  .extractor-error-report.hidden {
    transform: translateX(100%);
  }
}