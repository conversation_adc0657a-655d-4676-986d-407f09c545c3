/**
 * 基础抽取器类 - 所有抽取器的基类
 * 提供统一的接口和通用功能
 * <AUTHOR>
 * @version 1.0.0
 */

class BaseExtractor {
  /**
   * 构造函数
   * @param {string} moduleId - 模块唯一标识
   * @param {string} name - 模块显示名称
   * @param {string} description - 模块描述
   */
  constructor(moduleId, name, description = '') {
    this.moduleId = moduleId;
    this.name = name;
    this.description = description;
    this.status = 'pending'; // pending, processing, completed, failed
    this.progress = 0; // 0-100
    this.result = null;
    this.error = null;
    this.startTime = null;
    this.endTime = null;
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  /**
   * 获取CSS选择器配置
   * 子类必须实现此方法
   * @returns {Object} 选择器配置对象
   */
  getSelectors() {
    throw new Error(`getSelectors method must be implemented in ${this.constructor.name}`);
  }

  /**
   * 执行数据提取
   * 子类必须实现此方法
   * @returns {Promise<any>} 提取的数据
   */
  async extract() {
    throw new Error(`extract method must be implemented in ${this.constructor.name}`);
  }

  /**
   * 数据验证
   * 子类可以重写此方法
   * @param {any} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    return data !== null && data !== undefined && data !== '';
  }

  /**
   * 数据格式化
   * 子类可以重写此方法
   * @param {any} data - 原始数据
   * @returns {any} 格式化后的数据
   */
  format(data) {
    if (typeof data === 'string') {
      return data.trim().replace(/\s+/g, ' ');
    }
    return data;
  }

  /**
   * 执行完整的提取流程
   * @returns {Promise<Object>} 提取结果
   */
  async run() {
    this.startTime = Date.now();
    this.status = 'processing';
    this.progress = 0;
    this.notifyProgress();

    try {
      // 更新进度：开始提取
      this.progress = 20;
      this.notifyProgress();

      // 执行提取
      const rawData = await this.extract();
      
      // 更新进度：提取完成
      this.progress = 60;
      this.notifyProgress();

      // 数据验证
      if (!this.validate(rawData)) {
        throw new Error(`Data validation failed for ${this.moduleId}`);
      }

      // 更新进度：验证完成
      this.progress = 80;
      this.notifyProgress();

      // 数据格式化
      this.result = this.format(rawData);
      
      // 完成
      this.progress = 100;
      this.status = 'completed';
      this.endTime = Date.now();
      this.notifyProgress();

      return {
        success: true,
        moduleId: this.moduleId,
        name: this.name,
        data: this.result,
        duration: this.endTime - this.startTime
      };

    } catch (error) {
      this.error = error;
      this.status = 'failed';
      this.endTime = Date.now();
      this.notifyProgress();

      // 如果还有重试次数，尝试重试
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        console.warn(`${this.moduleId} failed, retrying (${this.retryCount}/${this.maxRetries})...`);
        await this.delay(1000 * this.retryCount); // 递增延迟
        return this.run();
      }

      return {
        success: false,
        moduleId: this.moduleId,
        name: this.name,
        error: error.message,
        duration: this.endTime - this.startTime
      };
    }
  }

  /**
   * 通知进度更新
   */
  notifyProgress() {
    if (window.ProgressManager) {
      window.ProgressManager.updateProgress(this.moduleId, this.progress, this.status);
    }
    
    // 发送消息到background script
    if (chrome && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'PROGRESS_UPDATE',
        moduleId: this.moduleId,
        name: this.name,
        progress: this.progress,
        status: this.status,
        result: this.result,
        error: this.error
      }).catch(err => {
        console.warn('Failed to send progress update:', err);
      });
    }
  }

  /**
   * 延迟执行
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 安全的DOM查询
   * @param {string} selector - CSS选择器
   * @param {Element} context - 查询上下文，默认为document
   * @returns {Element|null} 查询结果
   */
  safeQuerySelector(selector, context = document) {
    try {
      return context.querySelector(selector);
    } catch (error) {
      console.warn(`Invalid selector: ${selector}`, error);
      return null;
    }
  }

  /**
   * 安全的DOM查询（多个元素）
   * @param {string} selector - CSS选择器
   * @param {Element} context - 查询上下文，默认为document
   * @returns {NodeList} 查询结果
   */
  safeQuerySelectorAll(selector, context = document) {
    try {
      return context.querySelectorAll(selector);
    } catch (error) {
      console.warn(`Invalid selector: ${selector}`, error);
      return [];
    }
  }

  /**
   * 等待元素出现
   * @param {string} selector - CSS选择器
   * @param {number} timeout - 超时时间（毫秒）
   * @param {Element} context - 查询上下文
   * @returns {Promise<Element>} 元素
   */
  waitForElement(selector, timeout = 5000, context = document) {
    return new Promise((resolve, reject) => {
      const element = this.safeQuerySelector(selector, context);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = this.safeQuerySelector(selector, context);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(context, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element not found: ${selector} (timeout: ${timeout}ms)`));
      }, timeout);
    });
  }

  /**
   * 获取模块信息
   * @returns {Object} 模块信息
   */
  getInfo() {
    return {
      moduleId: this.moduleId,
      name: this.name,
      description: this.description,
      status: this.status,
      progress: this.progress,
      result: this.result,
      error: this.error,
      retryCount: this.retryCount,
      duration: this.endTime && this.startTime ? this.endTime - this.startTime : null
    };
  }

  /**
   * 重置模块状态
   */
  reset() {
    this.status = 'pending';
    this.progress = 0;
    this.result = null;
    this.error = null;
    this.startTime = null;
    this.endTime = null;
    this.retryCount = 0;
  }
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.BaseExtractor = BaseExtractor;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BaseExtractor;
}