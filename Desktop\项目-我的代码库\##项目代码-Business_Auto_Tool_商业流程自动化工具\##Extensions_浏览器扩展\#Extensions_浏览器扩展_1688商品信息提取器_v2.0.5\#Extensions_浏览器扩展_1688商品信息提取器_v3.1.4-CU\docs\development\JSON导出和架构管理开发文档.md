# JSON导出和架构管理开发文档

## 📋 概述

本文档详细说明了1688商品信息提取器的JSON导出格式、架构管理系统和模块化设计思路，为第三方软件集成和项目维护提供完整的技术规范。

## 🎯 设计目标

### 核心目标
1. **标准化数据格式**: 为第三方软件提供统一的JSON数据接口
2. **自动化导出流程**: 提取完成后自动复制到剪切板
3. **架构管理系统**: 监控和管理模块化文件，防止混乱
4. **模式识别**: 明确区分批发和代发模式的数据结构

### 应用场景
- 第三方软件从剪切板读取JSON数据
- 数据分析和处理工具集成
- 商品信息管理系统对接
- 电商运营工具数据源

## 📊 标准JSON数据格式

### 数据结构概览

```json
{
  "version": "1.0.0",
  "format": "1688_product_extraction",
  "page": {
    "url": "https://detail.1688.com/offer/711914850251.html",
    "type": "wholesale",
    "mode": "wholesale",
    "title": "商品页面标题",
    "timestamp": "2025-01-09T10:30:00.000Z"
  },
  "product": {
    "title": {
      "text": "商品标题",
      "keywords": ["关键词1", "关键词2"],
      "confidence": 85
    },
    "images": [
      {
        "src": "图片URL",
        "alt": "图片描述",
        "width": 800,
        "height": 600,
        "type": "main",
        "quality": "high"
      }
    ],
    "specifications": {
      "details": {
        "材质": "棉",
        "颜色": "多色可选",
        "尺寸": "均码"
      },
      "variants": {
        "颜色": [
          {"text": "红色", "value": "red", "available": true},
          {"text": "蓝色", "value": "blue", "available": true}
        ]
      },
      "colors": [
        {"name": "红色", "value": "red", "color": "#ff0000", "available": true}
      ],
      "sizes": [
        {"name": "S", "value": "small", "available": true}
      ],
      "materials": [
        {"name": "纯棉", "type": "natural"}
      ],
      "brand": "品牌名称"
    },
    "rating": {
      "score": 4.5,
      "stars": {
        "rating": 4.5,
        "fullStars": 4,
        "halfStars": 1,
        "emptyStars": 0,
        "totalStars": 5
      },
      "reviewCount": 1250,
      "salesCount": 5680,
      "favoriteCount": 890,
      "viewCount": 12500,
      "positiveRate": 96.8
    }
  },
  "merchant": {
    "name": "义乌市靖发箱包有限公司",
    "type": "wholesale",
    "contact": {
      "phone": "联系电话",
      "address": "公司地址"
    },
    "credentials": {
      "businessLicense": "营业执照信息",
      "certifications": ["认证信息"]
    }
  },
  "pricing": {
    "mode": "wholesale",
    "currency": "¥",
    "wholesale": {
      "couponPrice": {
        "value": 4.00,
        "currency": "¥",
        "description": "券后价格"
      },
      "priceRange": [
        {"value": 6.00, "currency": "¥", "type": "min"},
        {"value": 27.00, "currency": "¥", "type": "max"}
      ],
      "minBatch": {
        "quantity": 1,
        "unit": "个",
        "description": "1个起批"
      }
    },
    "consign": null
  },
  "extraction": {
    "totalModules": 8,
    "successCount": 7,
    "failedCount": 1,
    "modules": {
      "wholesale-merchant-extractor": {
        "name": "批发商家信息抽取器",
        "success": true,
        "confidence": 92,
        "timestamp": 1704790200000,
        "error": null
      }
    }
  },
  "metadata": {
    "extractor": {
      "name": "1688商品信息提取器",
      "version": "3.0.0",
      "architecture": "modular"
    },
    "extraction": {
      "startTime": 1704790200000,
      "endTime": 1704790205000,
      "duration": 5000,
      "userAgent": "Mozilla/5.0...",
      "viewport": {"width": 1920, "height": 1080}
    },
    "quality": {
      "averageConfidence": 87,
      "dataCompleteness": 88,
      "reliability": "high"
    }
  }
}
```

### 关键字段说明

#### 页面信息 (page)
- `type`: 页面类型 (`wholesale` | `consign` | `unknown`)
- `mode`: 页面模式，与type相同，用于明确标识
- `url`: 完整的页面URL
- `timestamp`: 提取时间戳

#### 价格信息 (pricing)
- `mode`: 价格模式，明确标识批发或代发
- `wholesale`: 批发模式价格信息（仅批发页面有值）
  - `couponPrice`: 券后价格
  - `priceRange`: 价格区间数组
  - `minBatch`: 起批量信息
- `consign`: 代发模式价格信息（仅代发页面有值）
  - `price`: 单一价格
  - `startText`: 起始标识（如"起"）
  - `minOrder`: 最小起订量

#### 商品信息 (product)
- `title`: 标题信息，包含关键词提取
- `images`: 图片数组，包含主图和详情图
- `specifications`: 详细规格信息
- `rating`: 评价和社交证明信息

#### 提取统计 (extraction)
- `modules`: 各模块执行详情
- `successCount/failedCount`: 成功/失败统计
- 每个模块的置信度和错误信息

## 🏗️ 架构管理系统

### 系统架构

```
架构管理器 (ArchitectureManager)
├── 模块注册管理
├── 依赖关系管理
├── 加载顺序计算
├── 状态监控
└── 健康检查
```

### 模块注册机制

#### 模块信息结构
```javascript
{
  id: 'wholesale-price-extractor',
  name: 'WholesalePriceExtractor',
  type: 'extractor',
  version: '1.0.0',
  file: 'extractors/extractor_wholesale_price_v1.0.js',
  dependencies: ['base-extractor'],
  description: '批发价格信息抽取器',
  pageTypes: ['wholesale']
}
```

#### 模块类型定义
- **core**: 核心基础模块
- **extractor**: 数据抽取器模块
- **manager**: 管理器模块
- **ui**: 用户界面模块

### 依赖管理

#### 依赖关系图
```
base-extractor (基础)
├── wholesale-merchant-extractor
├── consign-merchant-extractor
├── wholesale-price-extractor
├── consign-price-extractor
├── product-title-extractor
├── product-rating-extractor
├── product-images-extractor
└── product-specs-extractor

logger-manager (基础)
├── ui-manager
├── data-export-manager
├── architecture-manager
└── extraction-manager
```

#### 加载顺序计算
1. **拓扑排序**: 根据依赖关系计算最优加载顺序
2. **循环依赖检测**: 自动检测并报告循环依赖
3. **并行加载**: 无依赖关系的模块可并行加载

### 状态监控

#### 模块状态类型
- `registered`: 已注册
- `loaded`: 已加载
- `initialized`: 已初始化
- `error`: 错误状态

#### 监控指标
- 模块加载成功率
- 平均初始化时间
- 错误发生频率
- 内存使用情况

## 📁 项目目录架构

### 标准目录结构

```
项目根目录/
├── manifest.json                 # 扩展清单文件
├── README.md                     # 项目说明
├── 系统流程图.svg                # 系统架构图
├── JSON导出和架构管理开发文档.md   # 本文档
├── 模块化架构评估报告.md          # 架构评估报告
│
├── core/                         # 核心管理模块
│   ├── base-extractor.js         # 抽取器基类
│   ├── url-detector.js           # URL检测器
│   ├── progress-manager.js       # 进度管理器
│   ├── logger-manager.js         # 日志管理器
│   ├── ui-manager.js             # UI管理器
│   ├── data-export-manager.js    # 数据导出管理器
│   ├── architecture-manager.js   # 架构管理器
│   └── extraction-manager.js     # 提取管理器
│
├── extractors/                   # 数据抽取器模块
│   ├── extractor_wholesale_merchant_v1.0.js    # 批发商家
│   ├── extractor_consign_merchant_v1.0.js      # 代发商家
│   ├── extractor_wholesale_price_v1.0.js       # 批发价格
│   ├── extractor_consign_price_v1.0.js         # 代发价格
│   ├── extractor_product_title_v1.0.js         # 商品标题
│   ├── extractor_product_rating_v1.0.js        # 评价信息
│   ├── extractor_product_images_v1.0.js        # 商品图片
│   └── extractor_product_specs_v1.0.js         # 规格属性
│
├── content/                      # 内容脚本
│   └── content-script.js         # 主协调器
│
├── popup/                        # 弹窗界面
│   ├── popup.html               # 弹窗HTML
│   ├── popup.css                # 弹窗样式
│   └── popup.js                 # 弹窗脚本
│
├── background/                   # 后台脚本
│   └── background.js            # 后台服务
│
├── styles/                       # 样式文件
│   └── content.css              # 内容样式
│
└── icons/                        # 图标文件
    ├── icon16.svg               # 16x16图标
    ├── icon32.svg               # 32x32图标
    ├── icon48.svg               # 48x48图标
    └── icon128.svg              # 128x128图标
```

### 文件命名规范

#### 抽取器模块
```
extractor_{页面类型}_{信息类型}_v{版本号}.js

示例:
- extractor_wholesale_price_v1.0.js  # 批发价格抽取器
- extractor_consign_merchant_v1.0.js # 代发商家抽取器
```

#### 管理器模块
```
{功能域}-manager.js

示例:
- logger-manager.js      # 日志管理器
- data-export-manager.js # 数据导出管理器
```

## 🔄 数据导出流程

### 自动导出流程

```mermaid
sequenceDiagram
    participant EM as ExtractionManager
    participant DEM as DataExportManager
    participant CB as Clipboard
    participant UI as UIManager
    
    EM->>EM: 提取完成
    EM->>DEM: exportToJSON(results, pageInfo)
    DEM->>DEM: buildStandardJSON()
    DEM->>DEM: validateJSONData()
    DEM-->>EM: 返回JSON数据
    
    EM->>DEM: exportToClipboard(jsonData)
    DEM->>CB: navigator.clipboard.writeText()
    CB-->>DEM: 写入成功/失败
    DEM-->>EM: 导出结果
    
    alt 导出成功
        EM->>UI: showProgressToast('数据已复制到剪切板')
    else 导出失败
        EM->>UI: showProgressToast('导出失败')
    end
```

### 导出配置选项

```javascript
// 导出配置
const exportOptions = {
  jsonIndent: 2,              // JSON缩进空格数
  includeMetadata: true,      // 是否包含元数据
  includeTimestamp: true,     // 是否包含时间戳
  compressOutput: false,      // 是否压缩输出
  autoExportToClipboard: true // 自动导出到剪切板
};
```

### 导出方法

#### 1. 自动导出到剪切板
- 提取完成后自动执行
- 使用现代Clipboard API
- 降级到传统document.execCommand

#### 2. 手动文件导出
- 用户主动触发
- 生成JSON文件下载
- 自定义文件名格式

#### 3. 导出历史管理
- 记录导出历史（最多50条）
- 导出统计信息
- 批发/代发模式分类统计

## 🔍 模块化管理策略

### 模块添加流程

1. **创建模块文件**
   ```bash
   # 在对应目录创建文件
   touch extractors/extractor_new_feature_v1.0.js
   ```

2. **实现模块代码**
   ```javascript
   class NewFeatureExtractor extends BaseExtractor {
     constructor() {
       super('new_feature_001', '新功能', '描述');
     }
     // 实现抽取逻辑
   }
   ```

3. **注册到架构管理器**
   ```javascript
   // 在architecture-manager.js中添加
   {
     id: 'new-feature-extractor',
     name: 'NewFeatureExtractor',
     type: 'extractor',
     version: '1.0.0',
     file: 'extractors/extractor_new_feature_v1.0.js',
     dependencies: ['base-extractor'],
     description: '新功能抽取器'
   }
   ```

4. **更新manifest.json**
   ```json
   {
     "content_scripts": [{
       "js": [
         "extractors/extractor_new_feature_v1.0.js"
       ]
     }]
   }
   ```

5. **注册到提取管理器**
   ```javascript
   // 在extraction-manager.js中添加
   if (window.NewFeatureExtractor) {
     const extractor = new window.NewFeatureExtractor();
     this.extractors.set(extractor.moduleId, extractor);
   }
   ```

### 模块删除流程

1. **从提取管理器注销**
2. **从架构管理器移除**
3. **从manifest.json移除**
4. **删除模块文件**
5. **更新相关依赖**

### 版本管理策略

#### 版本号规范
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

#### 版本升级流程
1. 创建新版本文件
2. 更新架构注册信息
3. 测试兼容性
4. 更新manifest引用
5. 删除旧版本文件

## 🛡️ 错误处理和监控

### 错误分类

#### 模块级错误
- 加载失败
- 初始化错误
- 运行时异常
- 依赖缺失

#### 系统级错误
- 架构完整性问题
- 循环依赖
- 资源不足
- 权限问题

### 监控指标

#### 性能指标
- 模块加载时间
- 提取执行时间
- 内存使用量
- CPU使用率

#### 质量指标
- 提取成功率
- 数据置信度
- 错误发生率
- 用户满意度

### 健康检查

```javascript
// 架构健康检查
const health = architectureManager.checkModuleHealth();

// 健康状态
{
  overall: 'healthy' | 'warning' | 'critical',
  issues: ['问题描述'],
  recommendations: ['改进建议']
}
```

## 🔧 开发和调试

### 开发环境设置

1. **启用调试模式**
   ```javascript
   const config = {
     debugMode: true,
     enableConsoleOutput: true,
     enableErrorUI: true
   };
   ```

2. **使用Chrome MCP验证**
   - 实时查看DOM元素
   - 验证选择器有效性
   - 测试数据提取结果

3. **架构管理器监控**
   ```javascript
   // 获取架构报告
   const report = architectureManager.generateArchitectureReport();
   
   // 检查模块状态
   const status = architectureManager.getModuleStatus('module-id');
   ```

### 调试工具

#### 1. 日志管理器
- 分级日志记录
- 模块独立日志
- 错误堆栈追踪
- 日志导出功能

#### 2. 错误报告UI
- 实时错误显示
- 错误详情查看
- 错误统计分析
- 错误报告导出

#### 3. 架构监控面板
- 模块状态实时显示
- 依赖关系可视化
- 性能指标监控
- 健康状态检查

## 📈 性能优化

### 加载优化

1. **按需加载**
   - 根据页面类型加载对应抽取器
   - 延迟加载非关键模块
   - 预加载常用模块

2. **并行加载**
   - 无依赖模块并行加载
   - 异步初始化
   - 资源池管理

### 执行优化

1. **智能调度**
   - 优先级队列
   - 资源分配
   - 超时控制

2. **缓存策略**
   - 结果缓存
   - DOM查询缓存
   - 配置缓存

### 内存优化

1. **资源回收**
   - 及时清理无用对象
   - 事件监听器清理
   - DOM引用清理

2. **内存监控**
   - 内存使用统计
   - 泄漏检测
   - 垃圾回收优化

## 🚀 未来扩展规划

### 短期目标 (1-3个月)

1. **UI优化**
   - 架构管理可视化界面
   - 实时监控仪表板
   - 错误诊断工具

2. **功能增强**
   - 更多电商平台支持
   - 批量处理能力
   - 数据分析功能

### 中期目标 (3-6个月)

1. **智能化**
   - AI辅助DOM识别
   - 自适应选择器生成
   - 智能错误修复

2. **云端集成**
   - 配置云端同步
   - 数据云端存储
   - 协作功能

### 长期愿景 (6个月+)

1. **平台化**
   - 插件市场
   - 第三方扩展支持
   - API开放平台

2. **生态建设**
   - 开发者社区
   - 技术文档完善
   - 培训和支持

## 📚 参考资料

### 技术标准
- [JSON Schema规范](https://json-schema.org/)
- [Chrome Extension API](https://developer.chrome.com/docs/extensions/)
- [Web Components标准](https://www.webcomponents.org/)

### 最佳实践
- [模块化JavaScript设计模式](https://addyosmani.com/resources/essentialjsdesignpatterns/book/)
- [Chrome扩展开发最佳实践](https://developer.chrome.com/docs/extensions/mv3/devguide/)
- [前端架构设计原则](https://frontendmasters.com/books/front-end-handbook/)

---

**文档版本**: v1.0.0  
**最后更新**: 2025年1月9日  
**维护者**: 1688商品信息提取器开发团队  

**核心特性**: 标准化JSON导出 + 架构管理系统 + 模块化设计 + 自动剪切板集成