/**
 * 验证窗口监控器 - 自动关闭1688验证弹窗
 * 监控页面中的验证弹窗并自动关闭
 * <AUTHOR>
 * @version 1.0.0
 */

class VerificationMonitor {
  constructor(loggerManager) {
    this.logger = loggerManager;
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.config = {
      checkInterval: 1000, // 每秒检查一次
      selectors: {
        // 验证弹窗选择器
        verificationModal: [
          '.verification-modal',
          '.verify-modal',
          '.captcha-modal',
          '[class*="verification"]',
          '[class*="verify"]',
          '[class*="captcha"]',
          '.nc_wrapper', // 阿里云验证码
          '.nc-container', // 阿里云验证码容器
          '.slide-verify', // 滑动验证
          '.verify-code', // 验证码
          '.security-verify', // 安全验证
          '.human-verify', // 人机验证
          '[id*="verify"]', // ID包含verify的元素
          '[id*="captcha"]', // ID包含captcha的元素
          '.modal-dialog', // 通用模态框
          '.popup-dialog' // 弹窗对话框
        ],
        // 关闭按钮选择器
        closeButtons: [
          '.close',
          '.close-btn',
          '.modal-close',
          '[aria-label="关闭"]',
          '[aria-label="Close"]',
          'button[title="关闭"]',
          'button[title="Close"]',
          '.icon-close',
          '.fa-times',
          '.fa-close',
          '.error-report-close', // 错误报告关闭按钮
          '.dialog-close', // 对话框关闭按钮
          '.popup-close', // 弹窗关闭按钮
          '.modal-header .close', // 模态框头部关闭按钮
          '.btn-close', // 关闭按钮类
          '[class*="close"]', // 包含close的类
          'button:contains("×")', // 包含×的按钮
          'button:contains("✕")', // 包含✕的按钮
          'span:contains("×")', // 包含×的span
          'span:contains("✕")', // 包含✕的span
          '.nc-close', // 阿里云验证码关闭按钮
          '.verify-close' // 验证关闭按钮
        ],
        // 取消按钮选择器
        cancelButtons: [
          '.cancel',
          '.cancel-btn',
          'button:contains("取消")',
          'button:contains("Cancel")',
          'button:contains("跳过")',
          'button:contains("Skip")'
        ]
      }
    };
    
    this.init();
  }
  
  /**
   * 初始化监控器
   */
  init() {
    this.logger.info('VerificationMonitor', 'Initializing verification monitor');
    
    try {
      this.startMonitoring();
      this.logger.info('VerificationMonitor', 'Verification monitor initialized successfully');
    } catch (error) {
      this.logger.error('VerificationMonitor', 'Failed to initialize verification monitor', error);
    }
  }
  
  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = true;
    this.logger.info('VerificationMonitor', 'Starting verification window monitoring');
    
    // 设置定时检查
    this.monitorInterval = setInterval(() => {
      this.checkForVerificationWindows();
    }, this.config.checkInterval);
    
    // 监听DOM变化
    this.observeDOM();
  }
  
  /**
   * 停止监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    this.logger.info('VerificationMonitor', 'Stopping verification window monitoring');
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    
    if (this.domObserver) {
      this.domObserver.disconnect();
      this.domObserver = null;
    }
  }
  
  /**
   * 检查验证窗口
   */
  checkForVerificationWindows() {
    try {
      // 检查验证弹窗
      const verificationModals = this.findVerificationModals();
      
      if (verificationModals.length > 0) {
        this.logger.info('VerificationMonitor', `Found ${verificationModals.length} verification windows`);
        
        verificationModals.forEach((modal, index) => {
          this.closeVerificationWindow(modal, index);
        });
      }
    } catch (error) {
      this.logger.error('VerificationMonitor', 'Error checking verification windows', error);
    }
  }
  
  /**
   * 查找验证弹窗
   * @returns {Array} 找到的验证弹窗元素
   */
  findVerificationModals() {
    const modals = [];
    
    // 使用多个选择器查找验证弹窗
    this.config.selectors.verificationModal.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          // 检查元素是否可见
          if (this.isElementVisible(element)) {
            // 检查是否包含验证相关内容
            if (this.isVerificationContent(element)) {
              modals.push(element);
            }
          }
        });
      } catch (error) {
        // 忽略选择器错误
      }
    });
    
    // 去重
    return [...new Set(modals)];
  }
  
  /**
   * 检查元素是否可见
   * @param {Element} element - 要检查的元素
   * @returns {boolean} 是否可见
   */
  isElementVisible(element) {
    if (!element) return false;
    
    const style = window.getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0' &&
           element.offsetWidth > 0 && 
           element.offsetHeight > 0;
  }
  
  /**
   * 检查是否为验证内容
   * @param {Element} element - 要检查的元素
   * @returns {boolean} 是否为验证内容
   */
  isVerificationContent(element) {
    const text = element.textContent || element.innerText || '';
    const verificationKeywords = [
      '验证', '滑动验证', '点击验证', '图形验证', '拖拽验证',
      'verification', 'captcha', 'verify', 'slide', 'drag',
      '请拖动', '请点击', '请滑动', '安全验证', '人机验证',
      '请按住滑块', '拖动完成验证', '滑动解锁', '向右滑动',
      '请完成安全验证', '请拖动滑块', '滑块验证', '行为验证',
      '智能验证', '无感验证', '请稍候', '验证中', '加载中',
      '网络异常', '请重试', '验证失败', '验证成功',
      'nc_wrapper', 'nc-container', 'slide-verify'
    ];
    
    // 检查文本内容
    const hasKeyword = verificationKeywords.some(keyword => {
      try {
        return text.toLowerCase().includes(keyword.toLowerCase());
      } catch (error) {
        console.warn('检查文本关键词时出错:', error, element);
        return false;
      }
    });
    
    // 检查类名和ID
    const className = (element.className && typeof element.className === 'string') ? element.className : '';
    const id = element.id || '';
    const hasVerifyClass = verificationKeywords.some(keyword => {
      try {
        return className.toLowerCase().includes(keyword.toLowerCase()) ||
               id.toLowerCase().includes(keyword.toLowerCase());
      } catch (error) {
        console.warn('检查元素关键词时出错:', error, element);
        return false;
      }
    });
    
    return hasKeyword || hasVerifyClass;
  }
  
  /**
   * 关闭验证窗口
   * @param {Element} modal - 验证弹窗元素
   * @param {number} index - 弹窗索引
   */
  closeVerificationWindow(modal, index) {
    try {
      this.logger.info('VerificationMonitor', `Attempting to close verification window ${index + 1}`);
      
      // 尝试多种关闭方式
      const closed = this.tryCloseModal(modal);
      
      if (closed) {
        this.logger.info('VerificationMonitor', `Successfully closed verification window ${index + 1}`);
      } else {
        this.logger.warn('VerificationMonitor', `Failed to close verification window ${index + 1}`);
        // 尝试隐藏弹窗
        this.hideModal(modal);
      }
    } catch (error) {
      this.logger.error('VerificationMonitor', `Error closing verification window ${index + 1}`, error);
    }
  }
  
  /**
   * 尝试关闭弹窗
   * @param {Element} modal - 弹窗元素
   * @returns {boolean} 是否成功关闭
   */
  tryCloseModal(modal) {
    // 方法1: 查找关闭按钮
    const closeButton = this.findCloseButton(modal);
    if (closeButton) {
      this.clickElement(closeButton);
      return true;
    }
    
    // 方法2: 查找取消按钮
    const cancelButton = this.findCancelButton(modal);
    if (cancelButton) {
      this.clickElement(cancelButton);
      return true;
    }
    
    // 方法3: 点击遮罩层关闭
    if (this.tryClickOverlay(modal)) {
      return true;
    }
    
    // 方法4: 按ESC键
    this.pressEscape();
    
    return false;
  }
  
  /**
   * 查找关闭按钮
   * @param {Element} modal - 弹窗元素
   * @returns {Element|null} 关闭按钮元素
   */
  findCloseButton(modal) {
    for (const selector of this.config.selectors.closeButtons) {
      try {
        const button = modal.querySelector(selector);
        if (button && this.isElementVisible(button)) {
          return button;
        }
      } catch (error) {
        // 忽略选择器错误
      }
    }
    return null;
  }
  
  /**
   * 查找取消按钮
   * @param {Element} modal - 弹窗元素
   * @returns {Element|null} 取消按钮元素
   */
  findCancelButton(modal) {
    for (const selector of this.config.selectors.cancelButtons) {
      try {
        const button = modal.querySelector(selector);
        if (button && this.isElementVisible(button)) {
          return button;
        }
      } catch (error) {
        // 忽略选择器错误
      }
    }
    
    // 查找包含取消文本的按钮
    const buttons = modal.querySelectorAll('button, a, span[role="button"]');
    for (const button of buttons) {
      const text = button.textContent || button.innerText || '';
      if (/取消|关闭|跳过|cancel|close|skip/i.test(text)) {
        return button;
      }
    }
    
    return null;
  }
  
  /**
   * 尝试点击遮罩层关闭
   * @param {Element} modal - 弹窗元素
   * @returns {boolean} 是否成功
   */
  tryClickOverlay(modal) {
    try {
      // 查找可能的遮罩层
      const overlay = modal.closest('.modal-overlay, .overlay, .mask, [class*="overlay"], [class*="mask"]');
      if (overlay && overlay !== modal) {
        this.clickElement(overlay);
        return true;
      }
    } catch (error) {
      // 忽略错误
    }
    return false;
  }
  
  /**
   * 点击元素
   * @param {Element} element - 要点击的元素
   */
  clickElement(element) {
    try {
      // 尝试多种点击方式
      if (element.click) {
        element.click();
      } else {
        // 创建点击事件
        const event = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        element.dispatchEvent(event);
      }
    } catch (error) {
      this.logger.error('VerificationMonitor', 'Error clicking element', error);
    }
  }
  
  /**
   * 按ESC键
   */
  pressEscape() {
    try {
      const event = new KeyboardEvent('keydown', {
        key: 'Escape',
        keyCode: 27,
        bubbles: true,
        cancelable: true
      });
      document.dispatchEvent(event);
    } catch (error) {
      this.logger.error('VerificationMonitor', 'Error pressing escape', error);
    }
  }
  
  /**
   * 隐藏弹窗
   * @param {Element} modal - 弹窗元素
   */
  hideModal(modal) {
    try {
      modal.style.display = 'none';
      modal.style.visibility = 'hidden';
      modal.style.opacity = '0';
      
      // 移除弹窗
      setTimeout(() => {
        if (modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      }, 100);
      
      this.logger.info('VerificationMonitor', 'Verification window hidden');
    } catch (error) {
      this.logger.error('VerificationMonitor', 'Error hiding modal', error);
    }
  }
  
  /**
   * 监听DOM变化
   */
  observeDOM() {
    try {
      this.domObserver = new MutationObserver((mutations) => {
        let shouldCheck = false;
        
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // 检查是否有新增的弹窗元素
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node;
                if (this.isVerificationContent(element) || 
                    element.querySelector && 
                    this.config.selectors.verificationModal.some(selector => {
                      try {
                        return element.querySelector(selector);
                      } catch (e) {
                        return false;
                      }
                    })) {
                  shouldCheck = true;
                }
              }
            });
          }
        });
        
        if (shouldCheck) {
          // 延迟检查，确保DOM完全渲染
          setTimeout(() => {
            this.checkForVerificationWindows();
          }, 100);
        }
      });
      
      // 开始观察
      this.domObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
      
      this.logger.info('VerificationMonitor', 'DOM observer started');
    } catch (error) {
      this.logger.error('VerificationMonitor', 'Error setting up DOM observer', error);
    }
  }
  
  /**
   * 获取监控状态
   * @returns {boolean} 是否正在监控
   */
  isActive() {
    return this.isMonitoring;
  }
  
  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring();
    this.logger.info('VerificationMonitor', 'Verification monitor destroyed');
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VerificationMonitor;
} else if (typeof window !== 'undefined') {
  window.VerificationMonitor = VerificationMonitor;
}