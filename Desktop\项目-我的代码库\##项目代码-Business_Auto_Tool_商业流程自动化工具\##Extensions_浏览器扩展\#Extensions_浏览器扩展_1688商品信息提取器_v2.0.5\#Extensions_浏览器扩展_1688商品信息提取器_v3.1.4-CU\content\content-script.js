/**
 * Content Script - 主要内容脚本 (重构版)
 * 作为协调器，使用模块化组件处理页面交互和数据提取
 * <AUTHOR>
 * @version 2.0.0
 */

(function() {
  'use strict';
  
  // 防止重复注入
  if (window.ExtractorContentScript) {
    // Content script already loaded
    return;
  }
  
  /**
   * 主要内容脚本类 (重构版)
   */
  class ExtractorContentScript {
    constructor() {
      this.isInitialized = false;
      this.pageInfo = null;
      this.loggerManager = null;
      this.uiManager = null;
      this.extractionManager = null;
      this.verificationMonitor = null;
        this.verificationAutoCloser = null;
      
      this.config = {
        debugMode: true,
        autoStart: true,
        autoStartDelay: 3000
      };
      
      this.init();
    }
    
    /**
     * 初始化
     */
    async init() {
      if (this.isInitialized) return;
      
      try {
        // Initializing 1688 Product Extractor
        
        // 检查页面是否为1688商品页面
        if (!this.isValidPage()) {
          // Not a valid 1688 product page
          return;
        }
        
        // 初始化日志管理器
        this.initLoggerManager();
        
        // 初始化验证码监控器
        this.initVerificationMonitor();
        
        // 初始化验证码自动关闭器
        this.initVerificationAutoCloser();
        
        // 检测页面类型
        await this.detectPageType();
        
        // 初始化进度管理器
        this.initProgressManager();
        
        // 初始化UI管理器
        this.initUIManager();
        
        // 初始化提取管理器
        this.initExtractionManager();
        
        // 设置消息监听
        this.setupMessageListeners();
        
        // 设置页面监听
        this.setupPageListeners();
        
        this.isInitialized = true;
        
        // 通知background script初始化完成
        this.sendMessage({
          type: 'CONTENT_SCRIPT_READY',
          pageInfo: this.pageInfo,
          extractors: this.extractionManager ? Array.from(this.extractionManager.extractors.keys()) : []
        });
        
        this.loggerManager.info('ContentScript', '1688 Product Extractor initialized successfully');
        
        // 自动开始提取（延迟让用户看到状态指示器）
        if (this.config.autoStart) {
          setTimeout(() => {
            this.uiManager.updateStatusIndicator('ready', '准备开始自动提取...');
            setTimeout(() => this.startExtraction(), 1000);
          }, this.config.autoStartDelay);
        }
        
      } catch (error) {
        console.error('Failed to initialize content script:', error);
        if (this.loggerManager) {
          this.loggerManager.error('ContentScript', 'Failed to initialize content script', error);
        }
      }
    }
    
    /**
     * 初始化日志管理器
     */
    initLoggerManager() {
      if (window.LoggerManager) {
        this.loggerManager = new window.LoggerManager();
        window.loggerManager = this.loggerManager;
        // Logger Manager initialized
      } else {
        console.error('LoggerManager not available');
      }
    }
    
    /**
     * 初始化验证码监控器
     */
    initVerificationMonitor() {
      if (window.VerificationMonitor && this.loggerManager) {
        this.verificationMonitor = new window.VerificationMonitor(this.loggerManager);
        window.verificationMonitor = this.verificationMonitor;
        this.loggerManager.info('ContentScript', 'Verification Monitor initialized');
      } else {
        if (this.loggerManager) {
          this.loggerManager.error('ContentScript', 'VerificationMonitor not available or LoggerManager missing');
        } else {
          console.error('VerificationMonitor not available or LoggerManager missing');
        }
      }
    }
    
    /**
     * 初始化验证码自动关闭器
     */
    initVerificationAutoCloser() {
      if (window.VerificationAutoCloser && this.loggerManager) {
        this.verificationAutoCloser = new window.VerificationAutoCloser(this.loggerManager);
        window.verificationAutoCloser = this.verificationAutoCloser;
        
        // 立即开始监控
        this.verificationAutoCloser.startMonitoring();
        
        this.loggerManager.info('ContentScript', 'Verification Auto Closer initialized and started');
      } else {
        if (this.loggerManager) {
          this.loggerManager.error('ContentScript', 'VerificationAutoCloser not available or LoggerManager missing');
        } else {
          console.error('VerificationAutoCloser not available or LoggerManager missing');
        }
      }
    }
    
    /**
     * 初始化UI管理器
     */
    initUIManager() {
      if (window.UIManager && this.loggerManager) {
        this.uiManager = new window.UIManager(this.loggerManager);
        window.uiManager = this.uiManager;
        this.loggerManager.info('ContentScript', 'UI Manager initialized');
      } else {
        this.loggerManager.error('ContentScript', 'UIManager not available or LoggerManager missing');
      }
    }
    
    /**
     * 初始化提取管理器
     */
    initExtractionManager() {
      if (window.ExtractionManager && this.loggerManager && this.uiManager) {
        this.extractionManager = new window.ExtractionManager(this.loggerManager, this.uiManager);
        window.extractionManager = this.extractionManager;
        this.loggerManager.info('ContentScript', 'Extraction Manager initialized');
      } else {
        this.loggerManager.error('ContentScript', 'ExtractionManager not available or dependencies missing');
      }
    }
    
    /**
     * 初始化进度管理器
     */
    initProgressManager() {
      if (window.ProgressManager) {
        window.progressManager = new window.ProgressManager();
        this.loggerManager.info('ContentScript', 'Progress Manager initialized');
      } else {
        this.loggerManager.error('ContentScript', 'ProgressManager not available');
      }
    }
    
    /**
     * 检查是否为有效页面
     */
    isValidPage() {
      const url = window.location.href;
      const hostname = window.location.hostname;
      
      // 检查域名
      if (!hostname.includes('1688.com')) {
        return false;
      }
      
      // 检查是否为商品详情页
      if (!url.includes('/offer/') || !url.includes('.html')) {
        return false;
      }
      
      return true;
    }
    
    /**
     * 检测页面类型
     */
    async detectPageType() {
      try {
        if (!window.urlDetector) {
          throw new Error('URL Detector not available');
        }
        
        const detection = window.urlDetector.detectPageType();
        
        this.pageInfo = {
          url: window.location.href,
          pageType: detection.pageType,
          confidence: detection.confidence,
          timestamp: Date.now()
        };
        
        this.loggerManager.info('ContentScript', `Page type detected: ${detection.pageType} (confidence: ${detection.confidence}%)`);
      } catch (error) {
        this.loggerManager.error('ContentScript', 'Failed to detect page type', error);
        this.pageInfo = {
          url: window.location.href,
          pageType: 'unknown',
          confidence: 0,
          timestamp: Date.now()
        };
      }
    }
    
    /**
     * 设置消息监听
     */
    setupMessageListeners() {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleMessage(message, sender, sendResponse);
        return true; // 保持消息通道开放
      });
      
      this.loggerManager.debug('ContentScript', 'Message listeners set up');
    }
    
    /**
     * 处理消息
     */
    async handleMessage(message, sender, sendResponse) {
      try {
        this.loggerManager.debug('ContentScript', `Received message: ${message.type}`);
        
        switch (message.type) {
          case 'START_EXTRACTION':
            this.uiManager.showProgressToast('收到开始提取指令');
            const result = await this.startExtraction();
            sendResponse({ success: true, data: result });
            break;
            
          case 'STOP_EXTRACTION':
            this.uiManager.showProgressToast('收到停止提取指令');
            await this.stopExtraction();
            sendResponse({ success: true });
            break;
            
          case 'GET_STATUS':
            const status = this.getStatus();
            sendResponse({ success: true, data: status });
            break;
            
          case 'GET_RESULTS':
            const results = this.getResults();
            sendResponse({ success: true, data: results });
            break;
            
          case 'RESET':
            this.uiManager.showProgressToast('收到重置指令');
            await this.resetExtraction();
            sendResponse({ success: true });
            break;
            
          case 'GET_DEBUG_INFO':
            const debugInfo = this.getDebugInfo();
            sendResponse({ success: true, data: debugInfo });
            break;
            
          default:
            this.loggerManager.warn('ContentScript', `Unknown message type: ${message.type}`);
            sendResponse({ success: false, error: 'Unknown message type' });
        }
      } catch (error) {
        this.loggerManager.error('ContentScript', `Error handling message ${message.type}`, error);
        sendResponse({ success: false, error: error.message });
      }
    }
    
    /**
     * 设置页面监听
     */
    setupPageListeners() {
      // 监听页面变化
      const observer = new MutationObserver((mutations) => {
        // 可以在这里处理页面动态变化
      });
      
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
      
      // 页面卸载时清理资源
      window.addEventListener('beforeunload', () => {
        this.cleanup();
      });
      
      this.loggerManager.debug('ContentScript', 'Page listeners set up');
    }
    
    /**
     * 开始提取
     */
    async startExtraction() {
      try {
        if (!this.extractionManager) {
          throw new Error('Extraction Manager not available');
        }
        
        this.loggerManager.info('ContentScript', 'Starting extraction via Extraction Manager');
        const result = await this.extractionManager.startExtraction();
        
        // 发送完成通知到background script
        this.sendMessage({
          type: 'EXTRACTION_COMPLETED',
          results: result.results,
          stats: result.stats
        });
        
        return result;
      } catch (error) {
        this.loggerManager.error('ContentScript', 'Failed to start extraction', error);
        
        // 发送失败通知
        this.sendMessage({
          type: 'EXTRACTION_FAILED',
          error: error.message
        });
        
        throw error;
      }
    }
    
    /**
     * 停止提取
     */
    async stopExtraction() {
      try {
        if (this.extractionManager) {
          await this.extractionManager.stopExtraction();
        }
        
        this.sendMessage({
          type: 'EXTRACTION_STOPPED'
        });
        
        this.loggerManager.info('ContentScript', 'Extraction stopped');
      } catch (error) {
        this.loggerManager.error('ContentScript', 'Failed to stop extraction', error);
      }
    }
    
    /**
     * 重置提取
     */
    async resetExtraction() {
      try {
        if (this.extractionManager) {
          await this.extractionManager.resetExtraction();
        }
        
        this.sendMessage({
          type: 'EXTRACTION_RESET'
        });
        
        this.loggerManager.info('ContentScript', 'Extraction reset');
      } catch (error) {
        this.loggerManager.error('ContentScript', 'Failed to reset extraction', error);
      }
    }
    
    /**
     * 获取状态
     */
    getStatus() {
      return {
        isInitialized: this.isInitialized,
        pageInfo: this.pageInfo,
        extractionStats: this.extractionManager ? this.extractionManager.getStats() : null,
        logStats: this.loggerManager ? this.loggerManager.getLogStats() : null
      };
    }
    
    /**
     * 获取结果
     */
    getResults() {
      if (this.extractionManager && this.extractionManager.results) {
        return Object.fromEntries(this.extractionManager.results);
      }
      return {};
    }
    
    /**
     * 发送消息到background script
     */
    sendMessage(message) {
      try {
        chrome.runtime.sendMessage(message).catch(error => {
          this.loggerManager.warn('ContentScript', 'Failed to send message to background', error);
        });
      } catch (error) {
        this.loggerManager.error('ContentScript', 'Error sending message', error);
      }
    }
    
    /**
     * 导出结果
     */
    exportResults() {
      if (this.extractionManager) {
        this.extractionManager.exportResults();
      } else {
        this.loggerManager.error('ContentScript', 'Extraction Manager not available for export');
      }
    }
    
    /**
     * 获取调试信息
     */
    getDebugInfo() {
      return {
        isInitialized: this.isInitialized,
        pageInfo: this.pageInfo,
        config: this.config,
        managers: {
          logger: !!this.loggerManager,
          ui: !!this.uiManager,
          extraction: !!this.extractionManager,
          progress: !!window.progressManager
        },
        extractors: this.extractionManager ? Array.from(this.extractionManager.extractors.keys()) : [],
        results: this.getResults(),
        logStats: this.loggerManager ? this.loggerManager.getLogStats() : null
      };
    }
    
    /**
     * 清理资源
     */
    cleanup() {
      try {
        this.loggerManager.info('ContentScript', 'Cleaning up resources');
        
        if (this.extractionManager) {
          this.extractionManager.cleanup();
        }
        
        if (this.uiManager) {
          this.uiManager.cleanup();
        }
        
        this.loggerManager.info('ContentScript', 'Cleanup completed');
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    }
  }
  
  // 创建全局实例
  window.ExtractorContentScript = ExtractorContentScript;
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.extractorInstance = new ExtractorContentScript();
    });
  } else {
    window.extractorInstance = new ExtractorContentScript();
  }
  
})();