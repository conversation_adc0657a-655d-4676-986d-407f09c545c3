/**
 * 数据导出管理器 - 标准化JSON导出和剪切板管理
 * 负责将抽取结果格式化为标准JSON格式，支持剪切板导出
 * 为第三方软件提供统一的数据接口
 * <AUTHOR>
 * @version 1.0.0
 */

class DataExportManager {
  constructor(loggerManager) {
    this.logger = loggerManager;
    this.exportHistory = [];
    this.maxHistorySize = 50;
    
    this.config = {
      jsonIndent: 2, // JSON缩进空格数
      includeMetadata: true, // 是否包含元数据
      includeTimestamp: true, // 是否包含时间戳
      compressOutput: false // 是否压缩输出
    };
    
    this.init();
  }
  
  /**
   * 初始化导出管理器
   */
  init() {
    this.logger.info('DataExportManager', 'Initializing Data Export Manager');
    
    try {
      // 检查剪切板API支持
      this.clipboardSupported = this.checkClipboardSupport();
      
      this.logger.info('DataExportManager', `Data Export Manager initialized, clipboard support: ${this.clipboardSupported}`);
    } catch (error) {
      this.logger.error('DataExportManager', 'Failed to initialize Data Export Manager', error);
    }
  }
  
  /**
   * 检查剪切板API支持
   * @returns {boolean} 是否支持剪切板API
   */
  checkClipboardSupport() {
    return !!(navigator.clipboard && navigator.clipboard.writeText);
  }
  
  /**
   * 导出抽取结果为标准JSON格式
   * @param {Map} results - 抽取结果Map
   * @param {Object} pageInfo - 页面信息
   * @param {Object} options - 导出选项
   * @returns {Promise<Object>} 标准化的JSON数据
   */
  async exportToJSON(results, pageInfo, options = {}) {
    try {
      this.logger.info('DataExportManager', 'Starting JSON export process');
      
      // 合并配置选项
      const exportConfig = { ...this.config, ...options };
      
      // 构建标准JSON结构
      const jsonData = this.buildStandardJSON(results, pageInfo, exportConfig);
      
      // 验证JSON数据
      this.validateJSONData(jsonData);
      
      // 记录导出历史
      this.recordExportHistory(jsonData);
      
      this.logger.info('DataExportManager', 'JSON export completed successfully');
      
      return jsonData;
      
    } catch (error) {
      this.logger.error('DataExportManager', 'Failed to export JSON data', error);
      throw error;
    }
  }
  
  /**
   * 构建标准JSON数据结构
   * @param {Map} results - 抽取结果
   * @param {Object} pageInfo - 页面信息
   * @param {Object} config - 配置选项
   * @returns {Object} 标准JSON数据
   */
  buildStandardJSON(results, pageInfo, config) {
    const jsonData = {
      // 基础信息
      version: '1.0.0',
      format: '1688_product_extraction',
      
      // 页面信息
      page: {
        url: pageInfo?.url || window.location.href,
        type: pageInfo?.pageType || 'unknown',
        mode: this.determinePageMode(pageInfo?.pageType),
        title: document.title || '',
        timestamp: new Date().toISOString()
      },
      
      // 商品基础信息
      product: {
        title: null,
        images: [],
        specifications: {},
        rating: null
      },
      
      // 商家信息
      merchant: {
        name: null,
        type: pageInfo?.pageType || 'unknown',
        contact: {},
        credentials: {}
      },
      
      // 价格信息
      pricing: {
        mode: this.determinePageMode(pageInfo?.pageType),
        currency: '¥',
        wholesale: null,
        consign: null
      },
      
      // 抽取统计
      extraction: {
        totalModules: results ? results.size : 0,
        successCount: 0,
        failedCount: 0,
        modules: {}
      }
    };
    
    // 填充抽取结果数据
    if (results && results.size > 0) {
      this.populateExtractionData(jsonData, results);
    }
    
    // 添加元数据（如果启用）
    if (config.includeMetadata) {
      jsonData.metadata = this.buildMetadata(results, pageInfo);
    }
    
    return jsonData;
  }
  
  /**
   * 确定页面模式
   * @param {string} pageType - 页面类型
   * @returns {string} 页面模式
   */
  determinePageMode(pageType) {
    switch (pageType) {
      case 'wholesale':
        return 'wholesale'; // 批发模式
      case 'consign':
        return 'consign'; // 代发模式
      default:
        return 'unknown';
    }
  }
  
  /**
   * 填充抽取结果数据
   * @param {Object} jsonData - JSON数据对象
   * @param {Map} results - 抽取结果
   */
  populateExtractionData(jsonData, results) {
    let successCount = 0;
    let failedCount = 0;
    
    for (const [extractorId, result] of results) {
      if (result.success) {
        successCount++;
        this.mapResultToJSON(jsonData, extractorId, result);
      } else {
        failedCount++;
      }
      
      // 记录模块执行信息
      jsonData.extraction.modules[extractorId] = {
        name: result.name || extractorId,
        success: result.success,
        confidence: result.data?.confidence || 0,
        timestamp: result.timestamp || Date.now(),
        error: result.success ? null : result.error
      };
    }
    
    jsonData.extraction.successCount = successCount;
    jsonData.extraction.failedCount = failedCount;
  }
  
  /**
   * 将抽取结果映射到JSON结构
   * @param {Object} jsonData - JSON数据对象
   * @param {string} extractorId - 抽取器ID
   * @param {Object} result - 抽取结果
   */
  mapResultToJSON(jsonData, extractorId, result) {
    const data = result.data;
    if (!data) return;
    
    // 根据抽取器类型映射数据
    if (extractorId.includes('merchant')) {
      // 商家信息
      this.mapMerchantData(jsonData.merchant, data);
    } else if (extractorId.includes('price')) {
      // 价格信息
      this.mapPriceData(jsonData.pricing, extractorId, data);
    } else if (extractorId.includes('title')) {
      // 商品标题
      this.mapTitleData(jsonData.product, data);
    } else if (extractorId.includes('images')) {
      // 商品图片
      this.mapImagesData(jsonData.product, data);
    } else if (extractorId.includes('rating')) {
      // 评价信息
      this.mapRatingData(jsonData.product, data);
    } else if (extractorId.includes('specs')) {
      // 规格信息
      this.mapSpecsData(jsonData.product, data);
    }
  }
  
  /**
   * 映射商家数据
   * @param {Object} merchantObj - 商家对象
   * @param {Object} data - 商家数据
   */
  mapMerchantData(merchantObj, data) {
    if (data.name) merchantObj.name = data.name;
    if (data.type) merchantObj.type = data.type;
    if (data.contact) merchantObj.contact = { ...merchantObj.contact, ...data.contact };
    if (data.credentials) merchantObj.credentials = { ...merchantObj.credentials, ...data.credentials };
  }
  
  /**
   * 映射价格数据
   * @param {Object} pricingObj - 价格对象
   * @param {string} extractorId - 抽取器ID
   * @param {Object} data - 价格数据
   */
  mapPriceData(pricingObj, extractorId, data) {
    if (data.currency) pricingObj.currency = data.currency;
    
    if (extractorId.includes('wholesale')) {
      // 批发价格
      pricingObj.wholesale = {
        couponPrice: data.couponPrice ? {
          value: data.couponPrice.value,
          currency: data.couponPrice.currency,
          description: data.couponPrice.description
        } : null,
        priceRange: data.priceRange ? data.priceRange.map(price => ({
          value: price.value,
          currency: price.currency,
          type: price.type
        })) : [],
        minBatch: data.minBatch ? {
          quantity: data.minBatch.quantity,
          unit: data.minBatch.unit,
          description: data.minBatch.description
        } : null,
        display: data.display || {}
      };
    } else if (extractorId.includes('consign')) {
      // 代发价格
      pricingObj.consign = {
        price: data.price ? {
          value: data.price.value,
          currency: data.price.currency,
          displayText: data.price.displayText
        } : null,
        startText: data.startText,
        minOrder: data.minOrder ? {
          quantity: data.minOrder.quantity,
          unit: data.minOrder.unit,
          operator: data.minOrder.operator,
          description: data.minOrder.description
        } : null,
        display: data.display || {}
      };
    }
  }
  
  /**
   * 映射标题数据
   * @param {Object} productObj - 商品对象
   * @param {Object} data - 标题数据
   */
  mapTitleData(productObj, data) {
    productObj.title = {
      text: data.title || data.text,
      keywords: data.keywords || [],
      confidence: data.confidence || 0
    };
  }
  
  /**
   * 映射图片数据
   * @param {Object} productObj - 商品对象
   * @param {Object} data - 图片数据
   */
  mapImagesData(productObj, data) {
    if (data.images && Array.isArray(data.images)) {
      productObj.images = data.images.map(img => ({
        src: img.src,
        alt: img.alt || '',
        width: img.width || null,
        height: img.height || null,
        type: img.type || 'unknown',
        quality: img.quality || 'unknown'
      }));
    }
    
    if (data.mainImage) {
      productObj.mainImage = {
        src: data.mainImage.src,
        alt: data.mainImage.alt || '',
        width: data.mainImage.width || null,
        height: data.mainImage.height || null
      };
    }
  }
  
  /**
   * 映射评价数据
   * @param {Object} productObj - 商品对象
   * @param {Object} data - 评价数据
   */
  mapRatingData(productObj, data) {
    productObj.rating = {
      score: data.rating || null,
      stars: data.stars || null,
      reviewCount: data.reviewCount || null,
      salesCount: data.salesCount || null,
      favoriteCount: data.favoriteCount || null,
      viewCount: data.viewCount || null,
      positiveRate: data.positiveRate || null,
      display: data.display || {}
    };
  }
  
  /**
   * 映射规格数据
   * @param {Object} productObj - 商品对象
   * @param {Object} data - 规格数据
   */
  mapSpecsData(productObj, data) {
    productObj.specifications = {
      details: data.specifications || {},
      variants: data.variants || {},
      colors: data.colors || [],
      sizes: data.sizes || [],
      materials: data.materials || [],
      brand: data.brand || null,
      display: data.display || {}
    };
  }
  
  /**
   * 构建元数据
   * @param {Map} results - 抽取结果
   * @param {Object} pageInfo - 页面信息
   * @returns {Object} 元数据对象
   */
  buildMetadata(results, pageInfo) {
    return {
      extractor: {
        name: '1688商品信息提取器',
        version: '3.0.0',
        architecture: 'modular'
      },
      extraction: {
        startTime: pageInfo?.extractionStartTime || Date.now(),
        endTime: Date.now(),
        duration: pageInfo?.extractionDuration || 0,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      },
      quality: {
        averageConfidence: this.calculateAverageConfidence(results),
        dataCompleteness: this.calculateDataCompleteness(results),
        reliability: this.calculateReliability(results)
      }
    };
  }
  
  /**
   * 计算平均置信度
   * @param {Map} results - 抽取结果
   * @returns {number} 平均置信度
   */
  calculateAverageConfidence(results) {
    if (!results || results.size === 0) return 0;
    
    let totalConfidence = 0;
    let count = 0;
    
    for (const [, result] of results) {
      if (result.success && result.data?.confidence) {
        totalConfidence += result.data.confidence;
        count++;
      }
    }
    
    return count > 0 ? Math.round(totalConfidence / count) : 0;
  }
  
  /**
   * 计算数据完整性
   * @param {Map} results - 抽取结果
   * @returns {number} 数据完整性百分比
   */
  calculateDataCompleteness(results) {
    if (!results || results.size === 0) return 0;
    
    const successCount = Array.from(results.values()).filter(r => r.success).length;
    return Math.round((successCount / results.size) * 100);
  }
  
  /**
   * 计算可靠性
   * @param {Map} results - 抽取结果
   * @returns {string} 可靠性等级
   */
  calculateReliability(results) {
    const completeness = this.calculateDataCompleteness(results);
    const confidence = this.calculateAverageConfidence(results);
    
    const reliabilityScore = (completeness + confidence) / 2;
    
    if (reliabilityScore >= 80) return 'high';
    if (reliabilityScore >= 60) return 'medium';
    return 'low';
  }
  
  /**
   * 验证JSON数据
   * @param {Object} jsonData - JSON数据
   * @throws {Error} 验证失败时抛出错误
   */
  validateJSONData(jsonData) {
    // 检查必需字段
    const requiredFields = ['version', 'format', 'page', 'product', 'merchant', 'pricing', 'extraction'];
    
    for (const field of requiredFields) {
      if (!jsonData.hasOwnProperty(field)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    // 检查页面信息
    if (!jsonData.page.url || !jsonData.page.type) {
      throw new Error('Invalid page information');
    }
    
    // 检查数据格式
    try {
      JSON.stringify(jsonData);
    } catch (error) {
      throw new Error(`Invalid JSON format: ${error.message}`);
    }
  }
  
  /**
   * 导出到剪切板
   * @param {Object} jsonData - JSON数据
   * @param {Object} options - 导出选项
   * @returns {Promise<boolean>} 导出是否成功
   */
  async exportToClipboard(jsonData, options = {}) {
    try {
      this.logger.info('DataExportManager', 'Starting clipboard export');
      
      // 格式化JSON字符串
      const jsonString = this.formatJSONString(jsonData, options);
      
      // 首先尝试现代Clipboard API
      if (this.clipboardSupported) {
        try {
          await navigator.clipboard.writeText(jsonString);
          this.logger.info('DataExportManager', 'Data exported to clipboard successfully using modern API');
          return true;
        } catch (clipboardError) {
          this.logger.warn('DataExportManager', 'Modern clipboard API failed, trying fallback method', clipboardError);
        }
      }
      
      // 降级到传统方法
      const fallbackSuccess = this.fallbackClipboardExport(jsonData, options);
      if (fallbackSuccess) {
        return true;
      }
      
      // 如果所有方法都失败，提供用户友好的错误信息
      this.logger.warn('DataExportManager', 'All clipboard export methods failed');
      return false;
      
    } catch (error) {
      this.logger.error('DataExportManager', 'Clipboard export failed completely', error);
      return false;
    }
  }
  
  /**
   * 格式化JSON字符串
   * @param {Object} jsonData - JSON数据
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化的JSON字符串
   */
  formatJSONString(jsonData, options = {}) {
    const indent = options.compress ? 0 : (options.indent || this.config.jsonIndent);
    return JSON.stringify(jsonData, null, indent);
  }
  
  /**
   * 降级剪切板导出方法
   * @param {Object} jsonData - JSON数据
   * @param {Object} options - 导出选项
   * @returns {boolean} 导出是否成功
   */
  fallbackClipboardExport(jsonData, options = {}) {
    try {
      // 创建临时文本区域
      const textArea = document.createElement('textarea');
      textArea.value = this.formatJSONString(jsonData, options);
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      
      document.body.appendChild(textArea);
      textArea.select();
      
      // 执行复制命令
      const success = document.execCommand('copy');
      
      document.body.removeChild(textArea);
      
      if (success) {
        this.logger.info('DataExportManager', 'Data exported to clipboard using fallback method');
      } else {
        this.logger.warn('DataExportManager', 'Fallback clipboard export failed');
      }
      
      return success;
      
    } catch (error) {
      this.logger.error('DataExportManager', 'Fallback clipboard export error', error);
      return false;
    }
  }
  
  /**
   * 导出到文件
   * @param {Object} jsonData - JSON数据
   * @param {string} filename - 文件名
   * @param {Object} options - 导出选项
   */
  exportToFile(jsonData, filename, options = {}) {
    try {
      this.logger.info('DataExportManager', `Starting file export: ${filename}`);
      
      // 格式化JSON字符串
      const jsonString = this.formatJSONString(jsonData, options);
      
      // 创建Blob对象
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // 创建下载链接
      const a = document.createElement('a');
      a.href = url;
      a.download = filename || `1688_extraction_${Date.now()}.json`;
      
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      URL.revokeObjectURL(url);
      
      this.logger.info('DataExportManager', 'File export completed successfully');
      
    } catch (error) {
      this.logger.error('DataExportManager', 'Failed to export to file', error);
      throw error;
    }
  }
  
  /**
   * 记录导出历史
   * @param {Object} jsonData - JSON数据
   */
  recordExportHistory(jsonData) {
    const historyEntry = {
      timestamp: Date.now(),
      url: jsonData.page.url,
      mode: jsonData.page.mode,
      successCount: jsonData.extraction.successCount,
      totalModules: jsonData.extraction.totalModules,
      dataSize: JSON.stringify(jsonData).length
    };
    
    this.exportHistory.unshift(historyEntry);
    
    // 限制历史记录数量
    if (this.exportHistory.length > this.maxHistorySize) {
      this.exportHistory = this.exportHistory.slice(0, this.maxHistorySize);
    }
  }
  
  /**
   * 获取导出历史
   * @returns {Array} 导出历史列表
   */
  getExportHistory() {
    return [...this.exportHistory];
  }
  
  /**
   * 清除导出历史
   */
  clearExportHistory() {
    this.exportHistory = [];
    this.logger.info('DataExportManager', 'Export history cleared');
  }
  
  /**
   * 获取导出统计
   * @returns {Object} 导出统计信息
   */
  getExportStats() {
    const stats = {
      totalExports: this.exportHistory.length,
      wholesaleExports: 0,
      consignExports: 0,
      averageSuccessRate: 0,
      averageDataSize: 0
    };
    
    if (this.exportHistory.length > 0) {
      let totalSuccessRate = 0;
      let totalDataSize = 0;
      
      for (const entry of this.exportHistory) {
        if (entry.mode === 'wholesale') stats.wholesaleExports++;
        if (entry.mode === 'consign') stats.consignExports++;
        
        totalSuccessRate += (entry.successCount / entry.totalModules) * 100;
        totalDataSize += entry.dataSize;
      }
      
      stats.averageSuccessRate = Math.round(totalSuccessRate / this.exportHistory.length);
      stats.averageDataSize = Math.round(totalDataSize / this.exportHistory.length);
    }
    
    return stats;
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.DataExportManager = DataExportManager;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DataExportManager;
}