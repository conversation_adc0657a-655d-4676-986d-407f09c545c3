<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1688商品详情页面 - 测试模拟</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; }
        .product-header { border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 20px; }
        .product-title { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .product-price { font-size: 20px; color: #ff6600; margin-bottom: 10px; }
        .merchant-info { background: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .shop-company-name h1 { font-size: 18px; color: #333; margin: 0; }
        .od-collapse-module { border: 1px solid #ddd; margin-bottom: 15px; border-radius: 5px; }
        .od-collapse-title { background: #f5f5f5; padding: 10px; font-weight: bold; }
        .od-collapse-body { padding: 15px; }
        table { width: 100%; border-collapse: collapse; }
        table th, table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        table th { background: #f5f5f5; }
        .debug-panel { position: fixed; top: 10px; right: 10px; width: 300px; background: white; border: 1px solid #ccc; padding: 10px; font-size: 12px; max-height: 400px; overflow-y: auto; z-index: 9999; }
        .debug-panel h3 { margin: 0 0 10px 0; font-size: 14px; }
        .debug-log { margin-bottom: 5px; padding: 3px; border-left: 3px solid #ccc; }
        .debug-log.success { border-color: #4CAF50; background: #f1f8e9; }
        .debug-log.error { border-color: #f44336; background: #ffebee; }
        .debug-log.warning { border-color: #ff9800; background: #fff3e0; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 商品标题区域 -->
        <div class="product-header">
            <h1 class="product-title">
                【批发】优质棉质T恤 夏季新款 男女同款 多色可选 厂家直销
            </h1>
            <div class="product-price">
                ¥15.80 - ¥28.50 / 件
                <span style="color: #666; font-size: 14px;">起批量：10件</span>
            </div>
        </div>

        <!-- 商家信息区域 -->
        <div class="merchant-info">
            <div class="shop-company-name">
                <h1>广州市优质服装有限公司</h1>
            </div>
            <div class="merchant-details">
                <p>主营产品：服装、纺织品、日用品</p>
                <p>经营模式：生产厂家</p>
                <p>所在地区：广东 广州</p>
            </div>
        </div>

        <!-- 商品属性区域 -->
        <div class="od-collapse-module">
            <div class="od-collapse-title">商品属性</div>
            <div class="od-collapse-body">
                <table>
                    <tr>
                        <th>属性名称</th>
                        <th>属性值</th>
                        <th>属性名称</th>
                        <th>属性值</th>
                    </tr>
                    <tr>
                        <td>材质</td>
                        <td>100%纯棉</td>
                        <td>颜色</td>
                        <td>白色、黑色、灰色、蓝色</td>
                    </tr>
                    <tr>
                        <td>尺寸</td>
                        <td>S、M、L、XL、XXL</td>
                        <td>季节</td>
                        <td>夏季</td>
                    </tr>
                    <tr>
                        <td>风格</td>
                        <td>休闲</td>
                        <td>适用性别</td>
                        <td>男女通用</td>
                    </tr>
                    <tr>
                        <td>品牌</td>
                        <td>优质服装</td>
                        <td>产地</td>
                        <td>广东</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 规格参数区域 -->
        <div class="od-collapse-module">
            <div class="od-collapse-title">规格参数</div>
            <div class="od-collapse-body">
                <table>
                    <tr>
                        <th>参数名称</th>
                        <th>参数值</th>
                    </tr>
                    <tr>
                        <td>重量</td>
                        <td>约200g</td>
                    </tr>
                    <tr>
                        <td>包装</td>
                        <td>单件装</td>
                    </tr>
                    <tr>
                        <td>保质期</td>
                        <td>长期</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
        <h3>扩展调试信息</h3>
        <div id="debugLogs"></div>
        <button onclick="clearDebugLogs()">清空日志</button>
        <button onclick="testExtraction()">手动测试提取</button>
    </div>

    <script>
        // 模拟1688页面的一些全局变量
        window.location.href = 'https://detail.1688.com/offer/772460660597.html';
        
        // 调试日志系统
        const debugLogs = document.getElementById('debugLogs');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function addDebugLog(message, type = 'info') {
            const logElement = document.createElement('div');
            logElement.className = `debug-log ${type}`;
            logElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            debugLogs.appendChild(logElement);
            debugLogs.scrollTop = debugLogs.scrollHeight;
        }

        // 拦截控制台输出
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addDebugLog(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addDebugLog(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addDebugLog(args.join(' '), 'warning');
        };

        function clearDebugLogs() {
            debugLogs.innerHTML = '';
        }

        function testExtraction() {
            addDebugLog('开始手动测试提取...', 'info');
            
            // 测试商品标题提取
            const titleElement = document.querySelector('.product-title');
            if (titleElement) {
                addDebugLog(`商品标题: ${titleElement.textContent}`, 'success');
            } else {
                addDebugLog('未找到商品标题元素', 'error');
            }

            // 测试商家信息提取
            const merchantElement = document.querySelector('.shop-company-name h1');
            if (merchantElement) {
                addDebugLog(`商家名称: ${merchantElement.textContent}`, 'success');
            } else {
                addDebugLog('未找到商家名称元素', 'error');
            }

            // 测试商品属性提取
            const specsContainer = document.querySelector('.od-collapse-module table');
            if (specsContainer) {
                const rows = specsContainer.querySelectorAll('tr');
                addDebugLog(`商品属性表格: 找到 ${rows.length} 行数据`, 'success');
            } else {
                addDebugLog('未找到商品属性表格', 'error');
            }

            // 检查扩展是否加载
            if (window.extractionManager) {
                addDebugLog('提取管理器已加载', 'success');
            } else {
                addDebugLog('提取管理器未加载', 'warning');
            }

            if (window.UIManager) {
                addDebugLog('UI管理器已加载', 'success');
            } else {
                addDebugLog('UI管理器未加载', 'warning');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addDebugLog('测试页面加载完成', 'success');
            addDebugLog('等待扩展加载...', 'info');
            
            // 延迟检查扩展状态
            setTimeout(() => {
                testExtraction();
            }, 2000);
        });
    </script>
</body>
</html>
