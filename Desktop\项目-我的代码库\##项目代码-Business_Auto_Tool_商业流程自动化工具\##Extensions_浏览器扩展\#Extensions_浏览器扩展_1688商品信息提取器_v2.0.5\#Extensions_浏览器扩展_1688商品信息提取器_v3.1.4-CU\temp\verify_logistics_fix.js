/**
 * 物流信息显示修复验证脚本
 * 在浏览器控制台中运行此脚本来验证修复是否生效
 */

console.log('🔧 [验证] 开始验证物流信息显示修复...');

// 检查必要的类是否存在
function checkRequiredClasses() {
  const checks = {
    ResultFormatter: typeof ResultFormatter !== 'undefined',
    WholesaleLogisticsExtractor: typeof WholesaleLogisticsExtractor !== 'undefined',
    ConsignLogisticsExtractor: typeof ConsignLogisticsExtractor !== 'undefined'
  };
  
  console.log('📋 [验证] 必要类检查:', checks);
  
  return Object.values(checks).every(check => check);
}

// 模拟物流数据进行测试
function testLogisticsFormatting() {
  if (!checkRequiredClasses()) {
    console.error('❌ [验证失败] 缺少必要的类，请确保扩展已正确加载');
    return false;
  }
  
  const formatter = new ResultFormatter();
  
  // 测试数据
  const testCases = [
    {
      name: '批发物流信息',
      extractorId: 'extractor_wholesale_logistics',
      data: {
        success: true,
        data: {
          originCity: '浙江金华',
          destinationCity: '北京朝阳',
          shippingFee: '运费¥4起',
          deliveryPromise: '承诺48小时发货',
          isFreeship: false,
          rawData: { test: 'data' }
        },
        confidence: 85
      },
      pageType: 'wholesale'
    },
    {
      name: '代发物流信息',
      extractorId: 'extractor_consign_logistics',
      data: {
        success: true,
        data: {
          originCity: '广东东莞',
          destinationCity: '上海浦东',
          shippingFee: '免费配送',
          deliveryPromise: '24小时发货',
          isFreeship: true,
          deliveryTime: '3-5个工作日',
          rawData: { test: 'data' }
        },
        confidence: 90
      },
      pageType: 'consign'
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n🧪 [测试 ${index + 1}] ${testCase.name}`);
    
    try {
      const result = formatter.formatResultItem(
        testCase.extractorId,
        testCase.data,
        testCase.pageType
      );
      
      console.log('格式化结果:', result);
      
      // 检查是否包含"详细信息"
      const hasGenericText = result.includes('详细信息');
      
      // 检查是否包含具体信息
      const hasOriginCity = result.includes(testCase.data.data.originCity);
      const hasDestinationCity = result.includes(testCase.data.data.destinationCity);
      const hasShippingInfo = result.includes('运费') || result.includes('包邮') || result.includes('免费');
      
      if (hasGenericText) {
        console.error(`❌ [测试失败] ${testCase.name} 仍然显示"详细信息"`);
        allTestsPassed = false;
      } else {
        console.log(`✅ [测试通过] ${testCase.name} 不再显示"详细信息"`);
      }
      
      if (hasOriginCity && hasDestinationCity) {
        console.log(`✅ [测试通过] ${testCase.name} 包含具体的物流路线信息`);
      } else {
        console.error(`❌ [测试失败] ${testCase.name} 缺少物流路线信息`);
        allTestsPassed = false;
      }
      
      if (hasShippingInfo) {
        console.log(`✅ [测试通过] ${testCase.name} 包含运费信息`);
      } else {
        console.error(`❌ [测试失败] ${testCase.name} 缺少运费信息`);
        allTestsPassed = false;
      }
      
    } catch (error) {
      console.error(`❌ [测试异常] ${testCase.name}:`, error);
      allTestsPassed = false;
    }
  });
  
  return allTestsPassed;
}

// 检查当前页面的物流信息显示
function checkCurrentPageLogistics() {
  console.log('\n🔍 [验证] 检查当前页面的物流信息显示...');
  
  // 查找结果面板
  const resultPanel = document.querySelector('.extraction-results');
  if (!resultPanel) {
    console.warn('⚠️ [验证] 未找到结果面板，可能需要先运行提取');
    return;
  }
  
  // 查找物流相关的结果项
  const logisticsItems = resultPanel.querySelectorAll('.result-item');
  let foundLogisticsInfo = false;
  
  logisticsItems.forEach((item, index) => {
    const nameElement = item.querySelector('.result-name');
    const contentElement = item.querySelector('.result-content');
    
    if (nameElement && nameElement.textContent.includes('logistics')) {
      foundLogisticsInfo = true;
      console.log(`📦 [验证] 找到物流信息项 ${index + 1}:`);
      console.log('  名称:', nameElement.textContent);
      console.log('  内容:', contentElement ? contentElement.textContent : '无内容');
      
      // 检查是否还显示"详细信息"
      if (contentElement && contentElement.textContent.includes('详细信息')) {
        console.error('❌ [验证失败] 物流信息仍然显示"详细信息"');
      } else {
        console.log('✅ [验证通过] 物流信息显示正常');
      }
    }
  });
  
  if (!foundLogisticsInfo) {
    console.warn('⚠️ [验证] 当前页面未找到物流信息，可能需要先运行提取');
  }
}

// 主验证函数
function verifyLogisticsFix() {
  console.log('🚀 [验证开始] 物流信息显示修复验证');
  
  const formatTestPassed = testLogisticsFormatting();
  checkCurrentPageLogistics();
  
  console.log('\n📊 [验证结果]');
  if (formatTestPassed) {
    console.log('✅ 格式化测试通过 - 物流信息不再显示"详细信息"');
    console.log('✅ 修复生效 - 用户现在可以看到具体的物流信息');
  } else {
    console.error('❌ 格式化测试失败 - 修复可能未完全生效');
  }
  
  console.log('\n💡 [使用建议]');
  console.log('1. 如果测试通过，请在1688页面上运行提取来验证实际效果');
  console.log('2. 如果仍有问题，请检查浏览器控制台的错误信息');
  console.log('3. 确保扩展已重新加载并且所有脚本都已正确加载');
  
  return formatTestPassed;
}

// 自动运行验证
verifyLogisticsFix();
