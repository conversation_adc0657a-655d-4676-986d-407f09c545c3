/**
 * 物流信息显示修复测试
 * 测试结果格式化器是否能正确处理物流信息
 */

// 模拟物流数据
const testLogisticsData = {
  // 批发物流数据
  wholesaleLogistics: {
    success: true,
    data: {
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起',
      deliveryPromise: '承诺48小时发货',
      isFreeship: false,
      logisticsMethod: '快递配送',
      rawData: {
        containerHTML: '<div>...</div>',
        containerText: '物流信息文本'
      }
    },
    confidence: 85
  },
  
  // 代发物流数据
  consignLogistics: {
    success: true,
    data: {
      originCity: '广东东莞',
      destinationCity: '上海浦东',
      shippingFee: '免费配送',
      deliveryPromise: '24小时发货',
      isFreeship: true,
      deliveryTime: '3-5个工作日',
      rawData: {
        containerHTML: '<div>...</div>',
        containerText: '代发物流信息'
      }
    },
    confidence: 90
  }
};

// 测试函数
function testLogisticsDisplayFix() {
  console.log('🧪 [测试] 开始测试物流信息显示修复...');
  
  // 检查ResultFormatter是否存在
  if (typeof ResultFormatter === 'undefined') {
    console.error('❌ ResultFormatter 未定义，请确保已加载');
    return;
  }
  
  const formatter = new ResultFormatter();
  
  // 测试批发物流信息格式化
  console.log('📦 [测试] 测试批发物流信息格式化...');
  const wholesaleResult = formatter.formatResultItem(
    'extractor_wholesale_logistics',
    testLogisticsData.wholesaleLogistics,
    'wholesale'
  );
  console.log('批发物流格式化结果:', wholesaleResult);
  
  // 测试代发物流信息格式化
  console.log('🚚 [测试] 测试代发物流信息格式化...');
  const consignResult = formatter.formatResultItem(
    'extractor_consign_logistics',
    testLogisticsData.consignLogistics,
    'consign'
  );
  console.log('代发物流格式化结果:', consignResult);
  
  // 检查是否包含"详细信息"
  const hasGenericText = wholesaleResult.includes('详细信息') || consignResult.includes('详细信息');
  
  if (hasGenericText) {
    console.error('❌ [测试失败] 仍然显示"详细信息"，修复未生效');
  } else {
    console.log('✅ [测试成功] 物流信息正确显示，不再显示"详细信息"');
  }
  
  // 检查是否包含具体的物流信息
  const hasSpecificInfo = wholesaleResult.includes('浙江金华') && 
                          wholesaleResult.includes('北京朝阳') &&
                          consignResult.includes('广东东莞') &&
                          consignResult.includes('上海浦东');
  
  if (hasSpecificInfo) {
    console.log('✅ [测试成功] 包含具体的物流路线信息');
  } else {
    console.error('❌ [测试失败] 缺少具体的物流路线信息');
  }
  
  return {
    wholesaleResult,
    consignResult,
    hasGenericText,
    hasSpecificInfo
  };
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  // 等待DOM加载完成后运行测试
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testLogisticsDisplayFix);
  } else {
    setTimeout(testLogisticsDisplayFix, 1000);
  }
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testLogisticsDisplayFix, testLogisticsData };
}
