/**
 * 批发价格抽取器 - 专门处理批发模式的复杂价格结构
 * 支持券后价格、价格区间、起批量等批发特有的价格信息
 * 目标元素: #mainPrice .module-od-main-price 等批发价格相关元素
 * <AUTHOR>
 * @version 1.0.0
 */

class WholesalePriceExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_wholesale_price_001',
      '批发价格信息',
      '提取1688批发模式页面的复杂价格信息，包括券后价格、价格区间、起批量等'
    );
    
    // 抽取器配置
    this.config = {
      timeout: 10000, // 10秒超时
      retryDelay: 2000, // 重试延迟2秒
      maxPriceRanges: 5, // 最大价格区间数
      minPriceValue: 0.01, // 最小价格值
      maxPriceValue: 999999 // 最大价格值
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 主要选择器 - 批发模式特有
      primary: {
        // 主价格容器
        mainPriceContainer: [
          '#mainPrice[data-module="od_main_price"]', // 主价格模块
          '.module-od-main-price', // 主价格模块类
          '[data-spm="mainPrice"]' // SPM标识的主价格
        ],
        
        // 券后价格（批发特有）
        couponPrice: [
          '.price-component.onhand-price .price-info.currency', // 券后价格信息
          '.onhand-price .price-comp .price-info', // 券后价格组件
          '.price-component:has(.onhand-price-text) .price-info' // 包含到手价文本的价格
        ],
        
        // 价格区间（批发特有）
        priceRange: [
          '.price-component.range-price .price-comp .price-info.currency', // 价格区间信息
          '.price-component.range-price .price-info.currency', // 区间价格货币信息
          '.range-price .price-info.currency', // 区间价格货币信息
          '.price-component:has(.price-tag) .price-info' // 包含价格标签的价格信息
        ],
        
        // 起批量信息
        minBatch: [
          '.price-component p', // 价格组件中的段落
          '.range-price p', // 价格区间中的段落
          'p:contains("起批")', // 包含起批的段落
          'p:contains("个起批")', // 包含个起批的段落
          'p:contains("件起批")', // 包含件起批的段落
          '.price-component p[style*="color"]' // 有颜色样式的起批信息
        ],
        
        // 价格标签
        priceTag: [
          '.price-tag od-text[i18n="price"]', // 国际化价格标签
          '.price-tag', // 价格标签
          'span:contains("价格")' // 包含价格文本的span
        ],
        
        // 波浪符号（价格区间连接符）
        waveSymbol: [
          '.text-wave', // 波浪文本
          'span:contains("~")', // 包含波浪符号的span
          '.price-range-separator' // 价格区间分隔符
        ]
      },
      
      // 备用选择器
      fallback: {
        // 通用价格选择器
        anyPrice: [
          '.price-info span:nth-child(2)', // 价格信息第二个span（通常是整数部分）
          '.currency span:not(:first-child)', // 货币符号后的span
          '[class*="price"] strong', // 包含price类名的强调文本
          '.price-comp span' // 价格组件中的span
        ],
        
        // 通用货币符号
        currency: [
          '.price-info span:first-child', // 价格信息第一个span（通常是货币符号）
          '.currency span:first-child', // 货币类第一个span
          'span:contains("¥")', // 包含人民币符号
          '.price-unit' // 价格单位
        ]
      },
      
      // 上下文选择器
      context: [
        '#mainPrice', // 主价格ID
        '.module-od-main-price', // 主价格模块
        '.cart-gap', // 购物车间隙（通常包含价格）
        '[data-module="od_main_price"]' // 价格数据模块
      ],
      
      // 排除选择器
      exclude: [
        '.advertisement',
        '.popup',
        '.modal',
        '.tooltip',
        'script',
        'style',
        'noscript'
      ]
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的批发价格信息
   */
  async extract() {
    console.log('🚀 [价格调试] ========== 开始批发价格提取 ==========');
    console.log('🚀 [价格调试] 提取器ID:', this.moduleId);
    console.log('🚀 [价格调试] 当前页面URL:', window.location.href);
    
    const selectors = this.getSelectors();
    let priceInfo = null;
    
    try {
      // 第一步：检查是否为批发模式页面
      console.log('🔍 [价格调试] 检查页面类型...');
      if (!this.isWholesalePage()) {
        console.log('❌ [价格调试] 当前页面不是批发模式');
        throw new Error('当前页面不是批发模式');
      }
      console.log('✅ [价格调试] 确认为批发模式页面');
      
      // 第二步：提取主价格容器
      console.log('🔍 [价格调试] 查找主价格容器...');
      console.log('🔍 [价格调试] 价格容器选择器:', JSON.stringify(selectors.primary.mainPriceContainer));
      const mainContainer = await this.findMainPriceContainer(selectors.primary.mainPriceContainer);
      if (!mainContainer) {
        console.log('❌ [价格调试] 未找到主价格容器');
        throw new Error('未找到主价格容器');
      }
      console.log('✅ [价格调试] 找到价格容器:', mainContainer);
      console.log('✅ [价格调试] 容器HTML:', mainContainer.outerHTML.substring(0, 300) + '...');
      
      // 记录DOM数据到调试面板
      if (window.debugPanel) {
        window.debugPanel.addDOMData('价格信息提取器', '价格容器', mainContainer);
      }
      
      // 第三步：提取批发价格信息
      console.log('🔄 [价格调试] 开始提取价格信息...');
      priceInfo = await this.extractWholesalePriceInfo(mainContainer, selectors);
      console.log('🔄 [价格调试] 原始价格信息:', JSON.stringify(priceInfo, null, 2));
      
      if (!priceInfo) {
        console.log('❌ [价格调试] 未能提取到批发价格信息');
        throw new Error('未能提取到批发价格信息');
      }
      
      // 数据清理和增强
      console.log('🔄 [价格调试] 开始数据增强处理...');
      const enhancedInfo = this.enhanceWholesalePriceInfo(priceInfo);
      
      console.log('✅ [价格调试] 最终价格数据:', JSON.stringify(enhancedInfo, null, 2));
       
       // 记录提取结果到调试面板
       if (window.debugPanel) {
         window.debugPanel.addResult('价格信息提取器', enhancedInfo);
       }
       
       console.log('🎉 [价格调试] ========== 批发价格提取完成 ==========');
       
       return enhancedInfo;
      
    } catch (error) {
      console.error('❌ [价格调试] 批发价格提取失败:', error);
      console.log('🎉 [价格调试] ========== 批发价格提取完成 ==========');
      throw error;
    }
  }

  /**
   * 检查是否为批发模式页面
   * @returns {boolean} 是否为批发模式
   */
  isWholesalePage() {
    const url = window.location.href;
    
    // 首先检查URL中是否包含代发标识
    if (url.includes('sk=consign')) {
      return false;
    }
    
    // 检查是否存在代发特有元素（如果存在则不是批发模式）
    const consignIndicators = [
      '.od-fx-price-pc-price-wrapper',
      '.od-fx-price-pc',
      '[class*="od-fx-price"]'
    ];
    
    const hasConsignElements = consignIndicators.some(selector => {
      return this.safeQuerySelector(selector) !== null;
    });
    
    if (hasConsignElements) {
      return false;
    }
    
    // 检查页面中是否存在批发特有元素
    const wholesaleIndicators = [
      '#mainPrice[data-module="od_main_price"]',
      '.module-od-main-price',
      '.price-component.range-price'
    ];
    
    return wholesaleIndicators.some(selector => {
      return this.safeQuerySelector(selector) !== null;
    });
  }

  /**
   * 查找主价格容器
   * @param {Array} selectors - 主价格容器选择器列表
   * @returns {Promise<Element|null>} 主价格容器元素
   */
  async findMainPriceContainer(selectors) {
    for (const selector of selectors) {
      const element = this.safeQuerySelector(selector);
      if (element && this.isValidPriceContainer(element)) {
        return element;
      }
    }
    return null;
  }

  /**
   * 验证价格容器是否有效
   * @param {Element} container - 容器元素
   * @returns {boolean} 是否有效
   */
  isValidPriceContainer(container) {
    if (!container) return false;
    
    // 检查容器是否包含价格相关内容
    const priceIndicators = [
      '.price-info',
      '.currency',
      '.price-comp',
      'span:contains("¥")',
      'span:contains("价格")'
    ];
    
    return priceIndicators.some(indicator => {
      return this.safeQuerySelector(indicator, container) !== null;
    });
  }

  /**
   * 提取批发价格信息
   * @param {Element} container - 主价格容器
   * @param {Object} selectors - 选择器配置
   * @returns {Promise<Object>} 批发价格信息
   */
  async extractWholesalePriceInfo(container, selectors) {
    const priceInfo = {
      couponPrice: null, // 券后价格
      priceRange: [], // 价格区间
      minBatch: null, // 起批量
      priceTag: null, // 价格标签
      currency: '¥', // 货币符号
      extractionMethod: 'wholesale_primary'
    };
    
    try {
      // 提取券后价格
      try {
        priceInfo.couponPrice = await this.extractCouponPrice(container, selectors.primary.couponPrice);
      } catch (error) {
        console.warn('提取券后价格失败:', error);
      }
      
      // 提取价格区间
      try {
        priceInfo.priceRange = await this.extractPriceRange(container, selectors.primary.priceRange);
      } catch (error) {
        console.warn('提取价格区间失败:', error);
      }
      
      // 提取起批量
      try {
        priceInfo.minBatch = await this.extractMinBatch(container, selectors.primary.minBatch);
      } catch (error) {
        console.warn('提取起批量失败:', error);
      }
      
      // 提取价格标签
      try {
        priceInfo.priceTag = await this.extractPriceTag(container, selectors.primary.priceTag);
      } catch (error) {
        console.warn('提取价格标签失败:', error);
      }
      
      // 提取货币符号
      try {
        priceInfo.currency = await this.extractCurrency(container) || '¥';
      } catch (error) {
        console.warn('提取货币符号失败:', error);
        priceInfo.currency = '¥';
      }
      
      // 检查是否至少提取到了一些价格信息
      if (!priceInfo.couponPrice && (!priceInfo.priceRange || priceInfo.priceRange.length === 0)) {
        console.warn('未能提取到任何价格信息');
        return null;
      }
      
      return priceInfo;
      
    } catch (error) {
      console.error('提取批发价格信息时出错:', error);
      return null;
    }
  }

  /**
   * 提取券后价格
   * @param {Element} container - 容器元素
   * @param {Array} selectors - 券后价格选择器
   * @returns {Promise<Object|null>} 券后价格信息
   */
  async extractCouponPrice(container, selectors) {
    for (const selector of selectors) {
      const priceElements = this.safeQuerySelectorAll(selector, container);
      
      for (const element of priceElements) {
        // 检查是否在券后价格组件中
        const parentComponent = element.closest('.price-component.onhand-price');
        if (parentComponent) {
          const priceData = this.extractPriceFromElement(element);
          if (priceData) {
            return {
              ...priceData,
              type: 'coupon',
              description: '券后价格',
              element: element
            };
          }
        }
      }
    }
    return null;
  }

  /**
   * 提取价格区间
   * @param {Element} container - 容器元素
   * @param {Array} selectors - 价格区间选择器
   * @returns {Promise<Array>} 价格区间列表
   */
  async extractPriceRange(container, selectors) {
    const priceRange = [];
    
    try {
      // 首先尝试查找价格区间组件
      const rangeComponent = this.safeQuerySelector('.price-component.range-price', container);
      if (rangeComponent) {
        // 提取所有价格信息元素
        const priceElements = this.safeQuerySelectorAll('.price-info.currency', rangeComponent);
        
        for (const element of priceElements) {
          const priceData = this.extractPriceFromElement(element);
          if (priceData) {
            priceRange.push({
              ...priceData,
              type: 'range',
              element: element
            });
          }
        }
      }
      
      // 如果没有找到价格，尝试备用选择器
      if (priceRange.length === 0) {
        for (const selector of selectors) {
          const elements = this.safeQuerySelectorAll(selector, container);
          for (const element of elements) {
            const priceData = this.extractPriceFromElement(element);
            if (priceData) {
              priceRange.push({
                ...priceData,
                type: 'range',
                element: element
              });
            }
          }
          if (priceRange.length > 0) break;
        }
      }
      
      // 如果仍然没有找到价格，尝试直接在整个页面查找
      if (priceRange.length === 0) {
        const globalPriceElements = document.querySelectorAll('.price-component.range-price .price-info.currency');
        for (const element of globalPriceElements) {
          const priceData = this.extractPriceFromElement(element);
          if (priceData) {
            priceRange.push({
              ...priceData,
              type: 'range',
              element: element
            });
          }
        }
      }
      
    } catch (error) {
      console.warn('提取价格区间时出错:', error);
    }
    
    return priceRange;
  }

  /**
   * 提取起批量信息
   * @param {Element} container - 容器元素
   * @param {Array} selectors - 起批量选择器
   * @returns {Promise<Object|null>} 起批量信息
   */
  async extractMinBatch(container, selectors) {
    for (const selector of selectors) {
      const elements = this.safeQuerySelectorAll(selector, container);
      
      for (const element of elements) {
        const text = this.getElementText(element);
        if (text && text.includes('起批')) {
          const batchInfo = this.parseMinBatchText(text);
          if (batchInfo) {
            return {
              ...batchInfo,
              originalText: text,
              element: element
            };
          }
        }
      }
    }
    return null;
  }

  /**
   * 提取价格标签
   * @param {Element} container - 容器元素
   * @param {Array} selectors - 价格标签选择器
   * @returns {Promise<string|null>} 价格标签
   */
  async extractPriceTag(container, selectors) {
    for (const selector of selectors) {
      const element = this.safeQuerySelector(selector, container);
      if (element) {
        const text = this.getElementText(element);
        if (text) {
          return text.trim();
        }
      }
    }
    return null;
  }

  /**
   * 提取货币符号
   * @param {Element} container - 容器元素
   * @returns {Promise<string>} 货币符号
   */
  async extractCurrency(container) {
    const currencySelectors = [
      '.price-info.currency span:first-child',
      '.currency span:first-child',
      'span:contains("¥")',
      '.price-unit'
    ];
    
    for (const selector of currencySelectors) {
      const element = this.safeQuerySelector(selector, container);
      if (element) {
        const text = this.getElementText(element);
        if (text && /[¥$€£]/.test(text)) {
          return text.trim();
        }
      }
    }
    
    return '¥'; // 默认人民币
  }

  /**
   * 从元素中提取价格数据
   * @param {Element} element - 价格元素
   * @returns {Object|null} 价格数据
   */
  extractPriceFromElement(element) {
    if (!element) return null;
    
    const spans = element.querySelectorAll('span');
    if (spans.length < 2) return null;
    
    const currency = spans[0].textContent.trim();
    const integerPart = spans[1].textContent.trim();
    const decimalPart = spans[2] ? spans[2].textContent.trim() : '.00';
    
    // 组合完整价格
    const fullPriceText = integerPart + decimalPart;
    const priceValue = parseFloat(fullPriceText.replace(/[^\d.]/g, ''));
    
    if (isNaN(priceValue) || priceValue < this.config.minPriceValue || priceValue > this.config.maxPriceValue) {
      return null;
    }
    
    return {
      currency: currency,
      value: priceValue,
      integerPart: integerPart,
      decimalPart: decimalPart,
      displayText: currency + fullPriceText
    };
  }

  /**
   * 解析起批量文本
   * @param {string} text - 起批量文本
   * @returns {Object|null} 起批量信息
   */
  parseMinBatchText(text) {
    if (!text) return null;
    
    // 匹配数字和单位
    const match = text.match(/(\d+)(个|件|批)?起批/);
    if (match) {
      return {
        quantity: parseInt(match[1]),
        unit: match[2] || '个',
        description: `${match[1]}${match[2] || '个'}起批`
      };
    }
    
    return null;
  }

  /**
   * 增强批发价格信息
   * @param {Object} priceInfo - 原始价格信息
   * @returns {Object} 增强后的价格信息
   */
  enhanceWholesalePriceInfo(priceInfo) {
    const enhanced = { ...priceInfo };
    
    // 计算价格统计
    enhanced.statistics = this.calculatePriceStatistics(priceInfo);
    
    // 分析价格特征
    enhanced.analysis = this.analyzePriceFeatures(priceInfo);
    
    // 计算可信度
    enhanced.confidence = this.calculateConfidence(priceInfo);
    
    // 格式化显示
    enhanced.display = this.formatPriceDisplay(priceInfo);
    
    // 添加元数据
    enhanced.metadata = {
      extractorType: 'wholesale_price',
      pageType: 'wholesale',
      extractionTime: Date.now(),
      hasComplexPricing: this.hasComplexPricing(priceInfo)
    };
    
    return enhanced;
  }

  /**
   * 计算价格统计
   * @param {Object} priceInfo - 价格信息
   * @returns {Object} 价格统计
   */
  calculatePriceStatistics(priceInfo) {
    const stats = {
      hasCouponPrice: !!priceInfo.couponPrice,
      priceRangeCount: priceInfo.priceRange.length,
      hasMinBatch: !!priceInfo.minBatch,
      lowestPrice: null,
      highestPrice: null,
      averagePrice: null
    };
    
    // 收集所有价格值
    const allPrices = [];
    
    if (priceInfo.couponPrice) {
      allPrices.push(priceInfo.couponPrice.value);
    }
    
    priceInfo.priceRange.forEach(price => {
      if (price.value) {
        allPrices.push(price.value);
      }
    });
    
    if (allPrices.length > 0) {
      stats.lowestPrice = Math.min(...allPrices);
      stats.highestPrice = Math.max(...allPrices);
      stats.averagePrice = allPrices.reduce((sum, price) => sum + price, 0) / allPrices.length;
    }
    
    return stats;
  }

  /**
   * 分析价格特征
   * @param {Object} priceInfo - 价格信息
   * @returns {Object} 价格特征分析
   */
  analyzePriceFeatures(priceInfo) {
    return {
      pricingModel: 'wholesale',
      hasCouponDiscount: !!priceInfo.couponPrice,
      hasVolumeDiscount: priceInfo.priceRange.length > 1,
      requiresMinimumOrder: !!priceInfo.minBatch,
      complexityLevel: this.calculateComplexityLevel(priceInfo)
    };
  }

  /**
   * 计算复杂度级别
   * @param {Object} priceInfo - 价格信息
   * @returns {string} 复杂度级别
   */
  calculateComplexityLevel(priceInfo) {
    let complexity = 0;
    
    if (priceInfo.couponPrice) complexity += 2;
    if (priceInfo.priceRange.length > 1) complexity += 2;
    if (priceInfo.minBatch) complexity += 1;
    
    if (complexity >= 4) return 'high';
    if (complexity >= 2) return 'medium';
    return 'low';
  }

  /**
   * 检查是否有复杂定价
   * @param {Object} priceInfo - 价格信息
   * @returns {boolean} 是否有复杂定价
   */
  hasComplexPricing(priceInfo) {
    return !!(priceInfo.couponPrice || priceInfo.priceRange.length > 1 || priceInfo.minBatch);
  }

  /**
   * 格式化价格显示
   * @param {Object} priceInfo - 价格信息
   * @returns {Object} 格式化显示
   */
  formatPriceDisplay(priceInfo) {
    const display = {
      main: '',
      coupon: '',
      range: '',
      batch: ''
    };
    
    // 券后价格显示
    if (priceInfo.couponPrice) {
      display.coupon = `券后 ${priceInfo.couponPrice.displayText}`;
    }
    
    // 价格区间显示
    if (priceInfo.priceRange.length > 0) {
      const prices = priceInfo.priceRange.map(p => p.displayText).join(' ~ ');
      display.range = prices;
      display.main = display.range;
    }
    
    // 起批量显示
    if (priceInfo.minBatch) {
      display.batch = priceInfo.minBatch.description;
    }
    
    // 主显示（优先显示价格区间）
    if (!display.main && priceInfo.couponPrice) {
      display.main = priceInfo.couponPrice.displayText;
    }
    
    return display;
  }

  /**
   * 计算可信度
   * @param {Object} priceInfo - 价格信息
   * @returns {number} 可信度 (0-100)
   */
  calculateConfidence(priceInfo) {
    let confidence = 0;
    
    // 基础分数
    confidence += 20;
    
    // 有券后价格加分
    if (priceInfo.couponPrice) confidence += 25;
    
    // 有价格区间加分
    if (priceInfo.priceRange.length > 0) confidence += 30;
    
    // 有起批量加分
    if (priceInfo.minBatch) confidence += 15;
    
    // 有价格标签加分
    if (priceInfo.priceTag) confidence += 10;
    
    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * 获取元素文本内容
   * @param {Element} element - DOM元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    if (!element) return '';
    return (element.textContent || element.innerText || '').trim();
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 检查是否有有效的价格信息
    const hasValidPrice = data.couponPrice || (data.priceRange && data.priceRange.length > 0);
    if (!hasValidPrice) {
      return false;
    }
    
    // 检查可信度
    if (data.confidence && data.confidence < 30) {
      return false;
    }
    
    return true;
  }

  /**
   * 数据格式化
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    if (!data) return null;
    
    return {
      ...data,
      currency: data.currency || '¥',
      formatted: true,
      formatTime: Date.now()
    };
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.WholesalePriceExtractor = WholesalePriceExtractor;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WholesalePriceExtractor;
}