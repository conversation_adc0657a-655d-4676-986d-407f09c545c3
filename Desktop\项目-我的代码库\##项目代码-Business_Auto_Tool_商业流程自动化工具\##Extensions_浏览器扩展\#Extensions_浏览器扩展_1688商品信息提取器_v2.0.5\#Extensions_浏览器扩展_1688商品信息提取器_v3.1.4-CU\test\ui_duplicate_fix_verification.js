/**
 * UI重复显示修复验证脚本
 * 验证多重UI显示问题的修复效果
 */

console.log('🧪 开始UI重复显示修复验证...');

/**
 * 验证配置
 */
const VERIFICATION_CONFIG = {
  // 检查间隔（毫秒）
  checkInterval: 500,
  // 最大检查次数
  maxChecks: 20,
  // 预期的结果公告板数量
  expectedBoardCount: 1
};

/**
 * 验证状态
 */
let verificationState = {
  isRunning: false,
  checkCount: 0,
  boardCounts: [],
  startTime: null,
  results: {
    duplicateDetected: false,
    maxBoards: 0,
    timeline: []
  }
};

/**
 * 检查结果公告板数量
 */
function checkResultsBoards() {
  const boards = document.querySelectorAll('.extractor-results-board');
  const count = boards.length;
  
  console.log(`📊 检查 #${verificationState.checkCount + 1}: 发现 ${count} 个结果公告板`);
  
  // 记录数据
  verificationState.boardCounts.push(count);
  verificationState.results.maxBoards = Math.max(verificationState.results.maxBoards, count);
  verificationState.results.timeline.push({
    time: Date.now() - verificationState.startTime,
    count: count,
    timestamp: new Date().toLocaleTimeString()
  });
  
  // 检查是否有重复
  if (count > VERIFICATION_CONFIG.expectedBoardCount) {
    verificationState.results.duplicateDetected = true;
    console.warn(`⚠️ 检测到重复显示！发现 ${count} 个结果公告板，预期只有 ${VERIFICATION_CONFIG.expectedBoardCount} 个`);
    
    // 记录重复的公告板信息
    boards.forEach((board, index) => {
      console.log(`📋 公告板 #${index + 1}:`, {
        className: board.className,
        visible: board.offsetParent !== null,
        position: board.getBoundingClientRect(),
        innerHTML: board.innerHTML.substring(0, 200) + '...'
      });
    });
  }
  
  return count;
}

/**
 * 开始验证
 */
function startVerification() {
  if (verificationState.isRunning) {
    console.warn('⚠️ 验证已在运行中');
    return;
  }
  
  console.log('🚀 开始UI重复显示验证...');
  
  // 重置状态
  verificationState = {
    isRunning: true,
    checkCount: 0,
    boardCounts: [],
    startTime: Date.now(),
    results: {
      duplicateDetected: false,
      maxBoards: 0,
      timeline: []
    }
  };
  
  // 开始定期检查
  const checkInterval = setInterval(() => {
    verificationState.checkCount++;
    
    const boardCount = checkResultsBoards();
    
    // 检查是否达到最大检查次数
    if (verificationState.checkCount >= VERIFICATION_CONFIG.maxChecks) {
      clearInterval(checkInterval);
      finishVerification();
    }
  }, VERIFICATION_CONFIG.checkInterval);
  
  console.log(`⏰ 将进行 ${VERIFICATION_CONFIG.maxChecks} 次检查，每次间隔 ${VERIFICATION_CONFIG.checkInterval}ms`);
}

/**
 * 完成验证
 */
function finishVerification() {
  verificationState.isRunning = false;
  const duration = Date.now() - verificationState.startTime;
  
  console.log('\n📊 验证完成！结果统计:');
  console.log(`⏱️ 验证时长: ${duration}ms`);
  console.log(`🔢 检查次数: ${verificationState.checkCount}`);
  console.log(`📈 最大公告板数量: ${verificationState.results.maxBoards}`);
  console.log(`🎯 是否检测到重复: ${verificationState.results.duplicateDetected ? '是' : '否'}`);
  
  // 显示时间线
  console.log('\n📅 检查时间线:');
  verificationState.results.timeline.forEach((entry, index) => {
    const status = entry.count > VERIFICATION_CONFIG.expectedBoardCount ? '❌' : '✅';
    console.log(`${status} ${entry.timestamp} (+${entry.time}ms): ${entry.count} 个公告板`);
  });
  
  // 最终结论
  if (verificationState.results.duplicateDetected) {
    console.error('❌ 验证失败：检测到UI重复显示问题');
    console.log('💡 建议检查以下方面：');
    console.log('   - ExtractionManager 是否正确调用 showResultsBoard');
    console.log('   - Popup 是否禁用了自动显示结果');
    console.log('   - 是否存在其他UI显示调用');
  } else {
    console.log('✅ 验证成功：未检测到UI重复显示问题');
    console.log('🎉 修复效果良好！');
  }
  
  return verificationState.results;
}

/**
 * 手动触发提取测试
 */
function triggerExtractionTest() {
  console.log('🧪 手动触发提取测试...');
  
  if (!window.extractionManager) {
    console.error('❌ ExtractionManager 未找到');
    return;
  }
  
  // 开始验证
  startVerification();
  
  // 延迟1秒后开始提取
  setTimeout(async () => {
    try {
      console.log('🚀 开始提取...');
      await window.extractionManager.startExtraction();
    } catch (error) {
      console.error('❌ 提取失败:', error);
    }
  }, 1000);
}

/**
 * 模拟提取完成测试
 */
function simulateExtractionComplete() {
  console.log('🎭 模拟提取完成测试...');
  
  // 开始验证
  startVerification();
  
  // 模拟提取结果
  const mockResults = new Map();
  mockResults.set('wholesale-logistics', {
    success: true,
    name: '批发物流信息',
    data: {
      confidence: 90,
      originCity: '浙江金华',
      destinationCity: '北京朝阳',
      shippingFee: '运费¥4起',
      deliveryTime: '承诺48小时发货'
    }
  });
  
  mockResults.set('dropship-logistics', {
    success: true,
    name: '代发物流信息',
    data: {
      confidence: 85,
      originCity: '广东东莞',
      destinationCity: '上海浦东',
      shippingFee: '¥15.00',
      deliveryTime: '24小时内发货'
    }
  });
  
  // 延迟1秒后显示结果
  setTimeout(() => {
    if (window.uiManager && window.uiManager.showResultsBoard) {
      console.log('📋 显示模拟结果...');
      window.uiManager.showResultsBoard(mockResults);
    } else {
      console.error('❌ UIManager 未找到');
    }
  }, 1000);
}

/**
 * 获取验证报告
 */
function getVerificationReport() {
  return {
    state: verificationState,
    config: VERIFICATION_CONFIG,
    currentBoardCount: document.querySelectorAll('.extractor-results-board').length,
    timestamp: new Date().toISOString()
  };
}

// 导出函数供外部调用
window.uiDuplicateFixVerification = {
  startVerification,
  finishVerification,
  triggerExtractionTest,
  simulateExtractionComplete,
  checkResultsBoards,
  getVerificationReport,
  config: VERIFICATION_CONFIG,
  state: verificationState
};

console.log('📝 UI重复显示修复验证脚本已加载');
console.log('💡 使用方法:');
console.log('  - window.uiDuplicateFixVerification.startVerification() - 开始验证');
console.log('  - window.uiDuplicateFixVerification.triggerExtractionTest() - 触发提取测试');
console.log('  - window.uiDuplicateFixVerification.simulateExtractionComplete() - 模拟提取完成');
console.log('  - window.uiDuplicateFixVerification.getVerificationReport() - 获取验证报告');
