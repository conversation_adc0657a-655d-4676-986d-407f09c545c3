/**
 * 提取管理器 - 统一管理所有数据提取逻辑
 * 负责协调各个抽取器模块，处理提取流程
 * <AUTHOR>
 * @version 1.0.0
 */

class ExtractionManager {
  constructor(loggerManager, uiManager) {
    this.logger = loggerManager;
    this.ui = uiManager;
    this.extractors = new Map();
    this.results = new Map();
    this.isExtracting = false;
    this.dataExportManager = null;
    this.pageInfo = null;

    this.config = {
      maxConcurrentExtractors: 3,
      extractionTimeout: 30000, // 30秒超时
      retryAttempts: 2,
      retryDelay: 2000,
      autoExportToClipboard: true // 自动导出到剪切板
    };

    this.init();
  }

  /**
   * 初始化提取管理器
   */
  init() {
    this.logger.info('ExtractionManager', 'Initializing Extraction Manager');

    try {
      // 初始化数据导出管理器
      this.initDataExportManager();

      // 注册抽取器
      this.registerExtractors();

      this.logger.info('ExtractionManager', 'Extraction Manager initialized successfully');
    } catch (error) {
      this.logger.error('ExtractionManager', 'Failed to initialize Extraction Manager', error);
    }
  }

  /**
   * 初始化数据导出管理器
   */
  initDataExportManager() {
    if (window.DataExportManager) {
      this.dataExportManager = new window.DataExportManager(this.logger);
      this.logger.info('ExtractionManager', 'Data Export Manager initialized');
    } else {
      this.logger.warn('ExtractionManager', 'DataExportManager not available');
    }
  }



  /**
   * 注册抽取器
   */
  registerExtractors() {
    try {
      // 检测页面类型
      const pageType = this.detectPageType();
      this.logger.info('ExtractionManager', `Detected page type: ${pageType}`);

      // 注册商家信息抽取器（根据页面类型）
      if (pageType === 'wholesale') {
        // 批发模式商家抽取器
        if (window.WholesaleMerchantExtractor) {
          const extractor = new window.WholesaleMerchantExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale merchant extractor');
        } else {
          this.logger.warn('ExtractionManager', 'WholesaleMerchantExtractor not available');
        }
      } else if (pageType === 'consign') {
        // 代发模式商家抽取器
        if (window.ConsignMerchantExtractor) {
          const extractor = new window.ConsignMerchantExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered consign merchant extractor');
        } else {
          this.logger.warn('ExtractionManager', 'ConsignMerchantExtractor not available');
        }
      }

      // 注册专业化价格抽取器（根据页面类型）
      if (pageType === 'wholesale') {
        // 批发价格抽取器
        if (window.WholesalePriceExtractor) {
          const extractor = new window.WholesalePriceExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale price extractor');
        } else {
          this.logger.warn('ExtractionManager', 'WholesalePriceExtractor not available');
        }
      } else if (pageType === 'consign') {
        // 代发价格抽取器
        if (window.ConsignPriceExtractor) {
          const extractor = new window.ConsignPriceExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered consign price extractor');
        } else {
          this.logger.warn('ExtractionManager', 'ConsignPriceExtractor not available');
        }
      }

      // 注册专业化抽取器（根据页面类型选择合适的提取器）

      // 商品标题抽取器（根据页面类型）
      if (pageType === 'wholesale') {
        if (window.WholesaleProductTitleExtractor) {
          const extractor = new window.WholesaleProductTitleExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale product title extractor');
        } else {
          this.logger.warn('ExtractionManager', 'WholesaleProductTitleExtractor not available');
        }
      } else if (pageType === 'consign') {
        if (window.ConsignProductTitleExtractor) {
          const extractor = new window.ConsignProductTitleExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered consign product title extractor');
        } else {
          this.logger.warn('ExtractionManager', 'ConsignProductTitleExtractor not available');
        }
      }

      // 商品图片抽取器（根据页面类型）
      if (pageType === 'wholesale') {
        if (window.WholesaleProductImagesExtractor) {
          const extractor = new window.WholesaleProductImagesExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale product images extractor');
        } else {
          this.logger.warn('ExtractionManager', 'WholesaleProductImagesExtractor not available');
        }
      } else if (pageType === 'consign') {
        if (window.ConsignProductImagesExtractor) {
          const extractor = new window.ConsignProductImagesExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered consign product images extractor');
        } else {
          this.logger.warn('ExtractionManager', 'ConsignProductImagesExtractor not available');
        }
      }

      // 商品评价信息抽取器（根据页面类型）
      if (pageType === 'wholesale') {
        if (window.WholesaleProductRatingExtractor) {
          const extractor = new window.WholesaleProductRatingExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale product rating extractor');
        } else {
          this.logger.warn('ExtractionManager', 'WholesaleProductRatingExtractor not available');
        }
      } else if (pageType === 'consign') {
        if (window.ConsignProductRatingExtractor) {
          const extractor = new window.ConsignProductRatingExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered consign product rating extractor');
        } else {
          this.logger.warn('ExtractionManager', 'ConsignProductRatingExtractor not available');
        }
      }

      // 商品规格属性抽取器（根据页面类型）
      if (pageType === 'wholesale') {
        if (window.WholesaleProductSpecsExtractor) {
          const extractor = new window.WholesaleProductSpecsExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale product specs extractor');
        } else {
          this.logger.warn('ExtractionManager', 'WholesaleProductSpecsExtractor not available');
        }
      } else if (pageType === 'consign') {
        if (window.ConsignProductSpecsExtractor) {
          const extractor = new window.ConsignProductSpecsExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered consign product specs extractor');
        } else {
          this.logger.warn('ExtractionManager', 'ConsignProductSpecsExtractor not available');
        }
      }

      // 注册商品详细信息提取器（批发模式专用）
      if (pageType === 'wholesale') {
        if (window.WholesaleProductInfoExtractor) {
          const extractor = new window.WholesaleProductInfoExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale product info extractor');
        } else {
          this.logger.warn('ExtractionManager', 'WholesaleProductInfoExtractor not available');
        }
      }

      // 注册物流信息抽取器（根据页面类型选择合适的提取器）
      if (pageType === 'wholesale' || pageType === 'unknown') {
        // 批发模式或未知页面类型：使用批发物流提取器
        if (window.WholesaleLogisticsExtractor) {
          const extractor = new window.WholesaleLogisticsExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          console.log('🚚 [ExtractionManager] 批发物流提取器注册成功:', {
            moduleId: extractor.moduleId,
            name: extractor.name,
            description: extractor.description,
            pageType: pageType,
            reason: 'Wholesale page detected, using wholesale logistics extractor'
          });
          this.logger.info('ExtractionManager', `Registered wholesale logistics extractor (pageType: ${pageType})`);
        } else {
          console.error('❌ [ExtractionManager] WholesaleLogisticsExtractor 未找到');
          this.logger.warn('ExtractionManager', 'WholesaleLogisticsExtractor not available');
        }
      } else if (pageType === 'consign') {
        // 代发模式：使用代发物流提取器
        if (window.ConsignLogisticsExtractor) {
          const extractor = new window.ConsignLogisticsExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          console.log('🚚 [ExtractionManager] 代发物流提取器注册成功:', {
            moduleId: extractor.moduleId,
            name: extractor.name,
            description: extractor.description,
            pageType: pageType,
            reason: 'Consign page detected, using consign logistics extractor'
          });
          this.logger.info('ExtractionManager', `Registered consign logistics extractor (pageType: ${pageType})`);
        } else {
          console.error('❌ [ExtractionManager] ConsignLogisticsExtractor 未找到');
          this.logger.warn('ExtractionManager', 'ConsignLogisticsExtractor not available');
        }
      } else {
        // 其他情况：同时注册两个提取器作为备用
        console.log('🔄 [ExtractionManager] 页面类型不确定，注册双物流提取器作为备用');
        
        if (window.WholesaleLogisticsExtractor) {
          const extractor = new window.WholesaleLogisticsExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered wholesale logistics extractor as fallback');
        }
        
        if (window.ConsignLogisticsExtractor) {
          const extractor = new window.ConsignLogisticsExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered consign logistics extractor as fallback');
        }
      }

      // 如果页面类型未知，使用批发版本作为默认
      if (pageType === 'unknown') {
        this.logger.warn('ExtractionManager', 'Unknown page type, using wholesale extractors as fallback');

        if (window.WholesaleProductTitleExtractor) {
          const extractor = new window.WholesaleProductTitleExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered fallback product title extractor');
        }

        if (window.WholesaleProductRatingExtractor) {
          const extractor = new window.WholesaleProductRatingExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered fallback product rating extractor');
        }

        if (window.WholesaleProductSpecsExtractor) {
          const extractor = new window.WholesaleProductSpecsExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered fallback product specs extractor');
        }

        if (window.WholesaleProductImagesExtractor) {
          const extractor = new window.WholesaleProductImagesExtractor();
          this.extractors.set(extractor.moduleId, extractor);
          this.logger.info('ExtractionManager', 'Registered fallback product images extractor');
        }
      }

      // 添加抽取器到进度管理器
      if (window.progressManager) {
        for (const extractor of this.extractors.values()) {
          window.progressManager.addTask(extractor.moduleId, extractor.name, extractor.description);
        }
      }

      this.logger.info('ExtractionManager', `Registered ${this.extractors.size} extractors`);
    } catch (error) {
      this.logger.error('ExtractionManager', 'Failed to register extractors', error);
    }
  }

  /**
   * 检测页面类型
   * @returns {string} 页面类型
   */
  detectPageType() {
    try {
      if (!window.urlDetector) {
        this.logger.error('ExtractionManager', 'URL Detector not available');
        return 'unknown';
      }

      const detection = window.urlDetector.detectPageType();

      // 保存页面信息供导出使用
      this.pageInfo = {
        url: window.location.href,
        pageType: detection.pageType,
        confidence: detection.confidence,
        timestamp: Date.now(),
        extractionStartTime: Date.now()
      };

      return detection.pageType;
    } catch (error) {
      this.logger.error('ExtractionManager', 'Failed to detect page type', error);
      return 'unknown';
    }
  }

  /**
   * 开始提取
   * @returns {Promise<Object>} 提取结果
   */
  async startExtraction() {
    if (this.isExtracting) {
      this.logger.warn('ExtractionManager', 'Extraction already in progress');
      return { success: false, error: 'Extraction already in progress' };
    }

    try {
      this.isExtracting = true;
      this.ui.setExtracting(true);

      this.logger.info('ExtractionManager', 'Starting extraction process');

      // 清空之前的结果
      this.results.clear();

      // 更新UI状态
      this.ui.updateStatusIndicator('extracting', '正在提取商品信息...');
      this.ui.showProgressToast('开始提取商品信息');

      // 开始进度管理
      if (window.progressManager) {
        window.progressManager.startAll();
      }

      // 并发执行所有抽取器
      const extractorPromises = Array.from(this.extractors.values()).map(extractor =>
        this.runExtractorWithRetry(extractor)
      );

      // 等待所有抽取器完成
      const results = await Promise.allSettled(extractorPromises);

      // 统计结果
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failCount = results.length - successCount;

      this.logger.info('ExtractionManager', `Extraction completed: ${successCount} success, ${failCount} failed`);

      // 更新UI状态
      if (failCount === 0) {
        this.ui.updateStatusIndicator('completed', `提取完成！成功提取 ${successCount} 项信息`);
        this.ui.showProgressToast('商品信息提取完成');
      } else {
        this.ui.updateStatusIndicator('failed', `提取完成：${successCount} 成功，${failCount} 失败`);
        this.ui.showProgressToast(`提取完成，部分失败`);
      }

      // 自动导出到剪切板
      if (this.config.autoExportToClipboard && this.dataExportManager) {
        try {
          const jsonData = await this.dataExportManager.exportToJSON(this.results, this.pageInfo);
          const exportSuccess = await this.dataExportManager.exportToClipboard(jsonData);

          if (exportSuccess) {
            this.ui.showProgressToast('数据已自动复制到剪切板', '📋');
            this.logger.info('ExtractionManager', 'Data automatically exported to clipboard');
          } else {
            this.ui.showProgressToast('剪切板导出失败，请手动导出', '⚠️');
          }
        } catch (error) {
          this.logger.error('ExtractionManager', 'Failed to auto-export to clipboard', error);
          this.ui.showProgressToast('自动导出失败', '❌');
        }
      }

      // 显示结果公告板
      setTimeout(() => {
        // 准备显示结果公告板

        this.ui.showResultsBoard(this.results);
        // 公告板显示后隐藏状态指示器
        setTimeout(() => {
          if (this.ui.statusIndicator) {
            this.ui.statusIndicator.classList.add('hidden');
          }
        }, 1000);
      }, 2000);

      return {
        success: true,
        results: Object.fromEntries(this.results),
        stats: { success: successCount, failed: failCount, total: results.length }
      };

    } catch (error) {
      this.logger.error('ExtractionManager', 'Extraction process failed', error);

      // 更新UI状态
      this.ui.updateStatusIndicator('failed', `提取失败：${error.message}`);
      this.ui.showProgressToast('提取过程中发生错误');

      return { success: false, error: error.message };

    } finally {
      this.isExtracting = false;
      this.ui.setExtracting(false);

      // 停止进度管理器
      if (window.progressManager) {
        window.progressManager.stopAll();
      }
    }
  }

  /**
   * 带重试的运行抽取器
   * @param {Object} extractor - 抽取器实例
   * @returns {Promise<Object>} 抽取结果
   */
  async runExtractorWithRetry(extractor) {
    let lastError = null;

    for (let attempt = 0; attempt <= this.config.retryAttempts; attempt++) {
      try {
        this.logger.info('ExtractionManager', `Running extractor ${extractor.moduleId} (attempt ${attempt + 1})`);

        const result = await this.runExtractor(extractor);

        // 检查结果是否成功
        // 对于评价抽取器，即使没有数据也应该被认为是成功的
        const isSuccess = result.success ||
          (extractor.moduleId === '1688_product_rating_001' && result.data && result.data.success);

        if (isSuccess) {
          this.logger.info('ExtractionManager', `Extractor ${extractor.moduleId} completed successfully`);
          return result;
        } else {
          throw new Error(result.error || 'Extraction failed');
        }

      } catch (error) {
        lastError = error;

        // 对于评价抽取器，如果错误信息表明这是已处理的情况，不进行重试
        if (extractor.moduleId === '1688_product_rating_001' &&
          (error.message.includes('已处理') || error.message.includes('正常情况'))) {
          this.logger.info('ExtractionManager', `Extractor ${extractor.moduleId} completed with handled result`);
          return {
            success: true,
            name: extractor.name,
            data: {
              rating: null,
              success: true,
              extractionMethod: 'error_handled',
              confidence: 1,
              message: '评价信息未找到但已正确处理'
            },
            timestamp: Date.now()
          };
        }

        this.logger.warn('ExtractionManager', `Extractor ${extractor.moduleId} failed (attempt ${attempt + 1}): ${error.message}`);

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.config.retryAttempts) {
          await this.delay(this.config.retryDelay);
        }
      }
    }

    // 所有重试都失败了
    this.logger.error('ExtractionManager', `Extractor ${extractor.moduleId} failed after ${this.config.retryAttempts + 1} attempts`, lastError);
    return { success: false, error: lastError.message, extractor: extractor.moduleId };
  }

  /**
   * 运行单个抽取器
   * @param {Object} extractor - 抽取器实例
   * @returns {Promise<Object>} 抽取结果
   */
  async runExtractor(extractor) {
    try {
      // 更新进度
      if (window.progressManager) {
        window.progressManager.updateProgress(extractor.moduleId, 0, 'processing');
      }

      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Extraction timeout')), this.config.extractionTimeout);
      });

      // 执行抽取
      console.log(`🔄 [ExtractionManager] 开始执行提取器: ${extractor.moduleId} (${extractor.name})`);
      const extractionPromise = extractor.extract();

      // 等待抽取完成或超时
      const data = await Promise.race([extractionPromise, timeoutPromise]);
      
      // 添加物流提取器的详细调试
      if (extractor.moduleId === '1688_consign_logistics_001') {
        console.log('🚚 [ExtractionManager] 物流提取器原始数据:', {
          success: data?.success,
          hasData: !!data?.data,
          dataKeys: data?.data ? Object.keys(data.data) : [],
          confidence: data?.confidence,
          rawData: data
        });
      }

      // 对于评价抽取器和商品属性抽取器，如果数据本身包含success标记，直接使用
      if ((extractor.moduleId === '1688_product_rating_001' || extractor.moduleId === '1688_product_specs_001') && data && data.success) {
        const result = {
          success: true,
          name: extractor.name,
          data: data,
          timestamp: Date.now()
        };

        this.results.set(extractor.moduleId, result);

        // 更新进度
        if (window.progressManager) {
          window.progressManager.updateProgress(extractor.moduleId, 100, 'completed');
        }

        return result;
      }

      // 验证数据
      const isValid = extractor.validate(data);
      if (extractor.moduleId === '1688_consign_logistics_001') {
        console.log('🔍 [ExtractionManager] 物流数据验证:', {
          isValid,
          data: data
        });
      }
      
      if (!isValid) {
        if (extractor.moduleId === '1688_consign_logistics_001') {
          console.error('❌ [ExtractionManager] 物流数据验证失败');
        }
        throw new Error('Data validation failed');
      }

      // 格式化数据
      const formattedData = extractor.format(data);
      
      if (extractor.moduleId === '1688_consign_logistics_001') {
        console.log('📋 [ExtractionManager] 物流数据格式化结果:', {
          hasFormattedData: !!formattedData,
          formattedDataKeys: formattedData ? Object.keys(formattedData) : [],
          formattedData: formattedData
        });
      }

      // 存储结果
      const result = {
        success: true,
        name: extractor.name,
        data: formattedData,
        timestamp: Date.now()
      };

      this.results.set(extractor.moduleId, result);

      // 更新进度
      if (window.progressManager) {
        window.progressManager.updateProgress(extractor.moduleId, 100, 'completed');
      }

      return result;

    } catch (error) {
      // 对于评价抽取器的特殊处理
      if (extractor.moduleId === '1688_product_rating_001') {
        // 如果是评价抽取器，尝试创建一个成功的空结果
        const result = {
          success: true,
          name: extractor.name,
          data: {
            rating: null,
            success: true,
            extractionMethod: 'error_handled',
            confidence: 1,
            message: '评价信息提取遇到问题但已处理',
            metadata: {
              extractorType: 'product_rating',
              extractionTime: Date.now(),
              hasRating: false,
              hasReviews: false,
              hasSales: false
            }
          },
          timestamp: Date.now()
        };

        this.results.set(extractor.moduleId, result);

        // 更新进度为完成
        if (window.progressManager) {
          window.progressManager.updateProgress(extractor.moduleId, 100, 'completed');
        }

        return result;
      }

      // 其他抽取器的正常错误处理
      // 更新进度为失败
      if (window.progressManager) {
        window.progressManager.updateProgress(extractor.moduleId, 0, 'failed');
      }

      const result = {
        success: false,
        name: extractor.name,
        error: error.message,
        timestamp: Date.now()
      };

      this.results.set(extractor.moduleId, result);

      throw error;
    }
  }

  /**
   * 停止提取
   */
  async stopExtraction() {
    try {
      this.logger.info('ExtractionManager', 'Stopping extraction process');

      this.isExtracting = false;
      this.ui.setExtracting(false);

      // 更新UI状态
      this.ui.updateStatusIndicator('ready', '提取已停止');
      this.ui.showProgressToast('提取已手动停止');

      // 停止进度管理器
      if (window.progressManager) {
        window.progressManager.stopAll();
      }

      this.logger.info('ExtractionManager', 'Extraction stopped successfully');
    } catch (error) {
      this.logger.error('ExtractionManager', 'Failed to stop extraction', error);
    }
  }

  /**
   * 重置提取器
   */
  async resetExtraction() {
    try {
      this.logger.info('ExtractionManager', 'Resetting extraction process');

      // 停止当前提取
      this.isExtracting = false;
      this.ui.setExtracting(false);

      // 清空结果
      this.results.clear();

      // 重置进度管理器
      if (window.progressManager) {
        window.progressManager.reset();
      }

      // 更新UI状态
      this.ui.updateStatusIndicator('ready', '已重置 - 准备开始新的提取');
      this.ui.showProgressToast('提取器已重置');

      // 重新注册抽取器
      this.extractors.clear();
      this.registerExtractors();

      this.logger.info('ExtractionManager', 'Extraction reset successfully');
    } catch (error) {
      this.logger.error('ExtractionManager', 'Failed to reset extraction', error);
    }
  }

  /**
   * 导出结果
   */
  exportResults() {
    try {
      const data = {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        pageType: this.detectPageType(),
        results: {}
      };

      // 整理结果数据
      for (const [extractorId, result] of this.results) {
        if (result.success) {
          data.results[extractorId] = {
            name: result.name,
            data: result.data,
            confidence: result.data.confidence || 0
          };
        }
      }

      // 创建下载
      const jsonStr = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `1688_extraction_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      URL.revokeObjectURL(url);

      this.ui.showProgressToast('数据导出成功');
      this.logger.info('ExtractionManager', 'Results exported successfully');
    } catch (error) {
      this.logger.error('ExtractionManager', 'Failed to export results', error);
      this.ui.showProgressToast('导出失败', '❌');
    }
  }

  /**
   * 获取提取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      totalExtractors: this.extractors.size,
      totalResults: this.results.size,
      successCount: 0,
      failCount: 0,
      isExtracting: this.isExtracting
    };

    for (const result of this.results.values()) {
      if (result.success) {
        stats.successCount++;
      } else {
        stats.failCount++;
      }
    }

    return stats;
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  cleanup() {
    try {
      this.stopExtraction();
      this.extractors.clear();
      this.results.clear();

      this.logger.info('ExtractionManager', 'Extraction Manager cleaned up');
    } catch (error) {
      this.logger.error('ExtractionManager', 'Failed to cleanup Extraction Manager', error);
    }
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.ExtractionManager = ExtractionManager;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ExtractionManager;
}