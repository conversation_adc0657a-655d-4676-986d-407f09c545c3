# 1688页面DOM结构分析报告

## 页面类型对比分析

### 批发模式 (wholesale) 页面特征
**URL**: `https://detail.1688.com/offer/711914850251.html?offerId=711914850251&spm=a260k.home2025.recommendpart.3`

#### 关键DOM结构:
1. **商家信息区域**:
   ```html
   <div class="winport-title v-flex">
     <a class="shop-company-name v-flex" href="...">
       <h1 title="义乌市靖发箱包有限公司">义乌市靖发箱包有限公司</h1>
     </a>
   </div>
   ```

2. **商品标题区域**:
   ```html
   <div class="od-pc-offer-title-contain">
     <div class="title-content">
       <div class="title-first-column">
         <div class="title-text">ballchain日系2025小众自制刺绣环保托特包刺绣尼龙购物</div>
       </div>
       <div class="title-second-column">
         <div class="title-text title-second-text">袋包帆布</div>
       </div>
     </div>
   </div>
   ```

3. **价格信息区域**:
   ```html
   <div class="od-pc-offer-price-contain">
     <div class="price-wrapper">
       <span class="price-unit">¥</span>
       <span class="price-text"><strong>24</strong><strong>.00</strong></span>
       <span class="start-text">起</span>
     </div>
   </div>
   ```

### 代发模式 (consign) 页面特征
**URL**: `https://detail.1688.com/offer/711914850251.html?sk=consign&...`

#### 关键DOM结构:
1. **商家信息区域** (移动端样式):
   ```html
   <div>
     <span title="义乌市靖发箱包有限公司">义乌市靖发箱包有限公司</span>
   </div>
   ```

2. **商品标题区域**:
   ```html
   <div class="title-content">
     <div class="title-first-column">
       <div class="title-text">ballchain日系2025小众自制刺绣环保托特包刺绣尼龙购物</div>
     </div>
     <div class="title-second-column">
       <div class="title-text title-second-text">袋包帆布</div>
     </div>
   </div>
   ```

3. **代发价格区域**:
   ```html
   <div class="od-fx-price-pc-price-wrapper">
     <div class="od-fx-price-pc-price-column">
       <div class="od-fx-price-pc-price-box">
         <span class="price-unit">¥</span>
         <span class="price-text"><strong>24</strong><strong>.00</strong></span>
         <span class="start-text">起</span>
       </div>
       <div class="desc-text">≥1件</div>
     </div>
   </div>
   ```

## 可提取的商品信息分组

### 第一组：基础商品信息
1. **商家名称** - 两种模式都有，但DOM结构不同
2. **商品标题** - 结构相似，可统一处理
3. **商品价格** - 价格结构基本一致
4. **起订量** - 代发模式特有

### 第二组：商品详情信息
1. **商品图片** - 图片轮播区域
2. **商品视频** - 视频展示区域
3. **商品评价** - 评分和评价数量
4. **销量信息** - 全网销量数据

### 第三组：服务和权益信息
1. **服务标签** - 7天无理由退货等
2. **权益信息** - 先采后付等
3. **物流信息** - 发货地、运费等
4. **售后服务** - 退换货政策

### 第四组：店铺信息
1. **店铺评分** - 服务评分
2. **回头率** - 客户回头率
3. **店铺标签** - 性价比高、质量好等
4. **联系方式** - 客服链接

## DOM选择器策略

### 批发模式选择器
```javascript
const wholesaleSelectors = {
  merchant: {
    primary: 'h1[title]',
    fallback: '.shop-company-name h1',
    context: '.winport-title'
  },
  title: {
    primary: '.title-text',
    fallback: '.od-pc-offer-title-contain .title-content',
    multiple: true
  },
  price: {
    primary: '.price-text strong',
    unit: '.price-unit',
    context: '.od-pc-offer-price-contain'
  },
  images: {
    primary: '.od-gallery-img',
    context: '.od-gallery-turn'
  },
  rating: {
    primary: '.ant-rate',
    score: '.title-evaluate-score',
    count: '.title-info-number'
  }
};
```

### 代发模式选择器
```javascript
const consignSelectors = {
  merchant: {
    primary: 'span[title*="公司"]',
    fallback: 'span[title*="企业"]',
    context: '#hd_0_container_0'
  },
  title: {
    primary: '.title-text',
    fallback: '.title-content div',
    multiple: true
  },
  price: {
    primary: '.od-fx-price-pc-price-box .price-text strong',
    unit: '.price-unit',
    context: '.od-fx-price-pc-price-wrapper'
  },
  minOrder: {
    primary: '.desc-text',
    context: '.od-fx-price-pc-price-column'
  },
  images: {
    primary: '.detail-gallery-img',
    context: '.detail-gallery-turn'
  }
};
```

## 页面差异总结

### 主要差异点:
1. **商家信息**: 批发用`<h1>`标签，代发用`<span>`标签
2. **页面布局**: 批发是PC端布局，代发偏向移动端布局
3. **价格展示**: 代发模式有最小起订量显示
4. **功能标签**: 代发模式有"一件代发"标签

### 相同点:
1. **商品标题**: 结构基本相同
2. **图片轮播**: 功能和结构相似
3. **评价系统**: 评分和评价数量展示相同
4. **基础商品信息**: 核心信息内容一致

## 建议的抽取器模块设计

基于以上分析，建议创建以下独立抽取器模块:

1. **商家信息抽取器** (已有)
2. **商品标题抽取器** (新增)
3. **价格信息抽取器** (新增)
4. **商品图片抽取器** (新增)
5. **评价信息抽取器** (新增)
6. **服务标签抽取器** (新增)
7. **店铺信息抽取器** (新增)

每个抽取器都应该支持批发和代发两种模式的不同DOM结构。