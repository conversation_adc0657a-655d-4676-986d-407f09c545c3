# 1688商品信息提取器 - 开发指南

**版本**: v3.1.0
**目标**: 为开发者提供完整的开发指导
**更新时间**: 2025年1月

---

## 🚀 快速开始

### 环境要求
- **浏览器**: Chrome 88+ / Edge 88+ / Firefox 85+
- **开发工具**: VS Code (推荐)
- **调试工具**: Chrome DevTools
- **版本控制**: Git

### 项目结构
```
1688-product-extractor-v3.1.0/
├── manifest.json              # 扩展配置文件
├── background/               # 后台脚本
│   └── background.js
├── content/                  # 内容脚本
│   └── content-script.js
├── core/                     # 核心模块
│   ├── base-extractor.js     # 基础提取器
│   ├── extraction-manager.js # 提取管理器
│   ├── ui-manager.js         # UI管理器
│   ├── result-formatter.js   # 结果格式化器 (v3.1.0新增)
│   ├── data-export-manager.js# 数据导出管理器
│   ├── logger-manager.js     # 日志管理器
│   └── ...
├── extractors/               # 数据提取器
│   ├── wholesale/            # 批发模式提取器
│   └── consign/              # 代发模式提取器
├── popup/                    # 弹出窗口
├── styles/                   # 样式文件
├── icons/                    # 图标资源
└── docs/                     # 文档
```

### 快速安装
1. **克隆项目**:
   ```bash
   git clone <repository-url>
   cd 1688-product-extractor
   ```

2. **加载扩展**:
   - 打开Chrome扩展管理页面 (`chrome://extensions/`)
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录

3. **验证安装**:
   - 访问1688商品页面
   - 点击扩展图标
   - 查看是否正常显示界面

---

## 🏗️ 架构详解

### 核心设计原则

#### 1. 模块化设计
- **单一职责**: 每个模块只负责一个核心功能
- **松耦合**: 模块间通过接口通信，减少直接依赖
- **高内聚**: 相关功能集中在同一模块内

#### 2. 配置驱动
- **行为配置**: 通过配置控制不同模式的行为差异
- **选择器配置**: CSS选择器通过配置文件管理
- **格式化配置**: UI显示格式通过配置控制

#### 3. 错误处理
- **分层处理**: 不同层次的错误采用不同处理策略
- **优雅降级**: 功能异常时提供备选方案
- **用户友好**: 错误信息对用户友好且可操作

### 模块详解

#### ContentScript (主协调器)
**职责**: 整体流程协调和模块初始化

```javascript
class ContentScript {
  async init() {
    // 1. 检查页面有效性
    if (!this.isValidPage()) return;
    
    // 2. 初始化核心模块
    this.initLoggerManager();
    this.initUIManager();
    this.initExtractionManager();
    
    // 3. 设置事件监听
    this.setupEventListeners();
  }
}
```

**关键方法**:
- `isValidPage()`: 检查当前页面是否为1688商品页面
- `initModules()`: 按顺序初始化各个模块
- `setupEventListeners()`: 设置消息监听和事件处理

#### ExtractionManager (提取管理器)
**职责**: 管理所有数据提取器，控制提取流程

```javascript
class ExtractionManager {
  async startExtraction() {
    // 1. 检测页面类型
    const pageType = this.detectPageType();
    
    // 2. 获取对应的提取器
    const extractors = this.getExtractorsForPageType(pageType);
    
    // 3. 并行执行提取
    const results = await this.runExtractorsInParallel(extractors);
    
    // 4. 显示结果
    this.ui.showResultsBoard(results);
  }
}
```

**关键特性**:
- **并行提取**: 多个提取器同时运行，提升效率
- **错误隔离**: 单个提取器失败不影响其他提取器
- **重试机制**: 失败的提取器自动重试
- **进度反馈**: 实时显示提取进度

#### UIManager (界面管理器)
**职责**: 管理所有UI组件和用户交互

```javascript
class UIManager {
  constructor(loggerManager) {
    this.logger = loggerManager;
    this.resultFormatter = new ResultFormatter(); // v3.1.0新特性
  }
  
  formatResultItem(extractorId, result) {
    // 使用统一的格式化器
    const pageType = this.detectPageType();
    return this.resultFormatter.formatResultItem(extractorId, result, pageType);
  }
}
```

**v3.1.0优化**:
- 移除复杂的委托系统
- 使用统一的ResultFormatter
- 简化调用链路
- 提升性能30%

#### ResultFormatter (结果格式化器) 🆕
**职责**: 统一的数据格式化接口

```javascript
class ResultFormatter {
  constructor() {
    this.formatters = new Map();
    this.initFormatters();
  }
  
  formatResultItem(extractorId, result, pageType) {
    const config = this.formatters.get(pageType);
    
    // 根据数据类型选择格式化方法
    if (result.data?.title) return this.formatTitleResult(result.data, config);
    if (result.data?.price) return this.formatPriceResult(result.data, config, pageType);
    if (result.data?.specs) return this.formatSpecsResult(result.data, config);
    // ... 其他类型
  }
}
```

**核心特性**:
- **配置驱动**: 通过配置控制格式化行为
- **类型识别**: 自动识别数据类型并选择合适的格式化方法
- **模式适配**: 支持批发、代发等不同模式
- **错误处理**: 完善的异常处理和降级机制

---

## 🛠️ 开发实践

### 添加新的提取器

#### 1. 创建提取器类
```javascript
// extractors/wholesale/extractor_wholesale_new_feature_v1.0.js
class WholesaleNewFeatureExtractor extends BaseExtractor {
  constructor() {
    super(
      '1688_wholesale_new_feature_001',
      '批发新功能提取器',
      '提取1688批发页面的新功能数据'
    );
  }
  
  getSelectors() {
    return {
      primary: {
        container: ['.new-feature-container', '.nf-wrapper'],
        dataElements: ['.nf-data', '.new-feature-item']
      },
      fallback: {
        container: ['[data-new-feature]', '.fallback-container']
      }
    };
  }
  
  async extract() {
    try {
      const selectors = this.getSelectors();
      const data = await this.extractNewFeatureData(selectors);
      
      return {
        success: true,
        data: this.enhanceData(data),
        confidence: this.calculateConfidence(data)
      };
    } catch (error) {
      this.logger.error('NewFeatureExtractor', 'Extraction failed', error);
      throw error;
    }
  }
  
  async extractNewFeatureData(selectors) {
    // 具体的提取逻辑
    const container = await this.findContainer(selectors.primary.container);
    if (!container) throw new Error('Container not found');
    
    const elements = container.querySelectorAll(selectors.primary.dataElements.join(','));
    return Array.from(elements).map(el => this.extractElementData(el));
  }
}
```

#### 2. 注册提取器
在 `manifest.json` 中添加脚本引用:
```json
{
  "content_scripts": [{
    "js": [
      // ... 现有脚本
      "extractors/wholesale/extractor_wholesale_new_feature_v1.0.js",
      // ... 其他脚本
    ]
  }]
}
```

#### 3. 在ExtractionManager中注册
```javascript
// core/extraction-manager.js
registerExtractors() {
  // ... 现有注册逻辑
  
  if (window.WholesaleNewFeatureExtractor) {
    const extractor = new window.WholesaleNewFeatureExtractor();
    this.extractors.set(extractor.moduleId, extractor);
    this.logger.info('ExtractionManager', `Registered: ${extractor.moduleId}`);
  }
}
```

### 添加新的格式化类型

#### 1. 在ResultFormatter中添加格式化方法
```javascript
// core/result-formatter.js
formatNewFeatureResult(data, config) {
  if (!data || !data.items) {
    return `<div class="result-value">新功能: 暂无数据</div>`;
  }
  
  let content = `<div class="result-value">新功能: ${data.items.length} 项</div>`;
  
  // 显示前几项
  const displayItems = data.items.slice(0, config.newFeatureLimit || 3);
  displayItems.forEach(item => {
    content += `<div class="result-detail">${item.name}: ${item.value}</div>`;
  });
  
  if (data.items.length > displayItems.length) {
    const remaining = data.items.length - displayItems.length;
    content += `<div class="result-detail more-items">还有 ${remaining} 项...</div>`;
  }
  
  return content;
}
```

#### 2. 在主格式化方法中添加条件
```javascript
formatResultItem(extractorId, result, pageType) {
  // ... 现有逻辑
  
  if (result.data?.newFeature || result.data?.items) {
    return this.formatNewFeatureResult(result.data, config);
  }
  
  // ... 其他条件
}
```

#### 3. 更新配置
```javascript
initFormatters() {
  this.formatters.set('wholesale', {
    // ... 现有配置
    newFeatureLimit: 5,
    showNewFeatureDetails: true
  });
  
  this.formatters.set('consign', {
    // ... 现有配置
    newFeatureLimit: 3,
    showNewFeatureDetails: false
  });
}
```

### 调试和测试

#### 1. 使用DebugPanel
```javascript
// 在提取器中添加调试信息
this.debugPanel?.addExtractorData(this.moduleId, {
  selector: usedSelector,
  element: element,
  rawData: rawData,
  processedData: processedData
});
```

#### 2. 日志记录
```javascript
// 使用分级日志
this.logger.debug('ExtractorName', 'Debug message', debugData);
this.logger.info('ExtractorName', 'Info message');
this.logger.warn('ExtractorName', 'Warning message', warningData);
this.logger.error('ExtractorName', 'Error message', error);
```

#### 3. 性能监控
```javascript
// 监控提取性能
const startTime = performance.now();
// ... 提取逻辑
const endTime = performance.now();
this.logger.info('Performance', `Extraction took ${endTime - startTime}ms`);
```

---

## 📊 性能优化

### 提取器性能优化

#### 1. 选择器优化
```javascript
// ❌ 低效的选择器
getSelectors() {
  return {
    primary: {
      elements: ['*[class*="price"]'] // 遍历所有元素
    }
  };
}

// ✅ 高效的选择器
getSelectors() {
  return {
    primary: {
      elements: ['.price-container .price', '#price-info .value'] // 精确定位
    }
  };
}
```

#### 2. 批量DOM操作
```javascript
// ❌ 多次DOM查询
const elements = [];
for (const selector of selectors) {
  const el = document.querySelector(selector);
  if (el) elements.push(el);
}

// ✅ 批量查询
const combinedSelector = selectors.join(',');
const elements = document.querySelectorAll(combinedSelector);
```

#### 3. 异步处理优化
```javascript
// ❌ 串行处理
for (const extractor of extractors) {
  const result = await extractor.extract();
  results.push(result);
}

// ✅ 并行处理
const promises = extractors.map(extractor => extractor.extract());
const results = await Promise.allSettled(promises);
```

### UI性能优化

#### 1. 虚拟滚动
```javascript
// 对于大量数据的显示，使用虚拟滚动
class VirtualList {
  constructor(container, items, itemHeight) {
    this.container = container;
    this.items = items;
    this.itemHeight = itemHeight;
    this.visibleStart = 0;
    this.visibleEnd = 0;
  }
  
  render() {
    const containerHeight = this.container.clientHeight;
    const visibleCount = Math.ceil(containerHeight / this.itemHeight);
    
    // 只渲染可见的项目
    const visibleItems = this.items.slice(this.visibleStart, this.visibleEnd);
    this.renderItems(visibleItems);
  }
}
```

#### 2. 防抖和节流
```javascript
// 防抖：延迟执行
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流：限制执行频率
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
```

---

## 🔧 故障排除

### 常见问题

#### 1. 扩展加载失败
**症状**: 扩展图标显示错误或无法点击

**排查步骤**:
1. 检查 `manifest.json` 语法是否正确
2. 验证所有引用的文件是否存在
3. 查看Chrome扩展管理页面的错误信息
4. 检查控制台是否有JavaScript错误

**解决方案**:
```javascript
// 添加错误处理
try {
  // 模块初始化代码
} catch (error) {
  console.error('Module initialization failed:', error);
  // 发送错误报告
  chrome.runtime.sendMessage({
    type: 'error',
    error: error.message,
    stack: error.stack
  });
}
```

#### 2. 数据提取失败
**症状**: 提取器运行但没有返回数据

**排查步骤**:
1. 检查页面DOM结构是否发生变化
2. 验证CSS选择器是否仍然有效
3. 查看网络请求是否被阻止
4. 检查页面加载时机

**解决方案**:
```javascript
// 添加选择器验证
validateSelectors(selectors) {
  for (const [key, selectorList] of Object.entries(selectors)) {
    const found = selectorList.some(selector => {
      return document.querySelector(selector) !== null;
    });
    
    if (!found) {
      this.logger.warn('SelectorValidation', `No elements found for ${key}`);
    }
  }
}
```

#### 3. UI显示异常
**症状**: 结果显示不正确或样式错乱

**排查步骤**:
1. 检查CSS样式是否被页面样式覆盖
2. 验证HTML结构是否正确
3. 查看是否有JavaScript错误
4. 检查数据格式是否符合预期

**解决方案**:
```javascript
// 添加样式隔离
const shadowRoot = element.attachShadow({mode: 'closed'});
shadowRoot.innerHTML = `
  <style>
    /* 隔离的样式 */
    .result-item { /* ... */ }
  </style>
  <div class="result-container">
    ${content}
  </div>
`;
```

### 调试工具

#### 1. Chrome DevTools
- **Elements**: 检查DOM结构和CSS样式
- **Console**: 查看日志和错误信息
- **Network**: 监控网络请求
- **Performance**: 分析性能瓶颈
- **Application**: 检查扩展存储和权限

#### 2. 扩展调试
```javascript
// 在background.js中添加调试信息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Received message:', message);
  console.log('From tab:', sender.tab?.id);
  
  // 处理消息
  handleMessage(message).then(response => {
    console.log('Sending response:', response);
    sendResponse(response);
  });
  
  return true; // 保持消息通道开放
});
```

#### 3. 自定义调试面板
```javascript
class DebugPanel {
  constructor() {
    this.isVisible = false;
    this.data = new Map();
  }
  
  show() {
    if (this.isVisible) return;
    
    const panel = document.createElement('div');
    panel.id = 'extractor-debug-panel';
    panel.innerHTML = this.generateHTML();
    
    document.body.appendChild(panel);
    this.isVisible = true;
  }
  
  addData(key, value) {
    this.data.set(key, value);
    this.updateDisplay();
  }
}
```

---

## 📚 API参考

### BaseExtractor API

#### 构造函数
```javascript
constructor(moduleId, name, description)
```
- `moduleId`: 唯一标识符
- `name`: 显示名称
- `description`: 功能描述

#### 核心方法
```javascript
// 必须实现的方法
async extract()                    // 主要提取逻辑
getSelectors()                     // 获取CSS选择器配置
validate(data)                     // 验证提取的数据
format(data)                       // 格式化输出数据

// 可选重写的方法
calculateConfidence(data)          // 计算置信度
enhanceData(data)                  // 数据增强处理
handleError(error)                 // 错误处理
```

### ResultFormatter API

#### 主要方法
```javascript
formatResultItem(extractorId, result, pageType)  // 格式化结果项
formatTitleResult(data, config)                  // 格式化标题
formatPriceResult(data, config, pageType)        // 格式化价格
formatSpecsResult(data, config)                  // 格式化规格
formatRatingResult(data, config)                 // 格式化评价
```

#### 配置选项
```javascript
{
  priceFormat: string,        // 价格格式类型
  showBatchInfo: boolean,     // 是否显示批量信息
  keywordLimit: number,       // 关键词显示限制
  priceLabel: string,         // 价格标签文本
  specialFields: string[]     // 特殊字段列表
}
```

### UIManager API

#### 主要方法
```javascript
showProgressToast(message, icon)     // 显示进度提示
showResultsBoard(results)            // 显示结果面板
showErrorToast(message, title)       // 显示错误提示
showSuccessToast(message, title)     // 显示成功提示
hideAllUI()                          // 隐藏所有UI
cleanup()                            // 清理资源
```

---

## 🎯 最佳实践总结

### 代码质量
1. **遵循ESLint规则**: 保持代码风格一致
2. **添加JSDoc注释**: 为所有公共方法添加文档
3. **单元测试**: 为核心功能编写测试
4. **错误处理**: 完善的异常处理机制

### 性能优化
1. **避免频繁DOM操作**: 批量处理DOM更新
2. **使用事件委托**: 减少事件监听器数量
3. **延迟加载**: 非关键功能延迟初始化
4. **内存管理**: 及时清理不需要的资源

### 用户体验
1. **响应式设计**: 适配不同屏幕尺寸
2. **加载状态**: 提供清晰的加载反馈
3. **错误提示**: 友好的错误信息和解决建议
4. **快捷操作**: 提供键盘快捷键支持

### 安全性
1. **输入验证**: 验证所有外部输入
2. **权限最小化**: 只请求必要的权限
3. **数据加密**: 敏感数据加密存储
4. **CSP策略**: 实施内容安全策略

---

*开发指南版本: v3.1.0*
*更新时间: 2025年1月*
*维护团队: 1688商品信息提取器开发团队*