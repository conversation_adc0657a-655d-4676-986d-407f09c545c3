/**
 * UI管理器 - 统一管理所有用户界面元素
 * 负责状态指示器、进度提示、结果公告板等UI组件
 * <AUTHOR>
 * @version 1.0.0
 */

class UIManager {
  constructor(loggerManager) {
    this.logger = loggerManager;
    this.statusIndicator = null;
    this.resultsBoard = null;
    this.progressToast = null;
    this.debugPanel = null;
    this.resultFormatter = null;

    this.config = {
      autoHideDelay: 3000,
      statusIndicatorDelay: 5000,
      animationDuration: 300
    };

    this.init();
  }

  /**
   * 初始化UI管理器
   */
  init() {
    this.logger.info('UIManager', 'Initializing UI Manager');

    try {
      // 初始化结果格式化器
      this.initResultFormatter();

      // 创建基础UI组件
      this.createStatusIndicator();

      // 初始化调试面板
      this.initDebugPanel();

      this.logger.info('UIManager', 'UI Manager initialized successfully');
    } catch (error) {
      this.logger.error('UIManager', 'Failed to initialize UI Manager', error);
    }
  }

  /**
   * 初始化结果格式化器
   */
  initResultFormatter() {
    try {
      if (window.ResultFormatter) {
        this.resultFormatter = new window.ResultFormatter();
        this.logger.info('UIManager', 'Result formatter initialized successfully');
      } else {
        this.logger.warn('UIManager', 'ResultFormatter not available, using fallback');
      }
    } catch (error) {
      this.logger.error('UIManager', 'Failed to initialize result formatter', error);
      this.resultFormatter = null;
    }
  }







  /**
   * 检测页面类型
   * @returns {string} 页面类型
   */
  detectPageType() {
    try {
      if (window.urlDetector) {
        const pageInfo = window.urlDetector.detectPageType();
        return pageInfo.pageType || 'unknown';
      }

      // 备用检测方法
      const url = window.location.href;
      if (url.includes('sk=consign') || url.includes('代发')) {
        return 'consign';
      } else {
        return 'wholesale';
      }
    } catch (error) {
      this.logger.error('UIManager', 'Failed to detect page type', error);
      return 'unknown';
    }
  }

  /**
   * 初始化调试面板
   */
  initDebugPanel() {
    try {
      if (window.DebugPanel) {
        this.debugPanel = new window.DebugPanel(this.logger);
        this.logger.info('UIManager', 'Debug panel initialized successfully');

        // 将调试面板实例设置为全局可访问
        window.debugPanel = this.debugPanel;
      } else {
        this.logger.warn('UIManager', 'DebugPanel not available');
      }
    } catch (error) {
      this.logger.error('UIManager', 'Failed to initialize debug panel', error);
    }
  }

  /**
   * 创建状态指示器
   */
  createStatusIndicator() {
    try {
      // 检查是否已存在
      if (document.querySelector('.extractor-status-indicator')) {
        this.logger.warn('UIManager', 'Status indicator already exists');
        return;
      }

      // 创建状态指示器元素
      const indicator = document.createElement('div');
      indicator.className = 'extractor-status-indicator';
      indicator.innerHTML = `
        <div class="status-header">
          <div class="status-icon ready"></div>
          <span>1688提取器</span>
        </div>
        <div class="status-text">就绪 - 点击插件图标开始提取</div>
      `;

      // 添加到页面
      document.body.appendChild(indicator);

      // 存储引用
      this.statusIndicator = indicator;

      // 3秒后自动隐藏
      setTimeout(() => {
        if (indicator && !this.isExtracting) {
          indicator.classList.add('hidden');
        }
      }, this.config.autoHideDelay);

      this.logger.info('UIManager', 'Status indicator created successfully');
    } catch (error) {
      this.logger.error('UIManager', 'Failed to create status indicator', error);
    }
  }

  /**
   * 更新状态指示器
   * @param {string} status - 状态 (ready, extracting, completed, failed)
   * @param {string} message - 状态消息
   */
  updateStatusIndicator(status, message) {
    try {
      if (!this.statusIndicator) {
        this.logger.warn('UIManager', 'Status indicator not found, creating new one');
        this.createStatusIndicator();
        return;
      }

      const icon = this.statusIndicator.querySelector('.status-icon');
      const text = this.statusIndicator.querySelector('.status-text');

      if (icon) {
        // 清除所有状态类
        icon.className = 'status-icon';
        // 添加新状态类
        icon.classList.add(status);
      }

      if (text && message) {
        text.textContent = message;
      }

      // 显示指示器
      this.statusIndicator.classList.remove('hidden');

      // 如果不是提取中状态，延时后自动隐藏
      if (status !== 'extracting') {
        setTimeout(() => {
          if (this.statusIndicator && !this.isExtracting) {
            this.statusIndicator.classList.add('hidden');
          }
        }, this.config.statusIndicatorDelay);
      }

      this.logger.debug('UIManager', `Status indicator updated: ${status} - ${message}`);
    } catch (error) {
      this.logger.error('UIManager', 'Failed to update status indicator', error);
    }
  }

  /**
   * 显示进度提示
   * @param {string} message - 提示消息
   * @param {string} icon - 图标 (默认为⚡)
   */
  showProgressToast(message, icon = '⚡') {
    try {
      // 检查是否已存在
      let toast = document.querySelector('.extractor-progress-toast');

      if (!toast) {
        toast = document.createElement('div');
        toast.className = 'extractor-progress-toast';
        document.body.appendChild(toast);
        this.progressToast = toast;
      }

      toast.innerHTML = `
        <span class="toast-icon">${icon}</span>
        <span class="toast-text">${message}</span>
      `;

      // 显示
      toast.classList.remove('hidden');

      // 添加显示动画
      setTimeout(() => {
        toast.classList.add('show');
      }, 100);

      // 3秒后隐藏
      setTimeout(() => {
        toast.classList.add('hidden');
      }, this.config.autoHideDelay);

      this.logger.debug('UIManager', `Progress toast shown: ${message}`);
    } catch (error) {
      this.logger.error('UIManager', 'Failed to show progress toast', error);
    }
  }

  /**
   * 显示结果公告板
   * @param {Map} results - 提取结果
   */
  showResultsBoard(results) {
    try {
      // showResultsBoard被调用
      this.logger.info('UIManager', 'Showing results board');

      // 移除现有的结果公告板
      const existingBoard = document.querySelector('.extractor-results-board');
      if (existingBoard) {
        existingBoard.remove();
        this.logger.info('UIManager', 'Removed existing results board');
      }

      // 创建公告板元素
      const board = document.createElement('div');
      board.className = 'extractor-results-board';

      // 构建结果内容
      let resultsHtml = `
        <div class="board-header">
          <div class="board-title">
            <span class="board-icon">📋</span>
            <h3>1688商品信息提取结果</h3>
          </div>
          <div class="board-controls">
            <button class="board-minimize" title="最小化">−</button>
            <button class="board-close" title="关闭">✕</button>
          </div>
        </div>
        <div class="board-content">
      `;

      // 添加提取结果
      if (results && results.size > 0) {
        for (const [extractorId, result] of results) {
          if (result.success && result.data) {
            resultsHtml += this.formatResultItem(extractorId, result);
          }
        }
      } else {
        resultsHtml += '<div class="no-results">暂无提取结果</div>';
      }

      resultsHtml += `
        </div>
        <div class="board-footer">
          <div class="board-stats">
            <span>共提取 ${results ? results.size : 0} 项信息</span>
            <span>•</span>
            <span>${new Date().toLocaleString()}</span>
          </div>
          <div class="board-actions">
            <button class="board-btn export-btn">导出数据</button>
            <button class="board-btn close-btn">关闭</button>
          </div>
        </div>
      `;

      board.innerHTML = resultsHtml;

      // 添加到页面
      document.body.appendChild(board);

      // 存储引用
      this.resultsBoard = board;

      // 绑定事件监听器
      this.bindResultsBoardEvents(board);

      // 添加动画效果
      setTimeout(() => {
        board.classList.add('show');
      }, 100);

      this.logger.info('UIManager', 'Results board displayed successfully');
    } catch (error) {
      this.logger.error('UIManager', 'Failed to show results board', error);
    }
  }

  /**
   * 绑定结果面板事件
   * @param {HTMLElement} board - 结果面板元素
   */
  bindResultsBoardEvents(board) {
    if (!board) return;

    // 绑定最小化按钮
    const minimizeBtn = board.querySelector('.board-minimize');
    if (minimizeBtn) {
      minimizeBtn.addEventListener('click', () => this.toggleResultsBoardMinimize(board));
    }

    // 绑定关闭按钮
    const closeBtns = board.querySelectorAll('.board-close, .close-btn');
    closeBtns.forEach(btn => {
      btn.addEventListener('click', () => this.closeResultsBoard(board));
    });

    // 绑定导出按钮
    const exportBtn = board.querySelector('.export-btn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportResults());
    }
  }

  /**
   * 切换结果面板最小化状态
   * @param {HTMLElement} board - 结果面板元素
   */
  toggleResultsBoardMinimize(board) {
    if (!board) return;

    const isMinimized = board.classList.contains('minimized');
    const minimizeBtn = board.querySelector('.board-minimize');

    if (isMinimized) {
      board.classList.remove('minimized');
      if (minimizeBtn) {
        minimizeBtn.innerHTML = '−';
        minimizeBtn.title = '最小化';
      }
    } else {
      board.classList.add('minimized');
      if (minimizeBtn) {
        minimizeBtn.innerHTML = '□';
        minimizeBtn.title = '还原';
      }
    }
  }

  /**
   * 关闭结果面板
   * @param {HTMLElement} board - 结果面板元素
   */
  closeResultsBoard(board) {
    if (board) {
      board.remove();
      this.resultsBoard = null;
    }
  }

  /**
   * 格式化结果项
   * @param {string} extractorId - 抽取器ID
   * @param {Object} result - 结果数据
   * @returns {string} 格式化的HTML
   */
  formatResultItem(extractorId, result) {
    try {
      // 使用统一的结果格式化器
      if (this.resultFormatter) {
        const pageType = this.detectPageType();
        return this.resultFormatter.formatResultItem(extractorId, result, pageType);
      }

      // 降级到基础实现
      // 安全检查输入参数
      if (!result || !result.data) {
        return `
          <div class="result-item error">
            <div class="result-header">
              <div class="result-name">${extractorId || '未知提取器'}</div>
              <div class="result-confidence low">0%</div>
            </div>
            <div class="result-content">
              <div class="result-value">数据格式错误</div>
            </div>
          </div>
        `;
      }

      const name = result.name || extractorId || '未知提取器';
      const confidence = result.data.confidence || 0;
      const confidenceClass = confidence >= 80 ? 'high' : confidence >= 60 ? 'medium' : 'low';

      let content = '';
      let pageType = '';

      // 安全检测页面类型
      try {
        const url = window.location.href;
        if (url.includes('sk=consign') || url.includes('代发')) {
          pageType = '代发模式';
        } else {
          pageType = '批发模式';
        }
      } catch (error) {
        this.logger.warn('UIManager', 'Failed to detect page type', error);
        pageType = '批发模式'; // 默认为批发模式
      }

      // 简化的安全显示逻辑
      if (result.data.title) {
        // 商品标题 - 智能整理显示
        const rawTitle = String(result.data.title);
        const cleanedTitle = this.cleanAndFormatTitle(rawTitle);
        content = `<div class="result-value">${cleanedTitle.main}</div>`;

        // 显示附加信息
        if (cleanedTitle.merchant) {
          content += `<div class="result-detail">商家: ${cleanedTitle.merchant}</div>`;
        }
        if (cleanedTitle.stats) {
          content += `<div class="result-detail">${cleanedTitle.stats}</div>`;
        }
      } else if (result.data.price || result.data.couponPrice || result.data.priceRange) {
        // 价格信息 - 支持批发和代发模式
        try {
          if (pageType === '批发模式') {
            // 批发模式价格显示
            if (result.data.couponPrice) {
              const couponPrice = typeof result.data.couponPrice === 'object' ?
                (result.data.couponPrice.value || result.data.couponPrice.price || '未知') :
                result.data.couponPrice;
              content = `<div class="result-value">券后价: ¥${couponPrice}</div>`;
            } else if (result.data.priceRange && Array.isArray(result.data.priceRange) && result.data.priceRange.length > 0) {
              const firstPrice = result.data.priceRange[0];
              const lastPrice = result.data.priceRange[result.data.priceRange.length - 1];
              const minPrice = typeof firstPrice === 'object' ? (firstPrice.value || firstPrice.price || firstPrice) : firstPrice;
              const maxPrice = typeof lastPrice === 'object' ? (lastPrice.value || lastPrice.price || lastPrice) : lastPrice;
              content = `<div class="result-value">批发价: ¥${minPrice} ~ ¥${maxPrice}</div>`;
            } else if (result.data.price) {
              const priceValue = typeof result.data.price === 'object' ?
                (result.data.price.value || result.data.price.price || '未知') :
                result.data.price;
              content = `<div class="result-value">价格: ¥${priceValue}</div>`;
            } else {
              content = `<div class="result-value">批发价格信息</div>`;
            }

            // 安全显示起批量信息
            if (result.data.minBatch) {
              try {
                const quantity = typeof result.data.minBatch === 'object' ?
                  (result.data.minBatch.quantity || result.data.minBatch.value || result.data.minBatch) :
                  result.data.minBatch;
                const unit = typeof result.data.minBatch === 'object' ?
                  (result.data.minBatch.unit || '个') : '个';
                content += `<div class="result-detail">${quantity}${unit}起批</div>`;
              } catch (batchError) {
                console.warn('处理起批量信息失败:', batchError);
              }
            }
          } else {
            // 代发模式价格显示
            const priceValue = typeof result.data.price === 'object' ?
              (result.data.price.value || result.data.price.price || '未知') :
              (result.data.price || '未知');
            content = `<div class="result-value">代发价: ¥${priceValue}</div>`;
          }
        } catch (priceError) {
          console.warn('处理价格信息失败:', priceError);
          content = `<div class="result-value">价格信息处理出错</div>`;
        }
      } else if (result.data.rating || result.data.score) {
        // 评价信息 - 仅显示基本评分，避免复杂数据处理
        const rating = result.data.rating || result.data.score;
        content = `<div class="result-value">评分: ${rating}分</div>`;

        // 仅显示基本统计，避免复杂循环和数组操作
        if (result.data.reviewCount) {
          content += `<div class="result-detail">评价数: ${result.data.reviewCount}</div>`;
        }
      } else if (result.data.imageCount || (result.data.images && result.data.images.length)) {
        // 图片信息 - 简化显示
        const count = result.data.imageCount || result.data.images.length;
        content = `<div class="result-value">图片: ${count} 张</div>`;
      } else if (result.data.attributes || result.data.specs || result.data.specifications) {
        // 商品属性信息
        const attributes = result.data.attributes || result.data.specs || result.data.specifications || {};
        const attrCount = Object.keys(attributes).length;

        if (attrCount > 0) {
          content = `<div class="result-value">商品属性: ${attrCount} 项</div>`;

          // 完整显示所有属性
          const allAttrs = [];
          for (const [key, value] of Object.entries(attributes)) {
            allAttrs.push(`${key}: ${String(value)}`);
          }

          // 分组显示，每行最多2个属性
          const lines = [];
          for (let i = 0; i < allAttrs.length; i += 2) {
            const line = allAttrs.slice(i, i + 2).join(' | ');
            lines.push(line);
          }

          // 显示所有属性行
          lines.forEach(line => {
            content += `<div class="result-detail">${line}</div>`;
          });
        } else {
          content = `<div class="result-value">商品属性: 暂无数据</div>`;
        }
        // 移除旧的物流处理逻辑，统一使用结果格式化器处理
      } else if (result.data.companyName || result.data.name) {
        // 商家信息 - 限制长度
        const companyName = String(result.data.companyName || result.data.name).substring(0, 50);
        content = `<div class="result-value">${companyName}</div>`;
      } else {
        // 通用显示 - 简化逻辑，避免与结果格式化器冲突
        content = `<div class="result-value">详细信息</div>`;

        // 显示数据键列表用于调试
        if (result.data && Object.keys(result.data).length > 0) {
          const dataKeys = Object.keys(result.data);
          const displayKeys = dataKeys.slice(0, 3).join(', ');
          content += `<div class="result-detail">包含: ${displayKeys}</div>`;
        }
      }

      return `
        <div class="result-item">
          <div class="result-header">
            <div class="result-name">${name}</div>
            <div class="result-confidence ${confidenceClass}">${confidence}%</div>
          </div>
          <div class="result-content">
            ${content}
          </div>
        </div>
      `;
    } catch (error) {
      this.logger.error('UIManager', 'Failed to format result item', error);
      return `<div class="result-item error">格式化结果时出错: ${extractorId}</div>`;
    }
  }

  /**
   * 导出结果数据
   */
  exportResults() {
    try {
      // 这个方法需要从主脚本获取结果数据
      if (window.extractorInstance && window.extractorInstance.exportResults) {
        window.extractorInstance.exportResults();
      } else {
        this.logger.error('UIManager', 'Export function not available');
        this.showProgressToast('导出功能暂不可用', '❌');
      }
    } catch (error) {
      this.logger.error('UIManager', 'Failed to export results', error);
      this.showProgressToast('导出失败', '❌');
    }
  }

  /**
   * 显示错误提示
   * @param {string} message - 错误消息
   * @param {string} title - 错误标题
   */
  showErrorToast(message, title = '错误') {
    try {
      // 创建错误提示
      const errorToast = document.createElement('div');
      errorToast.className = 'extractor-error-toast';
      errorToast.innerHTML = `
        <div class="error-icon">❌</div>
        <div class="error-title">${title}</div>
        <div class="error-message">${message}</div>
        <div class="error-actions">
          <button class="error-btn primary" onclick="window.loggerManager.showErrorReport()">查看详情</button>
          <button class="error-btn secondary" onclick="this.parentElement.parentElement.remove()">关闭</button>
        </div>
      `;

      document.body.appendChild(errorToast);

      // 10秒后自动移除
      setTimeout(() => {
        if (errorToast.parentNode) {
          errorToast.parentNode.removeChild(errorToast);
        }
      }, 10000);

      this.logger.debug('UIManager', `Error toast shown: ${title} - ${message}`);
    } catch (error) {
      this.logger.error('UIManager', 'Failed to show error toast', error);
    }
  }

  /**
   * 显示成功提示
   * @param {string} message - 成功消息
   * @param {string} title - 成功标题
   */
  showSuccessToast(message, title = '成功') {
    try {
      this.showProgressToast(`${title}: ${message}`, '✅');
      this.logger.debug('UIManager', `Success toast shown: ${title} - ${message}`);
    } catch (error) {
      this.logger.error('UIManager', 'Failed to show success toast', error);
    }
  }

  /**
   * 隐藏所有UI元素
   */
  hideAllUI() {
    try {
      if (this.statusIndicator) {
        this.statusIndicator.classList.add('hidden');
      }

      if (this.progressToast) {
        this.progressToast.classList.add('hidden');
      }

      if (this.resultsBoard) {
        this.resultsBoard.remove();
        this.resultsBoard = null;
      }

      this.logger.info('UIManager', 'All UI elements hidden');
    } catch (error) {
      this.logger.error('UIManager', 'Failed to hide UI elements', error);
    }
  }

  /**
   * 清理UI资源
   */
  cleanup() {
    try {
      this.hideAllUI();

      // 移除事件监听器等清理工作
      if (this.statusIndicator && this.statusIndicator.parentNode) {
        this.statusIndicator.parentNode.removeChild(this.statusIndicator);
        this.statusIndicator = null;
      }

      this.logger.info('UIManager', 'UI Manager cleaned up');
    } catch (error) {
      this.logger.error('UIManager', 'Failed to cleanup UI Manager', error);
    }
  }

  /**
   * 清理和格式化商品标题
   * @param {string} rawTitle - 原始标题文本
   * @returns {Object} 格式化后的标题信息
   */
  cleanAndFormatTitle(rawTitle) {
    try {
      // 移除多余的空白字符
      let cleanTitle = rawTitle.replace(/\s+/g, ' ').trim();

      const result = {
        main: '',
        merchant: '',
        stats: ''
      };

      // 提取商家信息（通常在开头）
      const merchantMatch = cleanTitle.match(/^([^\s]+(?:市|县|区)[^\s]*(?:电子商务|贸易|商行|公司|厂|店))[\s]*(.*)/i);
      if (merchantMatch) {
        result.merchant = merchantMatch[1];
        cleanTitle = merchantMatch[2];
      }

      // 提取统计信息（评价、销量等）
      const statsPattern = /(\d+(?:\.\d+)?条?评价|全网销量\d+\+?|\d+个?成交|举报|\d+\+?个成交)/g;
      const statsMatches = cleanTitle.match(statsPattern);
      if (statsMatches) {
        // 过滤和清理统计信息
        const cleanedStats = statsMatches.filter(stat => {
          // 过滤掉单独的"个成交"或不完整的统计信息
          return !stat.match(/^个成交$/) && stat.length > 2;
        }).map(stat => {
          // 标准化统计信息格式
          if (stat.includes('个成交') && !stat.match(/^\d/)) {
            return stat.replace(/^个成交/, '成交');
          }
          return stat;
        });

        result.stats = cleanedStats.join(' | ');
        // 从标题中移除统计信息
        cleanTitle = cleanTitle.replace(statsPattern, '').replace(/\s+/g, ' ').trim();
      }

      // 处理重复的商品名称
      const words = cleanTitle.split(' ');
      const uniqueWords = [];
      const seen = new Set();

      for (const word of words) {
        if (word.length > 2 && !seen.has(word.toLowerCase())) {
          seen.add(word.toLowerCase());
          uniqueWords.push(word);
        } else if (word.length <= 2) {
          uniqueWords.push(word);
        }
      }

      // 限制主标题长度
      result.main = uniqueWords.join(' ').substring(0, 80);
      if (result.main.length < rawTitle.length * 0.3) {
        // 如果清理后太短，使用原标题的前80个字符
        result.main = rawTitle.substring(0, 80);
      }

      return result;

    } catch (error) {
      console.warn('格式化标题失败:', error);
      return {
        main: rawTitle.substring(0, 80),
        merchant: '',
        stats: ''
      };
    }
  }

  /**
   * 设置提取状态
   * @param {boolean} isExtracting - 是否正在提取
   */
  setExtracting(isExtracting) {
    this.isExtracting = isExtracting;
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.UIManager = UIManager;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UIManager;
}