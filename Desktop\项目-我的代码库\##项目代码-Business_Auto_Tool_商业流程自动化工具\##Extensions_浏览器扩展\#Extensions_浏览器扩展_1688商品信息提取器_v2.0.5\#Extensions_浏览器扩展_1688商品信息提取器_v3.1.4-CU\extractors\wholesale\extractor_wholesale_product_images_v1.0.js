/**
 * 批发模式商品图片抽取器
 * 专门处理1688批发页面的商品图片信息提取
 * 目标元素: .od-gallery-img 等图片相关元素
 * <AUTHOR>
 * @version 1.0.0
 */

class WholesaleProductImagesExtractor extends BaseExtractor {
  /**
   * 构造函数
   */
  constructor() {
    super(
      '1688_wholesale_product_images_001',
      '批发商品图片',
      '提取1688批发页面的图片轮播、缩略图等图片信息'
    );
    
    // 抽取器配置
    this.config = {
      timeout: 10000, // 10秒超时
      retryDelay: 2000, // 重试延迟2秒
      maxImages: 50, // 最大图片数量
      minImageSize: 50, // 最小图片尺寸(像素)
      supportedFormats: ['.jpg', '.jpeg', '.png', '.webp', '.gif'], // 支持的图片格式
      excludePatterns: ['icon', 'logo', 'button', 'arrow'] // 排除的图片模式
    };
  }

  /**
   * 获取CSS选择器配置
   * @returns {Object} 选择器配置
   */
  getSelectors() {
    return {
      // 批发模式专用选择器
      primary: [
        '.od-gallery-img', // 批发模式图片轮播
        '.od-gallery-turn .od-gallery-img', // 批发模式缩略图
        '.preview-img', // 预览图片
        '.ant-image-img', // Ant Design图片组件
        '.product-image img', // 产品图片
        '.offer-image img', // 商品图片
        '.item-image img', // 项目图片
        '.gallery img', // 图片库
        '.carousel img', // 轮播图
        '.slider img' // 滑动图片
      ],
      
      // 备用选择器
      fallback: [
        'img[src*="cbu01.alicdn.com"]', // 阿里云CDN图片
        'img[src*="img.alicdn.com"]', // 阿里图片CDN
        'img[src*="gw.alicdn.com"]', // 阿里网关图片
        'img[alt*="商品"]', // alt属性包含商品
        'img[alt*="产品"]', // alt属性包含产品
        'img[class*="product"]', // 类名包含product
        'img[class*="item"]', // 类名包含item
        'img[class*="offer"]' // 类名包含offer
      ],
      
      // 上下文选择器 - 用于缩小搜索范围
      context: [
        '.od-gallery-preview', // 批发模式图片预览区
        '.gallery-fix-wrapper', // 图片固定包装器
        '.module-od-picture-gallery', // 图片库模块
        '.od-gallery-turn', // 批发模式图片轮播
        '.product-gallery', // 产品图片库
        '.image-gallery', // 图片库
        '.carousel-container', // 轮播容器
        '.slider-container' // 滑动容器
      ],
      
      // 排除选择器 - 避免误选
      exclude: [
        '.advertisement img',
        '.ad-content img',
        '.popup img',
        '.modal img',
        '.tooltip img',
        '.logo img', // 商标图片
        '.icon img', // 图标
        '.button img', // 按钮图片
        '.arrow img', // 箭头图片
        '.navigation img', // 导航图片
        '.menu img', // 菜单图片
        '.header img', // 头部图片
        '.footer img', // 页脚图片
        '.sidebar img', // 侧边栏图片
        '.video-icon', // 视频图标
        'script',
        'style',
        'noscript'
      ]
    };
  }

  /**
   * 执行数据提取
   * @returns {Promise<Object>} 提取的图片信息
   */
  async extract() {
    console.log('🚀 [图片调试] ========== 开始商品图片提取 ==========');
    console.log('🚀 [图片调试] 提取器ID:', this.moduleId);
    console.log('🚀 [图片调试] 当前页面URL:', window.location.href);
    
    const selectors = this.getSelectors();
    let imageInfo = null;
    
    try {
      // 检测页面类型
      console.log('🔍 [图片调试] 检测页面类型...');
      const pageType = this.detectPageType();
      console.log('✅ [图片调试] 页面类型:', pageType);
      
      // 第一步：尝试主要选择器
       console.log('🔍 [图片调试] 步骤1: 使用主要选择器...');
       console.log('🔍 [图片调试] 主要选择器数量:', selectors.primary.length);
       for (const selector of selectors.primary) {
         try {
           const images = await this.extractImagesWithSelector(selector, 'primary');
           if (images && images.length > 0) {
             imageInfo = this.createImageData(images, selector, 'primary');
             console.log('✅ [图片调试] 主要选择器成功:', selector, '找到图片数量:', images.length);
             break;
           }
         } catch (error) {
           console.log('⚠️ [图片调试] 选择器失败:', selector, error.message);
         }
       }
      
      if (!imageInfo || imageInfo.images.length === 0) {
        console.log('⚠️ [图片调试] 主要选择器未找到图片，尝试其他方法...');
        // 第二步：尝试在上下文中查找
        imageInfo = await this.extractWithContextSelectors(selectors.context, selectors.primary);
      }
      
      if (!imageInfo || imageInfo.images.length === 0) {
        // 第三步：尝试备用选择器
        imageInfo = await this.extractWithFallbackSelectors(selectors.fallback);
      }
      
      if (!imageInfo || imageInfo.images.length === 0) {
        // 第四步：智能搜索图片元素
        imageInfo = await this.intelligentImageSearch();
      }
      
      if (!imageInfo || imageInfo.images.length === 0) {
        throw new Error('未找到商品图片元素');
      }
      
      // 数据清理和增强
      return this.enhanceImageInfo(imageInfo);
      
    } catch (error) {
      console.error('商品图片提取失败:', error);
      throw error;
    }
  }

  /**
   * 检测页面类型
   * @returns {string} 页面类型
   */
  detectPageType() {
    const url = window.location.href;
    if (url.includes('sk=consign')) {
      return 'consign';
    }
    return 'wholesale';
  }



  /**
   * 在上下文中查找图片
   * @param {Array} contextSelectors - 上下文选择器
   * @param {Object} targetSelectors - 目标选择器
   * @returns {Promise<Object|null>} 图片信息
   */
  async extractWithContextSelectors(contextSelectors, targetSelectors) {
    for (const contextSelector of contextSelectors) {
      const contextElement = this.safeQuerySelector(contextSelector);
      if (!contextElement) continue;
      
      // 尝试所有类型的选择器
      const allSelectors = [
        ...targetSelectors.wholesale,
        ...targetSelectors.consign,
        ...targetSelectors.common
      ];
      
      for (const targetSelector of allSelectors) {
        const images = await this.extractImagesWithSelector(targetSelector, 'context', contextElement);
        if (images.length > 0) {
          return this.createImageData(images, `${contextSelector} ${targetSelector}`, 'context');
        }
      }
    }
    
    return null;
  }

  /**
   * 使用备用选择器提取
   * @param {Array} selectors - 备用选择器列表
   * @returns {Promise<Object|null>} 图片信息
   */
  async extractWithFallbackSelectors(selectors) {
    for (const selector of selectors) {
      const images = await this.extractImagesWithSelector(selector, 'fallback');
      if (images.length > 0) {
        return this.createImageData(images, selector, 'fallback');
      }
    }
    
    return null;
  }

  /**
   * 使用选择器提取图片
   * @param {string} selector - CSS选择器
   * @param {string} method - 提取方法
   * @param {Element} context - 上下文元素
   * @returns {Promise<Array>} 图片列表
   */
  async extractImagesWithSelector(selector, method, context = document) {
    const images = [];
    const elements = this.safeQuerySelectorAll(selector, context);
    
    for (const element of elements) {
      if (this.isValidImageElement(element)) {
        const imageData = await this.extractImageData(element, selector, method);
        if (imageData) {
          images.push(imageData);
          
          // 限制图片数量
          if (images.length >= this.config.maxImages) {
            break;
          }
        }
      }
    }
    
    return images;
  }

  /**
   * 智能搜索图片元素
   * @returns {Promise<Object|null>} 图片信息
   */
  async intelligentImageSearch() {
    const candidates = [];
    
    // 搜索所有img元素
    const allImages = this.safeQuerySelectorAll('img');
    
    for (const img of allImages) {
      if (this.isValidImageElement(img)) {
        const score = this.calculateImageScore(img);
        if (score > 0) {
          candidates.push({ element: img, score });
        }
      }
    }
    
    // 按分数排序
    candidates.sort((a, b) => b.score - a.score);
    
    // 取前N个高分图片
    const topCandidates = candidates.slice(0, Math.min(this.config.maxImages, candidates.length));
    
    if (topCandidates.length > 0) {
      const images = [];
      for (const candidate of topCandidates) {
        const imageData = await this.extractImageData(candidate.element, 'intelligent', 'intelligent');
        if (imageData) {
          imageData.score = candidate.score;
          images.push(imageData);
        }
      }
      
      if (images.length > 0) {
        return this.createImageData(images, 'intelligent search', 'intelligent');
      }
    }
    
    return null;
  }

  /**
   * 提取单个图片数据
   * @param {Element} element - 图片元素
   * @param {string} selector - 选择器
   * @param {string} method - 提取方法
   * @returns {Promise<Object|null>} 图片数据
   */
  async extractImageData(element, selector, method) {
    const src = element.src || element.getAttribute('src');
    if (!src) return null;
    
    // 获取图片尺寸
    const dimensions = await this.getImageDimensions(element);
    
    // 检查图片尺寸
    if (dimensions.width < this.config.minImageSize || dimensions.height < this.config.minImageSize) {
      return null;
    }
    
    return {
      src: src,
      alt: element.alt || '',
      title: element.title || '',
      width: dimensions.width,
      height: dimensions.height,
      aspectRatio: dimensions.width / dimensions.height,
      fileSize: await this.estimateFileSize(src),
      format: this.getImageFormat(src),
      element: {
        tagName: element.tagName.toLowerCase(),
        className: element.className,
        id: element.id,
        selector: selector
      },
      extraction: {
        method: method,
        selector: selector,
        timestamp: Date.now()
      },
      metadata: {
        isLazyLoaded: this.isLazyLoadedImage(element),
        hasDataSrc: !!element.getAttribute('data-src'),
        position: this.getElementPosition(element),
        zIndex: this.getElementZIndex(element)
      }
    };
  }

  /**
   * 创建图片数据对象
   * @param {Array} images - 图片列表
   * @param {string} selector - 选择器
   * @param {string} method - 提取方法
   * @returns {Object} 图片数据
   */
  createImageData(images, selector, method) {
    // 按尺寸和位置排序
    const sortedImages = images.sort((a, b) => {
      // 优先按面积排序（大图优先）
      const areaA = a.width * a.height;
      const areaB = b.width * b.height;
      if (areaA !== areaB) {
        return areaB - areaA;
      }
      
      // 其次按位置排序（上方优先）
      return a.metadata.position.top - b.metadata.position.top;
    });
    
    return {
      images: sortedImages,
      imageCount: sortedImages.length,
      mainImage: sortedImages[0] || null,
      thumbnails: sortedImages.slice(1) || [],
      formats: this.getUniqueFormats(sortedImages),
      totalSize: sortedImages.reduce((sum, img) => sum + (img.fileSize || 0), 0),
      averageSize: sortedImages.length > 0 ? 
        sortedImages.reduce((sum, img) => sum + (img.fileSize || 0), 0) / sortedImages.length : 0,
      dimensions: {
        maxWidth: Math.max(...sortedImages.map(img => img.width)),
        maxHeight: Math.max(...sortedImages.map(img => img.height)),
        minWidth: Math.min(...sortedImages.map(img => img.width)),
        minHeight: Math.min(...sortedImages.map(img => img.height))
      },
      extraction: {
        method: method,
        selector: selector,
        timestamp: Date.now()
      }
    };
  }

  /**
   * 验证是否为有效的图片元素
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否有效
   */
  isValidImageElement(element) {
    if (!element || element.tagName.toLowerCase() !== 'img') {
      return false;
    }
    
    // 检查是否在排除列表中
    const excludeSelectors = this.getSelectors().exclude;
    for (const excludeSelector of excludeSelectors) {
      if (element.matches && element.matches(excludeSelector)) {
        return false;
      }
      if (element.closest && element.closest(excludeSelector)) {
        return false;
      }
    }
    
    // 检查图片源
    const src = element.src || element.getAttribute('src') || element.getAttribute('data-src');
    if (!src) return false;
    
    // 检查是否为支持的图片格式
    const format = this.getImageFormat(src);
    if (!this.config.supportedFormats.includes(format)) {
      return false;
    }
    
    // 检查是否包含排除模式
    const srcLower = src.toLowerCase();
    if (this.config.excludePatterns.some(pattern => srcLower.includes(pattern))) {
      return false;
    }
    
    // 检查alt属性是否包含排除关键词
    const alt = element.alt ? element.alt.toLowerCase() : '';
    const excludeKeywords = ['icon', 'logo', 'button', 'arrow', '图标', '按钮', '箭头'];
    if (excludeKeywords.some(keyword => alt.includes(keyword))) {
      return false;
    }
    
    return true;
  }

  /**
   * 计算图片元素分数
   * @param {Element} element - DOM元素
   * @returns {number} 分数
   */
  calculateImageScore(element) {
    let score = 0;
    const src = element.src || element.getAttribute('src');
    
    // 基础分数
    score += 10;
    
    // 类名加分
    const className = element.className.toLowerCase();
    if (className.includes('gallery')) score += 30;
    if (className.includes('product')) score += 25;
    if (className.includes('item')) score += 20;
    if (className.includes('offer')) score += 20;
    if (className.includes('main')) score += 15;
    if (className.includes('preview')) score += 15;
    
    // CDN域名加分
    if (src.includes('cbu01.alicdn.com')) score += 25;
    if (src.includes('img.alicdn.com')) score += 20;
    if (src.includes('gw.alicdn.com')) score += 15;
    
    // 图片尺寸加分（通过URL推测）
    if (src.includes('_b.jpg') || src.includes('_big')) score += 20; // 大图
    if (src.includes('_s.jpg') || src.includes('_small')) score += 10; // 小图
    
    // alt属性加分
    const alt = element.alt ? element.alt.toLowerCase() : '';
    if (alt.includes('商品') || alt.includes('产品')) score += 15;
    if (alt.includes('主图') || alt.includes('详情')) score += 10;
    
    // 位置加分
    const rect = element.getBoundingClientRect();
    if (rect.top < window.innerHeight * 0.8) {
      score += 10;
    }
    
    // 尺寸加分
    if (rect.width >= 200 && rect.height >= 200) {
      score += 20;
    } else if (rect.width >= 100 && rect.height >= 100) {
      score += 10;
    }
    
    // 可见性检查
    if (rect.width > 0 && rect.height > 0) {
      score += 5;
    }
    
    // 减分项
    if (className.includes('icon')) score -= 20;
    if (className.includes('logo')) score -= 15;
    if (className.includes('button')) score -= 10;
    if (alt.includes('icon') || alt.includes('logo')) score -= 15;
    
    return Math.max(0, score);
  }

  /**
   * 获取图片尺寸
   * @param {Element} element - 图片元素
   * @returns {Promise<Object>} 尺寸信息
   */
  async getImageDimensions(element) {
    return new Promise((resolve) => {
      if (element.naturalWidth && element.naturalHeight) {
        resolve({
          width: element.naturalWidth,
          height: element.naturalHeight
        });
      } else if (element.width && element.height) {
        resolve({
          width: element.width,
          height: element.height
        });
      } else {
        // 等待图片加载
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth || img.width,
            height: img.naturalHeight || img.height
          });
        };
        img.onerror = () => {
          resolve({ width: 0, height: 0 });
        };
        img.src = element.src || element.getAttribute('src');
        
        // 超时处理
        setTimeout(() => {
          resolve({ width: 0, height: 0 });
        }, 3000);
      }
    });
  }

  /**
   * 估算文件大小
   * @param {string} src - 图片源地址
   * @returns {Promise<number>} 文件大小（字节）
   */
  async estimateFileSize(src) {
    try {
      const response = await fetch(src, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      return contentLength ? parseInt(contentLength) : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取图片格式
   * @param {string} src - 图片源地址
   * @returns {string} 图片格式
   */
  getImageFormat(src) {
    const url = src.toLowerCase();
    if (url.includes('.jpg') || url.includes('.jpeg')) return '.jpg';
    if (url.includes('.png')) return '.png';
    if (url.includes('.webp')) return '.webp';
    if (url.includes('.gif')) return '.gif';
    return '.jpg'; // 默认格式
  }

  /**
   * 检查是否为懒加载图片
   * @param {Element} element - 图片元素
   * @returns {boolean} 是否懒加载
   */
  isLazyLoadedImage(element) {
    return !!(element.getAttribute('data-src') || 
             element.getAttribute('data-lazy') ||
             element.className.includes('lazy'));
  }

  /**
   * 获取元素位置信息
   * @param {Element} element - DOM元素
   * @returns {Object} 位置信息
   */
  getElementPosition(element) {
    try {
      const rect = element.getBoundingClientRect();
      return {
        top: Math.round(rect.top),
        left: Math.round(rect.left),
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        visible: rect.width > 0 && rect.height > 0,
        inViewport: rect.top >= 0 && rect.top <= window.innerHeight
      };
    } catch (error) {
      return { visible: false, inViewport: false };
    }
  }

  /**
   * 获取元素z-index
   * @param {Element} element - DOM元素
   * @returns {number} z-index值
   */
  getElementZIndex(element) {
    try {
      const computedStyle = window.getComputedStyle(element);
      const zIndex = computedStyle.zIndex;
      return zIndex === 'auto' ? 0 : parseInt(zIndex) || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取唯一格式列表
   * @param {Array} images - 图片列表
   * @returns {Array} 格式列表
   */
  getUniqueFormats(images) {
    const formats = images.map(img => img.format);
    return [...new Set(formats)];
  }

  /**
   * 增强图片信息
   * @param {Object} imageInfo - 原始图片信息
   * @returns {Object} 增强后的图片信息
   */
  enhanceImageInfo(imageInfo) {
    const enhanced = { ...imageInfo };
    
    // 图片分析
    enhanced.analysis = this.analyzeImageFeatures(imageInfo);
    
    // 图片分类
    enhanced.category = this.categorizeImageTypes(imageInfo);
    
    // 计算可信度
    enhanced.confidence = this.calculateImageConfidence(imageInfo);
    
    // 质量评估
    enhanced.quality = this.assessImageQuality(imageInfo);
    
    return enhanced;
  }

  /**
   * 分析图片特征
   * @param {Object} imageInfo - 图片信息
   * @returns {Object} 分析结果
   */
  analyzeImageFeatures(imageInfo) {
    const analysis = {
      hasMainImage: !!imageInfo.mainImage,
      hasThumbnails: imageInfo.thumbnails.length > 0,
      imageCount: imageInfo.imageCount,
      formatVariety: imageInfo.formats.length,
      sizeRange: {
        width: imageInfo.dimensions.maxWidth - imageInfo.dimensions.minWidth,
        height: imageInfo.dimensions.maxHeight - imageInfo.dimensions.minHeight
      },
      averageAspectRatio: imageInfo.images.reduce((sum, img) => sum + img.aspectRatio, 0) / imageInfo.images.length
    };
    
    return analysis;
  }

  /**
   * 图片分类
   * @param {Object} imageInfo - 图片信息
   * @returns {Object} 分类结果
   */
  categorizeImageTypes(imageInfo) {
    const categories = {
      main: [],
      thumbnails: [],
      details: [],
      lifestyle: []
    };
    
    imageInfo.images.forEach((img, index) => {
      if (index === 0) {
        categories.main.push(img);
      } else if (img.width < 200 || img.height < 200) {
        categories.thumbnails.push(img);
      } else if (img.aspectRatio > 2 || img.aspectRatio < 0.5) {
        categories.details.push(img);
      } else {
        categories.lifestyle.push(img);
      }
    });
    
    return categories;
  }

  /**
   * 评估图片质量
   * @param {Object} imageInfo - 图片信息
   * @returns {Object} 质量评估
   */
  assessImageQuality(imageInfo) {
    const quality = {
      overall: 'unknown',
      resolution: 'unknown',
      variety: 'unknown',
      completeness: 'unknown'
    };
    
    // 分辨率评估
    const maxArea = imageInfo.dimensions.maxWidth * imageInfo.dimensions.maxHeight;
    if (maxArea >= 800 * 800) {
      quality.resolution = 'high';
    } else if (maxArea >= 400 * 400) {
      quality.resolution = 'medium';
    } else {
      quality.resolution = 'low';
    }
    
    // 多样性评估
    if (imageInfo.imageCount >= 10) {
      quality.variety = 'high';
    } else if (imageInfo.imageCount >= 5) {
      quality.variety = 'medium';
    } else {
      quality.variety = 'low';
    }
    
    // 完整性评估
    if (imageInfo.mainImage && imageInfo.thumbnails.length >= 3) {
      quality.completeness = 'high';
    } else if (imageInfo.mainImage && imageInfo.thumbnails.length >= 1) {
      quality.completeness = 'medium';
    } else {
      quality.completeness = 'low';
    }
    
    // 综合评估
    const scores = [quality.resolution, quality.variety, quality.completeness];
    const highCount = scores.filter(s => s === 'high').length;
    const mediumCount = scores.filter(s => s === 'medium').length;
    
    if (highCount >= 2) {
      quality.overall = 'high';
    } else if (highCount + mediumCount >= 2) {
      quality.overall = 'medium';
    } else {
      quality.overall = 'low';
    }
    
    return quality;
  }

  /**
   * 计算图片可信度
   * @param {Object} imageInfo - 图片信息
   * @returns {number} 可信度 (0-100)
   */
  calculateImageConfidence(imageInfo) {
    let confidence = 0;
    
    // 基础分数
    confidence += 20;
    
    // 提取方法加分
    if (imageInfo.extraction.method === 'primary') {
      confidence += 40;
    } else if (imageInfo.extraction.method === 'context') {
      confidence += 30;
    } else if (imageInfo.extraction.method === 'intelligent') {
      confidence += 25;
    } else {
      confidence += 15;
    }
    
    // 图片数量加分
    if (imageInfo.imageCount >= 5) {
      confidence += 20;
    } else if (imageInfo.imageCount >= 3) {
      confidence += 15;
    } else if (imageInfo.imageCount >= 1) {
      confidence += 10;
    }
    
    // 主图存在加分
    if (imageInfo.mainImage) {
      confidence += 10;
    }
    
    // 图片质量加分
    const maxArea = imageInfo.dimensions.maxWidth * imageInfo.dimensions.maxHeight;
    if (maxArea >= 800 * 800) {
      confidence += 10;
    } else if (maxArea >= 400 * 400) {
      confidence += 5;
    }
    
    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {boolean} 验证结果
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 检查必要字段
    if (!data.images || !Array.isArray(data.images) || data.images.length === 0) {
      return false;
    }
    
    // 检查图片数据
    for (const image of data.images) {
      if (!image.src || typeof image.src !== 'string') {
        return false;
      }
      if (typeof image.width !== 'number' || typeof image.height !== 'number') {
        return false;
      }
      if (image.width < this.config.minImageSize || image.height < this.config.minImageSize) {
        return false;
      }
    }
    
    // 检查可信度
    if (data.confidence && data.confidence < 30) {
      return false;
    }
    
    return true;
  }

  /**
   * 数据格式化
   * @param {Object} data - 原始数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    if (!data) return null;
    
    return {
      ...data,
      images: data.images.map(img => ({
        ...img,
        alt: img.alt.trim(),
        title: img.title.trim()
      })),
      formatted: true,
      formatTime: Date.now()
    };
  }
}

// 注册到全局作用域
if (typeof window !== 'undefined') {
  window.WholesaleProductImagesExtractor = WholesaleProductImagesExtractor;
}

// 如果支持模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WholesaleProductImagesExtractor;
}